import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Hive
    await Hive.initFlutter();

    // Initialize Supabase (with dummy values for testing)
    await Supabase.initialize(
      url: 'https://dummy.supabase.co',
      anonKey: 'dummy-anon-key-for-testing-only',
    );

    runApp(const MedyTrackApp());
  } catch (e) {
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                const Text('Initialization Error'),
                Text('$e'),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class MedyTrackApp extends StatelessWidget {
  const MedyTrackApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => TestBloc(),
      child: MaterialApp(
        title: 'MedyTrack - Group 2 Test',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: const Color(0xFF2D4A8E)),
          useMaterial3: true,
        ),
        home: const MyHomePage(title: 'MedyTrack - Backend & Storage Test'),
      ),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  String _storageTest = 'Not tested';
  String _hiveTest = 'Not tested';
  String _supabaseTest = 'Not tested';

  @override
  void initState() {
    super.initState();
    _testDependencies();
  }

  Future<void> _testDependencies() async {
    // Test Secure Storage
    try {
      await _secureStorage.write(key: 'test', value: 'success');
      final value = await _secureStorage.read(key: 'test');
      setState(() {
        _storageTest = value == 'success' ? '✅ Working' : '❌ Failed';
      });
    } catch (e) {
      setState(() {
        _storageTest = '❌ Error: $e';
      });
    }

    // Test Hive
    try {
      final box = await Hive.openBox('testBox');
      await box.put('test', 'success');
      final value = box.get('test');
      setState(() {
        _hiveTest = value == 'success' ? '✅ Working' : '❌ Failed';
      });
      await box.close();
    } catch (e) {
      setState(() {
        _hiveTest = '❌ Error: $e';
      });
    }

    // Test Supabase
    try {
      final client = Supabase.instance.client;
      setState(() {
        _supabaseTest = '✅ Initialized';
      });
    } catch (e) {
      setState(() {
        _supabaseTest = '❌ Error: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF2D4A8E),
        foregroundColor: Colors.white,
        title: Text(widget.title),
      ),
      body: BlocBuilder<TestBloc, TestState>(
        builder: (context, state) {
          return Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  const Icon(
                    Icons.storage,
                    size: 100,
                    color: Color(0xFF0DCDB7),
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'Group 2: Backend & Storage Test',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2D4A8E),
                    ),
                  ),
                  const SizedBox(height: 30),
                  Card(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Dependency Status:',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          _buildStatusRow(
                            'flutter_bloc',
                            '✅ Working (from Group 1)',
                          ),
                          _buildStatusRow(
                            'equatable',
                            '✅ Working (from Group 1)',
                          ),
                          _buildStatusRow('supabase_flutter', _supabaseTest),
                          _buildStatusRow('hive_flutter', _hiveTest),
                          _buildStatusRow(
                            'flutter_secure_storage',
                            _storageTest,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Counter: ${state.counter}',
                            style: const TextStyle(fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  const Card(
                    margin: EdgeInsets.symmetric(horizontal: 16),
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: Column(
                        children: [
                          Text(
                            '🎉 Group 2 Dependencies Test!',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                          SizedBox(height: 10),
                          Text(
                            'Backend and storage dependencies are being tested.',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontSize: 14),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.read<TestBloc>().add(IncrementEvent()),
        backgroundColor: const Color(0xFF0DCDB7),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildStatusRow(String dependency, String status) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              dependency,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              status,
              style: TextStyle(
                color: status.startsWith('✅') ? Colors.green : Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Test BLoC (same as Group 1)
class TestBloc extends Bloc<TestEvent, TestState> {
  TestBloc() : super(const TestState(counter: 0)) {
    on<IncrementEvent>((event, emit) {
      emit(TestState(counter: state.counter + 1));
    });
  }
}

abstract class TestEvent extends Equatable {
  const TestEvent();

  @override
  List<Object> get props => [];
}

class IncrementEvent extends TestEvent {}

class TestState extends Equatable {
  final int counter;

  const TestState({required this.counter});

  @override
  List<Object> get props => [counter];
}
