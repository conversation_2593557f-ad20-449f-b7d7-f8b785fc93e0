part of 'dashboard_bloc.dart';

abstract class DashboardState extends Equatable {
  const DashboardState();

  @override
  List<Object?> get props => [];
}

class DashboardInitial extends DashboardState {}

class DashboardLoading extends DashboardState {}

class DashboardLoaded extends DashboardState {
  final List<Medicine> medicines;
  final DashboardStats stats;

  const DashboardLoaded(this.medicines, this.stats);

  @override
  List<Object?> get props => [medicines, stats];
}

class DashboardError extends DashboardState {
  final String message;

  const DashboardError(this.message);

  @override
  List<Object?> get props => [message];
}

class DashboardStats extends Equatable {
  final int total;
  final int expired;
  final int expiringSoon;
  final int lowStock;
  final int outOfStock;

  const DashboardStats({
    required this.total,
    required this.expired,
    required this.expiringSoon,
    required this.lowStock,
    required this.outOfStock,
  });

  @override
  List<Object?> get props => [total, expired, expiringSoon, lowStock, outOfStock];
}
