
import { Medicine, Category } from "@/types";

// Determine if a medicine is expired
export const isExpired = (expiryDate: string): boolean => {
  const today = new Date();
  const expiry = new Date(expiryDate);
  return expiry < today;
};

// Determine if a medicine is about to expire (within 30 days)
export const isNearExpiry = (expiryDate: string): boolean => {
  const today = new Date();
  const expiry = new Date(expiryDate);
  const thirtyDaysFromNow = new Date();
  thirtyDaysFromNow.setDate(today.getDate() + 30);
  
  return expiry > today && expiry <= thirtyDaysFromNow;
};

// Format date to a more readable format
export const formatDate = (dateString: string): string => {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return '';
    }

    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    };
    return date.toLocaleDateString('fr-FR', options);
  } catch (error) {
    return '';
  }
};

// Format date for display in MM/YY format
export const formatDateShort = (dateString: string): string => {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return '';
    }

    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = String(date.getFullYear()).slice(-2);
    return `${month}/${year}`;
  } catch (error) {
    return '';
  }
};

// Get icon for medicine category
export const getCategoryIcon = (category: Category): string => {
  switch(category) {
    case "pain":
      return "pill";
    case "cold":
      return "thermometer";
    case "allergy":
      return "flower";
    case "digestion":
      return "apple";
    case "first-aid":
      return "first-aid";
    case "prescription":
      return "stethoscope";
    default:
      return "package";
  }
};

// Get color for medicine category
export const getCategoryColor = (category: Category): string => {
  switch(category) {
    case "pain":
      return "bg-teal/10";
    case "cold":
      return "bg-navy/10";
    case "allergy":
      return "bg-teal/20";
    case "digestion":
      return "bg-navy/20";
    case "first-aid":
      return "bg-teal/15";
    case "prescription":
      return "bg-navy/15";
    default:
      return "bg-muted";
  }
};

// Get text color for medicine category
export const getCategoryTextColor = (category: Category): string => {
  switch(category) {
    case "pain":
      return "text-sky-dark";
    case "cold":
      return "text-sky-dark";
    case "allergy":
      return "text-mint-dark";
    case "digestion":
      return "text-mint-dark";
    case "first-aid":
      return "text-peach-dark";
    case "prescription":
      return "text-peach-dark";
    default:
      return "text-foreground";
  }
};

// Filter medicines by category
export const filterByCategory = (medicines: Medicine[], category: Category | 'all'): Medicine[] => {
  if (category === 'all') {
    return medicines;
  }
  return medicines.filter(med => med.category === category);
};

// Filter medicines by expiry status
export const filterByExpiry = (medicines: Medicine[], filter: 'expired' | 'near-expiry' | 'all'): Medicine[] => {
  if (filter === 'all') {
    return medicines;
  }
  
  return medicines.filter(med => {
    if (filter === 'expired') {
      return isExpired(med.expiryDate);
    } else if (filter === 'near-expiry') {
      return isNearExpiry(med.expiryDate);
    }
    return false;
  });
};

// Filter medicines by location
export const filterByLocation = (medicines: Medicine[], locationId: string | 'all'): Medicine[] => {
  if (locationId === 'all') {
    return medicines;
  }
  return medicines.filter(med => med.location === locationId);
};

// Filter medicines by low quantity
export const filterByLowQuantity = (medicines: Medicine[], threshold: number = 3): Medicine[] => {
  return medicines.filter(med => med.quantity <= threshold && med.quantity > 0);
};

// Search medicines by name
export const searchMedicines = (medicines: Medicine[], query: string): Medicine[] => {
  if (!query) {
    return medicines;
  }
  
  const lowerCaseQuery = query.toLowerCase();
  return medicines.filter(med => 
    med.name.toLowerCase().includes(lowerCaseQuery) || 
    med.notes?.toLowerCase().includes(lowerCaseQuery)
  );
};
