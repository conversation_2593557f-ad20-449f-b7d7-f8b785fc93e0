
import { Medicine, Category, Tag } from "@/types";

// Determine if a medicine is expired
export const isExpired = (expiryDate: string): boolean => {
  const today = new Date();
  const expiry = new Date(expiryDate);
  return expiry < today;
};

// Determine if a medicine is about to expire (within custom threshold in months)
export const isNearExpiry = (expiryDate: string, thresholdMonths: number = 1): boolean => {
  const today = new Date();
  const expiry = new Date(expiryDate);
  const thresholdDate = new Date();
  thresholdDate.setMonth(today.getMonth() + thresholdMonths);

  return expiry > today && expiry <= thresholdDate;
};

// Legacy function for backward compatibility (uses 30 days)
export const isNearExpiryLegacy = (expiryDate: string): boolean => {
  const today = new Date();
  const expiry = new Date(expiryDate);
  const thirtyDaysFromNow = new Date();
  thirtyDaysFromNow.setDate(today.getDate() + 30);

  return expiry > today && expiry <= thirtyDaysFromNow;
};

// Format date to a more readable format
export const formatDate = (dateString: string): string => {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return '';
    }

    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    };
    return date.toLocaleDateString('fr-FR', options);
  } catch (error) {
    return '';
  }
};

// Format date for display in MM/YY format
export const formatDateShort = (dateString: string): string => {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return '';
    }

    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = String(date.getFullYear()).slice(-2);
    return `${month}/${year}`;
  } catch (error) {
    return '';
  }
};

// Get icon for medicine category
export const getCategoryIcon = (category: Category): string => {
  switch(category) {
    case "pain":
      return "pill";
    case "cold":
      return "thermometer";
    case "allergy":
      return "flower";
    case "digestion":
      return "apple";
    case "first-aid":
      return "first-aid";
    case "prescription":
      return "stethoscope";
    default:
      return "package";
  }
};

// Get color for medicine category
export const getCategoryColor = (category: Category): string => {
  switch(category) {
    case "pain":
      return "bg-teal/10";
    case "cold":
      return "bg-navy/10";
    case "allergy":
      return "bg-teal/20";
    case "digestion":
      return "bg-navy/20";
    case "first-aid":
      return "bg-teal/15";
    case "prescription":
      return "bg-navy/15";
    default:
      return "bg-muted";
  }
};

// Get text color for medicine category
export const getCategoryTextColor = (category: Category): string => {
  switch(category) {
    case "pain":
      return "text-sky-dark";
    case "cold":
      return "text-sky-dark";
    case "allergy":
      return "text-mint-dark";
    case "digestion":
      return "text-mint-dark";
    case "first-aid":
      return "text-peach-dark";
    case "prescription":
      return "text-peach-dark";
    default:
      return "text-foreground";
  }
};

// Filter medicines by category (legacy - for backward compatibility)
export const filterByCategory = (medicines: Medicine[], category: Category | 'all'): Medicine[] => {
  if (category === 'all') {
    return medicines;
  }
  return medicines.filter(med => med.category === category);
};

// Filter medicines by tags
export const filterByTags = (medicines: Medicine[], tagIds: string[]): Medicine[] => {
  if (tagIds.length === 0) {
    return medicines;
  }

  return medicines.filter(med => {
    if (!med.tags || med.tags.length === 0) {
      return false;
    }

    // Check if medicine has any of the selected tags
    return med.tags.some(tag => tagIds.includes(tag.id));
  });
};

// Get all unique tags from a list of medicines
export const getUniqueTags = (medicines: Medicine[]): Tag[] => {
  const tagMap = new Map<string, Tag>();

  medicines.forEach(medicine => {
    if (medicine.tags) {
      medicine.tags.forEach(tag => {
        tagMap.set(tag.id, tag);
      });
    }
  });

  return Array.from(tagMap.values());
};

// Get tag usage statistics
export const getTagUsageStats = (medicines: Medicine[]): { tag: Tag; count: number }[] => {
  const tagCounts = new Map<string, { tag: Tag; count: number }>();

  medicines.forEach(medicine => {
    if (medicine.tags) {
      medicine.tags.forEach(tag => {
        const existing = tagCounts.get(tag.id);
        if (existing) {
          existing.count++;
        } else {
          tagCounts.set(tag.id, { tag, count: 1 });
        }
      });
    }
  });

  return Array.from(tagCounts.values()).sort((a, b) => b.count - a.count);
};

// Get category label in French (for backward compatibility)
export const getCategoryLabel = (category: string): string => {
  const categoryMap: Record<string, string> = {
    'pain': 'Douleur',
    'cold': 'Rhume',
    'allergy': 'Allergie',
    'digestion': 'Digestion',
    'first-aid': 'Premiers soins',
    'prescription': 'Ordonnance',
    'other': 'Autre'
  };
  return categoryMap[category] || category;
};

// Convert category to corresponding tag name
export const categoryToTagName = (category: Category): string => {
  return getCategoryLabel(category);
};

// Check if a medicine has a specific tag
export const medicineHasTag = (medicine: Medicine, tagId: string): boolean => {
  return medicine.tags?.some(tag => tag.id === tagId) || false;
};

// Check if a medicine has any of the specified tags
export const medicineHasAnyTag = (medicine: Medicine, tagIds: string[]): boolean => {
  if (!medicine.tags || tagIds.length === 0) {
    return false;
  }
  return medicine.tags.some(tag => tagIds.includes(tag.id));
};

// Pharmaceutical tag color mapping for consistency
export const getPharmaceuticalTagColor = (tagName: string): string => {
  const therapeuticColors: Record<string, string> = {
    'antibiotique': '#E53E3E',      // Red for antibiotics
    'antalgique': '#3182CE',        // Blue for pain relief
    'anti-inflammatoire': '#D69E2E', // Orange for anti-inflammatory
    'antipyrétique': '#38A169',     // Green for fever reduction
    'antiallergique': '#805AD5',    // Purple for allergies
    'antispasmodique': '#DD6B20',   // Orange-red for antispasmodic
    'corticoïde': '#C53030',        // Dark red for corticoids
    'antifongique': '#2B6CB0',      // Dark blue for antifungal
    'antivirale': '#2C7A7B',        // Teal for antiviral
    'antihypertenseur': '#1A365D',  // Navy for hypertension
    'antidiabétique': '#553C9A',    // Dark purple for diabetes
    'psychotrope': '#744210'        // Brown for psychotropic
  };

  const usageColors: Record<string, string> = {
    'parapharmacie': '#0DCDB7',     // Teal for parapharmacy
    'premiers_soins': '#E53E3E',    // Red for first aid
    'complément_alimentaire': '#38A169', // Green for supplements
    'soins_peau': '#ED8936',        // Orange for skin care
    'soins_yeux': '#3182CE',        // Blue for eye care
    'soins_oreilles': '#805AD5',    // Purple for ear care
    'soins_bouche': '#D69E2E',      // Yellow for oral care
    'digestif': '#48BB78',          // Light green for digestive
    'respiratoire': '#4299E1',      // Light blue for respiratory
    'pédiatrique': '#F56565',       // Light red for pediatric
    'gynécologie': '#ED64A6',       // Pink for gynecology
    'dermatologie': '#F6AD55'       // Light orange for dermatology
  };

  return therapeuticColors[tagName] || usageColors[tagName] || '#0DCDB7';
};

// Get tag category emoji
export const getTagCategoryEmoji = (category: 'therapeutic' | 'usage'): string => {
  return category === 'therapeutic' ? '💊' : '🩺';
};

// Get tag category label
export const getTagCategoryLabel = (category: 'therapeutic' | 'usage'): string => {
  return category === 'therapeutic' ? 'Classes Thérapeutiques' : 'Domaines d\'Usage';
};

// Filter medicines by expiry status with custom threshold
export const filterByExpiry = (
  medicines: Medicine[],
  filter: 'expired' | 'near-expiry' | 'all',
  thresholdMonths: number = 1
): Medicine[] => {
  if (filter === 'all') {
    return medicines;
  }

  return medicines.filter(med => {
    if (filter === 'expired') {
      return isExpired(med.expiryDate);
    } else if (filter === 'near-expiry') {
      return isNearExpiry(med.expiryDate, thresholdMonths);
    }
    return false;
  });
};

// Filter medicines by location
export const filterByLocation = (medicines: Medicine[], locationId: string | 'all'): Medicine[] => {
  if (locationId === 'all') {
    return medicines;
  }
  return medicines.filter(med => med.location === locationId);
};

// Filter medicines by low quantity
export const filterByLowQuantity = (medicines: Medicine[], threshold: number = 3): Medicine[] => {
  return medicines.filter(med => med.quantity <= threshold && med.quantity > 0);
};

// Search medicines by name
export const searchMedicines = (medicines: Medicine[], query: string): Medicine[] => {
  if (!query) {
    return medicines;
  }
  
  const lowerCaseQuery = query.toLowerCase();
  return medicines.filter(med => 
    med.name.toLowerCase().includes(lowerCaseQuery) || 
    med.notes?.toLowerCase().includes(lowerCaseQuery)
  );
};
