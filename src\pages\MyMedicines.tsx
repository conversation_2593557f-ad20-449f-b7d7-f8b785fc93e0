import { useState, useMemo, useCallback, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Plus, Loader2, Search } from "lucide-react";
import { useUserMedicines } from "@/hooks/useUserMedicines";
import { useLocations } from "@/hooks/useLocations";
import { useDebounce } from "@/hooks/useDebounce";
import { Medicine } from "@/types";
import { isExpired, isNearExpiry } from "@/utils/helpers";
import {
  groupMedicines,
  getGroupedMedicinesArray,
  filterGroupedMedicines,
  searchGroupedMedicines,
  generateMedicineGroupKey
} from "@/utils/medicineUtils";
import Layout from "@/components/Layout";
import ModernHeader from "@/components/ModernHeader";
import MedicineCard from "@/components/medicines/MedicineCard";
import GroupedMedicineCard from "@/components/medicines/GroupedMedicineCard";
import MedicineFilters, { FilterState } from "@/components/medicines/MedicineFilters";
import MedicineSorting, { SortOption, ViewMode } from "@/components/medicines/MedicineSorting";
import VirtualizedMedicineList from "@/components/medicines/VirtualizedMedicineList";

const MyMedicines = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { medicines, isLoading } = useUserMedicines();
  const { locations } = useLocations();

  // Filter, sort, and view mode state
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    categories: [],
    locations: [],
    expiryStatus: 'all',
    stockStatus: 'all'
  });
  const [sortBy, setSortBy] = useState<SortOption>('name-asc');
  const [viewMode, setViewMode] = useState<ViewMode>('grouped'); // Default to grouped as per requirements

  // Apply URL filter parameters on component mount
  useEffect(() => {
    const filterParam = searchParams.get('filter');
    if (filterParam) {
      switch (filterParam) {
        case 'expired':
          setFilters(prev => ({ ...prev, expiryStatus: 'expired' }));
          break;
        case 'expiring-soon':
          setFilters(prev => ({ ...prev, expiryStatus: 'expiring' }));
          break;
        case 'low-stock':
          setFilters(prev => ({ ...prev, stockStatus: 'low' }));
          break;
      }
    }
  }, [searchParams]);

  // Debounce search to avoid excessive filtering
  const debouncedSearch = useDebounce(filters.search, 300);

  // Get display name for medicine (consistent with MedicineCard)
  const getMedicineDisplayName = (medicine: Medicine): string => {
    if (medicine.is_custom) {
      return (medicine.custom_name || "Médicament manuel").toLowerCase();
    } else {
      return (medicine.name || "Médicament inconnu").toLowerCase();
    }
  };

  // Filter and sort medicines (individual view)
  const filteredAndSortedMedicines = useMemo(() => {
    let filtered = medicines.filter(medicine => {
      // Search filter
      if (debouncedSearch) {
        const searchTerm = debouncedSearch.toLowerCase();
        const displayName = getMedicineDisplayName(medicine);
        const dosage = (medicine.dosage || "").toLowerCase();
        const notes = (medicine.notes || "").toLowerCase();

        if (!displayName.includes(searchTerm) &&
            !dosage.includes(searchTerm) &&
            !notes.includes(searchTerm)) {
          return false;
        }
      }

      // Category filter
      if (filters.categories.length > 0 && !filters.categories.includes(medicine.category)) {
        return false;
      }

      // Location filter
      if (filters.locations.length > 0 && !filters.locations.includes(medicine.location || '')) {
        return false;
      }

      // Expiry status filter
      if (filters.expiryStatus !== 'all') {
        const expired = medicine.expiryDate ? isExpired(medicine.expiryDate) : false;
        const nearExpiry = medicine.expiryDate ? isNearExpiry(medicine.expiryDate) : false;

        switch (filters.expiryStatus) {
          case 'expired':
            if (!expired) return false;
            break;
          case 'expiring':
            if (!nearExpiry || expired) return false;
            break;
          case 'valid':
            if (expired || nearExpiry) return false;
            break;
        }
      }

      // Stock status filter
      if (filters.stockStatus !== 'all') {
        const outOfStock = medicine.quantity === 0;
        const threshold = medicine.lowStockThreshold || 0;
        const lowStock = medicine.quantity <= threshold && medicine.quantity > 0;

        switch (filters.stockStatus) {
          case 'out':
            if (!outOfStock) return false;
            break;
          case 'low':
            if (!lowStock) return false;
            break;
          case 'in-stock':
            if (outOfStock || lowStock) return false;
            break;
        }
      }

      return true;
    });

    // Sort medicines
    filtered.sort((a, b) => {
      const aName = getMedicineDisplayName(a);
      const bName = getMedicineDisplayName(b);

      switch (sortBy) {
        case 'name-asc':
          return aName.localeCompare(bName);
        case 'name-desc':
          return bName.localeCompare(aName);
        case 'expiry-asc':
          if (!a.expiryDate && !b.expiryDate) return 0;
          if (!a.expiryDate) return 1;
          if (!b.expiryDate) return -1;
          return new Date(a.expiryDate).getTime() - new Date(b.expiryDate).getTime();
        case 'expiry-desc':
          if (!a.expiryDate && !b.expiryDate) return 0;
          if (!a.expiryDate) return 1;
          if (!b.expiryDate) return -1;
          return new Date(b.expiryDate).getTime() - new Date(a.expiryDate).getTime();
        case 'quantity-asc':
          return a.quantity - b.quantity;
        case 'quantity-desc':
          return b.quantity - a.quantity;
        case 'created-desc':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        default:
          return 0;
      }
    });

    return filtered;
  }, [medicines, debouncedSearch, filters, sortBy]);

  // Group medicines and apply filters (grouped view)
  const filteredAndSortedGroupedMedicines = useMemo(() => {
    // First apply individual medicine filters
    const filteredMedicines = filteredAndSortedMedicines;

    // Group the filtered medicines
    const grouped = groupMedicines(filteredMedicines);
    let groupedArray = getGroupedMedicinesArray(grouped,
      sortBy.includes('expiry') ? 'expiry' :
      sortBy.includes('quantity') ? 'quantity' : 'name'
    );

    // Apply search to grouped medicines if needed
    if (debouncedSearch) {
      groupedArray = searchGroupedMedicines(groupedArray, debouncedSearch);
    }

    // Apply expiry status filter to groups
    if (filters.expiryStatus !== 'all') {
      const filterMap = {
        'expired': 'expired' as const,
        'expiring': 'expiring' as const,
        'valid': 'active' as const
      };
      groupedArray = filterGroupedMedicines(groupedArray, filterMap[filters.expiryStatus] || 'all');
    }

    return groupedArray;
  }, [filteredAndSortedMedicines, debouncedSearch, filters.expiryStatus, sortBy]);

  const handleMedicineClick = useCallback((medicine: Medicine) => {
    navigate(`/medicine/${medicine.id}`);
  }, [navigate]);

  const handleGroupedMedicineClick = useCallback((groupedMedicine: any) => {
    // Navigate to group detail page with encoded group key
    const encodedGroupKey = encodeURIComponent(groupedMedicine.groupKey);
    navigate(`/medicine-group/${encodedGroupKey}`);
  }, [navigate]);

  const handleIndividualMedicineFromGroupClick = useCallback((medicine: Medicine) => {
    navigate(`/medicine/${medicine.id}`);
  }, [navigate]);

  const handleClearFilters = useCallback(() => {
    setFilters({
      search: '',
      categories: [],
      locations: [],
      expiryStatus: 'all',
      stockStatus: 'all'
    });
  }, []);

  const handleAddMedicine = useCallback(() => {
    navigate('/add-medicine');
  }, [navigate]);

  return (
    <Layout>
      <ModernHeader
        title="Mes Médicaments"
        subtitle="Gérez votre armoire à pharmacie personnelle"
        variant="navy"
      />
      <div className="space-y-6 pb-20">

        {/* Filters */}
        <MedicineFilters
          filters={filters}
          onFiltersChange={setFilters}
          locations={locations}
          onClearFilters={handleClearFilters}
        />

        {/* Sorting and Count */}
        <MedicineSorting
          sortBy={sortBy}
          onSortChange={setSortBy}
          totalCount={filteredAndSortedMedicines.length}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          groupedCount={filteredAndSortedGroupedMedicines.length}
          showViewToggle={true}
        />

        {/* Content */}
        {isLoading ? (
          <div className="flex justify-center py-12">
            <Loader2 className="animate-spin h-8 w-8 text-teal" />
          </div>
        ) : (viewMode === 'grouped' ? filteredAndSortedGroupedMedicines.length === 0 : filteredAndSortedMedicines.length === 0) ? (
          <div className="text-center py-12">
            {medicines.length === 0 ? (
              // No medicines at all
              <div className="space-y-4">
                <div className="w-16 h-16 bg-teal/10 rounded-full flex items-center justify-center mx-auto">
                  <Plus className="w-8 h-8 text-teal" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-navy mb-2">
                    Aucun médicament trouvé
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Commencez par ajouter votre premier médicament pour organiser votre armoire à pharmacie.
                  </p>
                  <Button 
                    onClick={handleAddMedicine}
                    className="bg-teal hover:bg-teal-dark text-white"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Ajouter un médicament
                  </Button>
                </div>
              </div>
            ) : (
              // No medicines match filters
              <div className="space-y-4">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto">
                  <Search className="w-8 h-8 text-gray-400" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-navy mb-2">
                    Aucun résultat trouvé
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Aucun médicament ne correspond à vos critères de recherche. Essayez de modifier vos filtres ou d'ajouter un nouveau médicament.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-2 justify-center">
                    <Button
                      variant="outline"
                      onClick={handleClearFilters}
                      className="border-teal text-teal hover:bg-teal/10"
                    >
                      Effacer les filtres
                    </Button>
                    <Button
                      onClick={handleAddMedicine}
                      className="bg-teal hover:bg-teal-dark text-white"
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Ajouter un médicament
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          // Medicine display based on view mode
          viewMode === 'grouped' ? (
            // Grouped view
            <div className="space-y-4">
              {filteredAndSortedGroupedMedicines.map((groupedMedicine) => (
                <GroupedMedicineCard
                  key={groupedMedicine.groupKey}
                  groupedMedicine={groupedMedicine}
                  onClick={() => handleGroupedMedicineClick(groupedMedicine)}
                  onMedicineClick={handleIndividualMedicineFromGroupClick}
                  showExpandButton={true}
                  defaultExpanded={false}
                />
              ))}
            </div>
          ) : (
            // Individual view - use virtualized list for large datasets
            filteredAndSortedMedicines.length > 50 ? (
              <VirtualizedMedicineList
                medicines={filteredAndSortedMedicines}
                onMedicineClick={handleMedicineClick}
                height={600}
              />
            ) : (
              <div className="space-y-4">
                {filteredAndSortedMedicines.map((medicine) => (
                  <MedicineCard
                    key={medicine.id}
                    medicine={medicine}
                    onClick={handleMedicineClick}
                  />
                ))}
              </div>
            )
          )
        )}

        {/* Add Medicine FAB */}
        {medicines.length > 0 && (
          <div className="fixed bottom-24 right-4 z-40">
            <Button
              onClick={handleAddMedicine}
              className="bg-teal hover:bg-teal-dark text-white rounded-full w-14 h-14 shadow-lg"
            >
              <Plus size={24} />
            </Button>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default MyMedicines;
