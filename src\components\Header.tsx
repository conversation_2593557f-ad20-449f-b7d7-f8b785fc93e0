
import React from "react";
import { Bell } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Link } from "react-router-dom";
import ProfileMenu from "@/components/ProfileMenu";
import featureFlags from "@/config/featureFlags";

interface HeaderProps {
  title?: string;
  userName?: string;
  showAvatar?: boolean;
  showNotifications?: boolean;
  notificationCount?: number;
  showGreeting?: boolean; // New prop to control greeting display
}

const Header = ({
  title = "Tableau de bord",
  userName,
  showAvatar = true,
  showNotifications = true,
  notificationCount = 0,
  showGreeting = false,
}: HeaderProps) => {
  // Only show notifications if the feature flag is enabled
  const displayNotifications = showNotifications && featureFlags.enableNotifications;

  // Generate personalized greeting
  const getGreeting = () => {
    const hour = new Date().getHours();
    let greeting = "Bonjour";

    if (hour < 12) {
      greeting = "Bonjour";
    } else if (hour < 18) {
      greeting = "Bon après-midi";
    } else {
      greeting = "Bonsoir";
    }

    return userName ? `${greeting}, ${userName}` : greeting;
  };

  return (
    <div className="flex justify-between items-center mb-6">
      <div>
        {showGreeting ? (
          <>
            <h1 className="text-xl font-bold text-navy">{getGreeting()}</h1>
            <p className="text-sm text-gray-600">{title}</p>
          </>
        ) : (
          <h1 className="text-xl font-bold text-navy">{title}</h1>
        )}
      </div>
      
      <div className="flex items-center gap-4">
        {displayNotifications && (
          <Link to="/alerts" className="relative">
            <Bell size={22} />
            {notificationCount > 0 && (
              <Badge
                variant="destructive"
                className="absolute -top-2 -right-2 w-5 h-5 p-0 flex items-center justify-center rounded-full"
              >
                {notificationCount}
              </Badge>
            )}
          </Link>
        )}
        
        {showAvatar && <ProfileMenu />}
      </div>
    </div>
  );
};

export default Header;
