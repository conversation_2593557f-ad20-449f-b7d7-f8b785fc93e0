import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:go_router/go_router.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

// Simple service for DI testing
@injectable
class TestService {
  String getMessage() => 'Dependency Injection Working!';
}

// Simple DI setup
final getIt = GetIt.instance;

@InjectableInit()
void configureDependencies() {
  getIt.registerSingleton<TestService>(TestService());
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    // Initialize DI
    configureDependencies();
    
    // Initialize Hive
    await Hive.initFlutter();
    
    // Initialize Supabase (with dummy values for testing)
    await Supabase.initialize(
      url: 'https://dummy.supabase.co',
      anonKey: 'dummy-anon-key-for-testing-only',
    );
    
    runApp(const MedyTrackApp());
  } catch (e) {
    runApp(MaterialApp(
      home: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              const Text('Initialization Error'),
              Text('$e'),
            ],
          ),
        ),
      ),
    ));
  }
}

// Router configuration
final GoRouter _router = GoRouter(
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => const MyHomePage(title: 'MedyTrack - Navigation & DI Test'),
    ),
    GoRoute(
      path: '/second',
      builder: (context, state) => const SecondPage(),
    ),
  ],
);

class MedyTrackApp extends StatelessWidget {
  const MedyTrackApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => TestBloc(),
      child: MaterialApp.router(
        title: 'MedyTrack - Group 3 Test',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: const Color(0xFF2D4A8E)),
          useMaterial3: true,
        ),
        routerConfig: _router,
      ),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  String _storageTest = 'Not tested';
  String _hiveTest = 'Not tested';
  String _supabaseTest = 'Not tested';
  String _diTest = 'Not tested';
  String _routerTest = '✅ Working (you\'re here!)';

  @override
  void initState() {
    super.initState();
    _testDependencies();
  }

  Future<void> _testDependencies() async {
    // Test DI
    try {
      final service = getIt<TestService>();
      final message = service.getMessage();
      setState(() {
        _diTest = message.isNotEmpty ? '✅ Working' : '❌ Failed';
      });
    } catch (e) {
      setState(() {
        _diTest = '❌ Error: $e';
      });
    }

    // Test Secure Storage
    try {
      await _secureStorage.write(key: 'test', value: 'success');
      final value = await _secureStorage.read(key: 'test');
      setState(() {
        _storageTest = value == 'success' ? '✅ Working' : '❌ Failed';
      });
    } catch (e) {
      setState(() {
        _storageTest = '❌ Error: $e';
      });
    }

    // Test Hive
    try {
      final box = await Hive.openBox('testBox');
      await box.put('test', 'success');
      final value = box.get('test');
      setState(() {
        _hiveTest = value == 'success' ? '✅ Working' : '❌ Failed';
      });
      await box.close();
    } catch (e) {
      setState(() {
        _hiveTest = '❌ Error: $e';
      });
    }

    // Test Supabase
    try {
      final client = Supabase.instance.client;
      setState(() {
        _supabaseTest = '✅ Initialized';
      });
    } catch (e) {
      setState(() {
        _supabaseTest = '❌ Error: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF2D4A8E),
        foregroundColor: Colors.white,
        title: Text(widget.title),
      ),
      body: BlocBuilder<TestBloc, TestState>(
        builder: (context, state) {
          return Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  const Icon(
                    Icons.navigation,
                    size: 100,
                    color: Color(0xFF0DCDB7),
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'Group 3: Navigation & DI Test',
                    style: TextStyle(
                      fontSize: 24, 
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2D4A8E),
                    ),
                  ),
                  const SizedBox(height: 30),
                  Card(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Dependency Status:',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          _buildStatusRow('flutter_bloc', '✅ Working (Groups 1-2)'),
                          _buildStatusRow('equatable', '✅ Working (Groups 1-2)'),
                          _buildStatusRow('supabase_flutter', _supabaseTest),
                          _buildStatusRow('hive_flutter', _hiveTest),
                          _buildStatusRow('flutter_secure_storage', _storageTest),
                          _buildStatusRow('go_router', _routerTest),
                          _buildStatusRow('get_it', _diTest),
                          _buildStatusRow('injectable', _diTest),
                          const SizedBox(height: 16),
                          Text(
                            'Counter: ${state.counter}',
                            style: const TextStyle(fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () => context.go('/second'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0DCDB7),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Test Navigation →'),
                  ),
                  const SizedBox(height: 20),
                  const Card(
                    margin: EdgeInsets.symmetric(horizontal: 16),
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: Column(
                        children: [
                          Text(
                            '🎉 Group 3 Dependencies Test!',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                          SizedBox(height: 10),
                          Text(
                            'Navigation and dependency injection are being tested.',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontSize: 14),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.read<TestBloc>().add(IncrementEvent()),
        backgroundColor: const Color(0xFF0DCDB7),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildStatusRow(String dependency, String status) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              dependency,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              status,
              style: TextStyle(
                color: status.startsWith('✅') ? Colors.green : Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class SecondPage extends StatelessWidget {
  const SecondPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF2D4A8E),
        foregroundColor: Colors.white,
        title: const Text('Second Page'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.check_circle,
              size: 100,
              color: Colors.green,
            ),
            const SizedBox(height: 20),
            const Text(
              '✅ Navigation Working!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => context.go('/'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0DCDB7),
                foregroundColor: Colors.white,
              ),
              child: const Text('← Back to Home'),
            ),
          ],
        ),
      ),
    );
  }
}

// Test BLoC (same as previous groups)
class TestBloc extends Bloc<TestEvent, TestState> {
  TestBloc() : super(const TestState(counter: 0)) {
    on<IncrementEvent>((event, emit) {
      emit(TestState(counter: state.counter + 1));
    });
  }
}

abstract class TestEvent extends Equatable {
  const TestEvent();
  
  @override
  List<Object> get props => [];
}

class IncrementEvent extends TestEvent {}

class TestState extends Equatable {
  final int counter;
  
  const TestState({required this.counter});
  
  @override
  List<Object> get props => [counter];
}
