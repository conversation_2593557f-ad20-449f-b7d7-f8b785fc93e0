{"buildFiles": ["C:\\Users\\<USER>\\Dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\GitHub\\medy-track (Lovable)\\medytrack_mobile_fresh\\build\\.cxx\\Debug\\5n4664o2\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\GitHub\\medy-track (Lovable)\\medytrack_mobile_fresh\\build\\.cxx\\Debug\\5n4664o2\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}