import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useUserSettings } from '@/hooks/useUserSettings';
import { isExpired, isNearExpiry } from '@/utils/helpers';

export const useNotificationCount = () => {
  const [notificationCount, setNotificationCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const { user, householdId } = useAuth();
  const { settings } = useUserSettings();

  useEffect(() => {
    const fetchNotificationCount = async () => {
      if (!householdId) {
        setNotificationCount(0);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        
        // Get medicine data for notification calculation
        const { data, error } = await supabase
          .from('user_medicines')
          .select('expiration, quantity, low_stock_threshold')
          .eq('household_id', householdId);

        if (error) throw error;

        let count = 0;
        
        if (data) {
          data.forEach((item: any) => {
            // Count expired medicines
            if (item.expiration && isExpired(item.expiration)) {
              count++;
            }
            // Count expiring soon medicines using custom threshold
            else if (item.expiration && isNearExpiry(item.expiration, settings.expiryWarningMonths)) {
              count++;
            }
            
            // Count low stock medicines
            const threshold = item.low_stock_threshold || 0;
            if (item.quantity <= threshold && item.quantity > 0) {
              count++;
            }
          });
        }

        setNotificationCount(count);
      } catch (err) {
        console.error('Error fetching notification count:', err);
        setNotificationCount(0);
      } finally {
        setIsLoading(false);
      }
    };

    fetchNotificationCount();
  }, [householdId, settings.expiryWarningMonths]);

  return { notificationCount, isLoading };
};
