import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

/// Statistics card component that mirrors the web app's dashboard cards
/// Features clickable cards with icons, numbers, and percentages
class StatisticsCard extends StatelessWidget {
  final String title;
  final int count;
  final int? totalCount;
  final IconData icon;
  final Color iconColor;
  final Color? backgroundColor;
  final VoidCallback? onTap;
  final bool showPercentage;

  const StatisticsCard({
    super.key,
    required this.title,
    required this.count,
    this.totalCount,
    required this.icon,
    this.iconColor = AppColors.teal,
    this.backgroundColor,
    this.onTap,
    this.showPercentage = true,
  });

  @override
  Widget build(BuildContext context) {
    final percentage = totalCount != null && totalCount! > 0 
        ? (count / totalCount! * 100).round() 
        : 0;

    return Card(
      elevation: 2,
      shadowColor: AppColors.shadowLight,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      color: backgroundColor ?? AppColors.cardLight,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon in circular background
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: iconColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: iconColor,
                  size: 24,
                ),
              ),
              
              const SizedBox(height: 12),
              
              // Count and percentage
              if (showPercentage && totalCount != null) ...[
                RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: count.toString(),
                        style: AppTextStyles.statisticsNumber,
                      ),
                      TextSpan(
                        text: ' ($percentage%)',
                        style: AppTextStyles.statisticsNumber.copyWith(
                          fontSize: 16,
                          color: AppColors.grey600,
                        ),
                      ),
                    ],
                  ),
                ),
              ] else ...[
                Text(
                  count.toString(),
                  style: AppTextStyles.statisticsNumber,
                  textAlign: TextAlign.center,
                ),
              ],
              
              const SizedBox(height: 8),
              
              // Title
              Text(
                title,
                style: AppTextStyles.statisticsLabel,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Factory methods for common statistics cards
extension StatisticsCardFactory on StatisticsCard {
  /// Creates an expired medicines card
  static StatisticsCard expired({
    required int count,
    int? totalCount,
    VoidCallback? onTap,
  }) {
    return StatisticsCard(
      title: 'Médicaments\nExpirés',
      count: count,
      totalCount: totalCount,
      icon: LucideIcons.alertTriangle,
      iconColor: AppColors.expired,
      onTap: onTap,
    );
  }

  /// Creates an expiring soon medicines card
  static StatisticsCard expiringSoon({
    required int count,
    int? totalCount,
    VoidCallback? onTap,
  }) {
    return StatisticsCard(
      title: 'Expirent\nBientôt',
      count: count,
      totalCount: totalCount,
      icon: LucideIcons.clock,
      iconColor: AppColors.expiringSoon,
      onTap: onTap,
    );
  }

  /// Creates a low stock medicines card
  static StatisticsCard lowStock({
    required int count,
    int? totalCount,
    VoidCallback? onTap,
  }) {
    return StatisticsCard(
      title: 'Stock\nFaible',
      count: count,
      totalCount: totalCount,
      icon: LucideIcons.packageOpen,
      iconColor: AppColors.lowStock,
      onTap: onTap,
    );
  }

  /// Creates a total medicines card
  static StatisticsCard total({
    required int count,
    VoidCallback? onTap,
  }) {
    return StatisticsCard(
      title: 'Total\nMédicaments',
      count: count,
      icon: LucideIcons.pill,
      iconColor: AppColors.teal,
      onTap: onTap,
      showPercentage: false,
    );
  }

  /// Creates a locations card
  static StatisticsCard locations({
    required int count,
    VoidCallback? onTap,
  }) {
    return StatisticsCard(
      title: 'Emplacements\nActifs',
      count: count,
      icon: LucideIcons.mapPin,
      iconColor: AppColors.navy,
      onTap: onTap,
      showPercentage: false,
    );
  }

  /// Creates a household members card
  static StatisticsCard members({
    required int count,
    VoidCallback? onTap,
  }) {
    return StatisticsCard(
      title: 'Membres\nFamille',
      count: count,
      icon: LucideIcons.users,
      iconColor: AppColors.info,
      onTap: onTap,
      showPercentage: false,
    );
  }
}
