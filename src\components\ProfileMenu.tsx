
import React from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { User, Settings, LogOut, Users, UserPlus, Home } from "lucide-react";

const ProfileMenu = () => {
  const { user, profile, signOut } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await signOut();
    navigate("/auth");
  };

  const userInitial = 
    profile?.name?.[0] || 
    user?.user_metadata?.name?.[0] || 
    user?.email?.[0] || 
    "U";

  const avatarUrl = 
    user?.user_metadata?.avatar_url || 
    profile?.avatar_url || 
    null;

  const displayName = 
    profile?.name || 
    user?.user_metadata?.name || 
    user?.email || 
    "Utilisateur";

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
          <Avatar className="h-10 w-10 cursor-pointer">
          {avatarUrl && <AvatarImage src={avatarUrl} alt={displayName} />}
          <AvatarFallback className="bg-primary text-primary-foreground">
            {userInitial}
          </AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{displayName}</p>
            <p className="text-xs leading-none text-muted-foreground">
              {user?.email}
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => navigate("/profile")}>
          <User className="mr-2 h-4 w-4" />
          <span>Mon Profil</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuLabel className="text-xs text-muted-foreground">
          Gestion du foyer
        </DropdownMenuLabel>
        <DropdownMenuItem onClick={() => navigate("/household-settings")}>
          <UserPlus className="mr-2 h-4 w-4" />
          <span>Inviter des membres</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => navigate("/family")}>
          <Users className="mr-2 h-4 w-4" />
          <span>Voir les membres</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => navigate("/locations")}>
          <Home className="mr-2 h-4 w-4" />
          <span>Emplacements</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => navigate("/settings")}>
          <Settings className="mr-2 h-4 w-4" />
          <span>Paramètres</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleLogout}>
          <LogOut className="mr-2 h-4 w-4" />
          <span>Déconnexion</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ProfileMenu;
