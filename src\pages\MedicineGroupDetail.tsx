import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Loader2, Edit, Trash2, Plus, Calendar, MapPin, User, Package } from "lucide-react";
import { Medicine } from "@/types";
import { useUserMedicines } from "@/hooks/useUserMedicines";
import { useLocations } from "@/hooks/useLocations";
import { useFamilyMembers } from "@/hooks/useFamilyMembers";
import { formatCompleteMedicineName, generateMedicineGroupKey } from "@/utils/medicineUtils";
import { formatDateShort, isExpired, isNearExpiry } from "@/utils/helpers";
import { toast } from "sonner";
import ModernHeader from "@/components/ModernHeader";
import { cn } from "@/lib/utils";

const MedicineGroupDetail = () => {
  const { groupKey } = useParams<{ groupKey: string }>();
  const navigate = useNavigate();
  const { medicines, isLoading, deleteMedicine } = useUserMedicines();
  const { locations } = useLocations();
  const { familyMembers } = useFamilyMembers();
  
  const [groupMedicines, setGroupMedicines] = useState<Medicine[]>([]);
  const [representativeMedicine, setRepresentativeMedicine] = useState<Medicine | null>(null);
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);

  useEffect(() => {
    if (!groupKey || !medicines.length) return;

    // Safely decode the group key from URL with error handling
    let decodedGroupKey: string;
    try {
      decodedGroupKey = decodeURIComponent(groupKey);
    } catch (error) {
      console.warn('Failed to decode group key:', groupKey, error);
      // If decoding fails, use the original key
      decodedGroupKey = groupKey;
    }

    // Find all medicines that belong to this group
    let matchingMedicines = medicines.filter(medicine =>
      generateMedicineGroupKey(medicine) === decodedGroupKey
    );

    // If no exact match found, try with the original (non-decoded) key as fallback
    if (matchingMedicines.length === 0 && decodedGroupKey !== groupKey) {
      matchingMedicines = medicines.filter(medicine =>
        generateMedicineGroupKey(medicine) === groupKey
      );
    }

    if (matchingMedicines.length === 0) {
      toast.error('Groupe de médicaments non trouvé');
      navigate('/my-medicines');
      return;
    }

    // Sort by expiry date (earliest first)
    const sortedMedicines = matchingMedicines.sort((a, b) => 
      a.expiryDate.localeCompare(b.expiryDate)
    );

    setGroupMedicines(sortedMedicines);
    setRepresentativeMedicine(sortedMedicines[0]);
  }, [groupKey, medicines, navigate]);

  const handleEdit = (medicine: Medicine) => {
    navigate(`/edit-medicine/${medicine.id}`);
  };

  const handleDelete = async (medicineId: string) => {
    if (deleteConfirm !== medicineId) {
      setDeleteConfirm(medicineId);
      setTimeout(() => setDeleteConfirm(null), 3000);
      return;
    }

    const success = await deleteMedicine(medicineId);
    if (success) {
      setDeleteConfirm(null);
      // If this was the last medicine in the group, go back
      if (groupMedicines.length <= 1) {
        navigate('/my-medicines');
      }
    }
  };

  const handleAddSimilar = () => {
    // Navigate to add medicine page with pre-filled data
    if (representativeMedicine) {
      const searchParams = new URLSearchParams({
        prefill: 'true',
        name: representativeMedicine.name,
        dosage: representativeMedicine.dosage || '',
        form: representativeMedicine.form || '',
        is_custom: representativeMedicine.is_custom ? 'true' : 'false',
        medicine_id: representativeMedicine.medicine_id || ''
      });
      navigate(`/add-medicine?${searchParams.toString()}`);
    }
  };

  const getLocationName = (locationId: string) => {
    const location = locations.find(loc => loc.id === locationId);
    return location?.name || 'Non spécifié';
  };

  const getFamilyMemberName = (memberId: string) => {
    const member = familyMembers.find(mem => mem.id === memberId);
    return member?.name || 'Non spécifié';
  };

  const getExpiryStatus = (expiryDate: string) => {
    if (isExpired(expiryDate)) return 'expired';
    if (isNearExpiry(expiryDate)) return 'expiring';
    return 'good';
  };

  const getExpiryStatusColor = (status: string) => {
    switch (status) {
      case 'expired': return 'border-red-500 bg-red-50';
      case 'expiring': return 'border-orange-500 bg-orange-50';
      default: return 'border-green-500 bg-green-50';
    }
  };

  const getExpiryStatusText = (status: string) => {
    switch (status) {
      case 'expired': return 'Expiré';
      case 'expiring': return 'Expire bientôt';
      default: return 'Valide';
    }
  };

  if (isLoading) {
    return (
      <div className="medicinet-container animate-fade-in flex items-center justify-center min-h-screen">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Chargement...</span>
        </div>
      </div>
    );
  }

  if (!representativeMedicine) {
    return (
      <div className="medicinet-container animate-fade-in">
        <ModernHeader 
          title="Groupe non trouvé"
          subtitle="Ce groupe de médicaments n'existe pas"
          showBackButton={true}
          variant="navy"
        />
        <div className="text-center py-8">
          <p className="text-gray-600 mb-4">Le groupe de médicaments demandé n'a pas été trouvé.</p>
          <Button onClick={() => navigate('/my-medicines')}>
            Retour à mes médicaments
          </Button>
        </div>
      </div>
    );
  }

  const totalQuantity = groupMedicines.reduce((sum, med) => sum + med.quantity, 0);

  return (
    <div className="medicinet-container animate-fade-in pb-20">
      <ModernHeader 
        title={formatCompleteMedicineName(representativeMedicine)}
        subtitle={`${groupMedicines.length} entrée${groupMedicines.length > 1 ? 's' : ''} • Total: ${totalQuantity} unité${totalQuantity > 1 ? 's' : ''}`}
        showBackButton={true}
        variant="navy"
      />

      <div className="space-y-4">
        {/* Add Similar Medicine Button */}
        <div className="bg-white rounded-lg p-4 border border-teal/20">
          <Button 
            onClick={handleAddSimilar}
            className="w-full bg-teal hover:bg-teal-dark text-white"
          >
            <Plus size={16} className="mr-2" />
            Ajouter une nouvelle entrée
          </Button>
        </div>

        {/* Medicine Entries */}
        {groupMedicines.map((medicine, index) => {
          const expiryStatus = getExpiryStatus(medicine.expiryDate);
          const isDeleting = deleteConfirm === medicine.id;
          
          return (
            <div 
              key={medicine.id}
              className={cn(
                "bg-white rounded-lg p-4 border-l-4 shadow-sm",
                getExpiryStatusColor(expiryStatus)
              )}
            >
              <div className="flex justify-between items-start mb-3">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-sm font-medium text-gray-500">
                      Entrée #{index + 1}
                    </span>
                    <span className={cn(
                      "px-2 py-1 rounded-full text-xs font-medium",
                      expiryStatus === 'expired' ? 'bg-red-100 text-red-800' :
                      expiryStatus === 'expiring' ? 'bg-orange-100 text-orange-800' :
                      'bg-green-100 text-green-800'
                    )}>
                      {getExpiryStatusText(expiryStatus)}
                    </span>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(medicine)}
                  >
                    <Edit size={14} />
                  </Button>
                  <Button
                    variant={isDeleting ? "destructive" : "outline"}
                    size="sm"
                    onClick={() => handleDelete(medicine.id)}
                  >
                    <Trash2 size={14} />
                    {isDeleting && <span className="ml-1 text-xs">Confirmer?</span>}
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Package size={16} className="text-gray-400" />
                  <span className="font-medium">{medicine.quantity} unité{medicine.quantity > 1 ? 's' : ''}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar size={16} className="text-gray-400" />
                  <span>{formatDateShort(medicine.expiryDate)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin size={16} className="text-gray-400" />
                  <span>{getLocationName(medicine.location)}</span>
                </div>
                {medicine.familyMember && (
                  <div className="flex items-center gap-2">
                    <User size={16} className="text-gray-400" />
                    <span>{getFamilyMemberName(medicine.familyMember.id)}</span>
                  </div>
                )}
              </div>

              {medicine.notes && (
                <div className="mt-3 p-2 bg-gray-50 rounded text-sm">
                  <strong>Notes:</strong> {medicine.notes}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MedicineGroupDetail;
