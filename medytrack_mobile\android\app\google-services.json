{"project_info": {"project_number": "123456789012", "project_id": "medytrack-mobile", "storage_bucket": "medytrack-mobile.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:123456789012:android:abcdef1234567890abcdef", "android_client_info": {"package_name": "com.medytrack.medytrack_mobile"}}, "oauth_client": [{"client_id": "123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDummyKeyForDevelopmentPurposes123456789"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:123456789012:android:abcdef1234567890abcdef", "android_client_info": {"package_name": "com.medytrack.medytrack_mobile.debug"}}, "oauth_client": [{"client_id": "123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDummyKeyForDevelopmentPurposes123456789"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com", "client_type": 3}]}}}], "configuration_version": "1"}