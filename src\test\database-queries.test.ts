import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useMedicineSearch } from '@/hooks/useMedicineSearch'
import { renderHook, waitFor } from '@testing-library/react'

// Mock Supabase client
const mockSupabaseSelect = vi.fn()
const mockSupabaseFrom = vi.fn(() => ({
  select: mockSupabaseSelect
}))

vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: mockSupabaseFrom
  }
}))

describe('Database Queries with Presentation Field', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Medicine Search Queries', () => {
    it('should include presentation field in medicine search query', async () => {
      // Mock the query chain
      const mockOr = vi.fn(() => ({
        limit: vi.fn(() => Promise.resolve({ data: [], error: null }))
      }))
      const mockSelectReturn = vi.fn(() => ({
        or: mockOr
      }))
      mockSupabaseSelect.mockReturnValue(mockSelectReturn())

      const { result } = renderHook(() => useMedicineSearch())

      // Trigger search
      await result.current.searchMedicines('test')

      // Verify that from was called with tunisia_medicines
      expect(mockSupabaseFrom).toHaveBeenCalledWith('tunisia_medicines')

      // Verify that select was called with the correct fields including presentation
      expect(mockSupabaseSelect).toHaveBeenCalledWith(`
          nom,
          laboratoire,
          dci,
          classe
        `)
    })

    it('should include presentation field in variant search query', async () => {
      // Mock the query chain for variants
      const mockOrder = vi.fn(() => Promise.resolve({ data: [], error: null }))
      const mockEq = vi.fn(() => ({
        order: mockOrder
      }))
      const mockSelectReturn = vi.fn(() => ({
        eq: mockEq
      }))
      mockSupabaseSelect.mockReturnValue(mockSelectReturn())

      const { result } = renderHook(() => useMedicineSearch())

      // Trigger variant search
      await result.current.getMedicineVariants('Levostamine')

      // Verify that select was called with presentation field
      expect(mockSupabaseSelect).toHaveBeenCalledWith(`
          id,
          nom,
          dosage,
          forme,
          presentation,
          laboratoire,
          amm,
          dci,
          classe,
          sous_classe
        `)

      // Verify that ordering includes presentation
      expect(mockOrder).toHaveBeenCalledWith('dosage', { ascending: true })
      expect(mockOrder).toHaveBeenCalledWith('forme', { ascending: true })
      expect(mockOrder).toHaveBeenCalledWith('presentation', { ascending: true })
    })
  })

  describe('Query Result Structure', () => {
    it('should handle medicine variants with presentation field', async () => {
      const mockVariantData = [
        {
          id: '1',
          nom: 'Levostamine',
          dosage: '5mg',
          forme: 'Comprimé',
          presentation: 'Boîte de 30 comprimés',
          laboratoire: 'Lab A',
          amm: 'AMM001',
          dci: 'DCI001',
          classe: 'Classe A',
          sous_classe: 'Sous-classe A'
        }
      ]

      // Mock successful query response
      const mockOrder = vi.fn(() => Promise.resolve({ 
        data: mockVariantData, 
        error: null 
      }))
      const mockEq = vi.fn(() => ({
        order: mockOrder
      }))
      const mockSelectReturn = vi.fn(() => ({
        eq: mockEq
      }))
      mockSupabaseSelect.mockReturnValue(mockSelectReturn())

      const { result } = renderHook(() => useMedicineSearch())

      // Trigger variant search
      await result.current.getMedicineVariants('Levostamine')

      // Wait for the result
      await waitFor(() => {
        expect(result.current.medicineVariants).toHaveLength(1)
      })

      // Verify that the variant includes presentation field
      const variant = result.current.medicineVariants[0]
      expect(variant).toHaveProperty('presentation')
      expect(variant.presentation).toBe('Boîte de 30 comprimés')
    })

    it('should handle multiple variants with different presentations', async () => {
      const mockVariantData = [
        {
          id: '1',
          nom: 'Levostamine',
          dosage: '5mg',
          forme: 'Comprimé',
          presentation: 'Boîte de 30 comprimés',
          laboratoire: 'Lab A',
          amm: 'AMM001',
          dci: 'DCI001',
          classe: 'Classe A',
          sous_classe: 'Sous-classe A'
        },
        {
          id: '2',
          nom: 'Levostamine',
          dosage: '5mg',
          forme: 'Comprimé',
          presentation: 'Flacon de 100ml',
          laboratoire: 'Lab A',
          amm: 'AMM002',
          dci: 'DCI001',
          classe: 'Classe A',
          sous_classe: 'Sous-classe A'
        }
      ]

      // Mock successful query response
      const mockOrder = vi.fn(() => Promise.resolve({ 
        data: mockVariantData, 
        error: null 
      }))
      const mockEq = vi.fn(() => ({
        order: mockOrder
      }))
      const mockSelectReturn = vi.fn(() => ({
        eq: mockEq
      }))
      mockSupabaseSelect.mockReturnValue(mockSelectReturn())

      const { result } = renderHook(() => useMedicineSearch())

      // Trigger variant search
      await result.current.getMedicineVariants('Levostamine')

      // Wait for the result
      await waitFor(() => {
        expect(result.current.medicineVariants).toHaveLength(2)
      })

      // Verify that both variants have different presentations
      const variants = result.current.medicineVariants
      expect(variants[0].presentation).toBe('Boîte de 30 comprimés')
      expect(variants[1].presentation).toBe('Flacon de 100ml')
      
      // Verify they have the same name, dosage, and form but different presentations
      expect(variants[0].nom).toBe(variants[1].nom)
      expect(variants[0].dosage).toBe(variants[1].dosage)
      expect(variants[0].forme).toBe(variants[1].forme)
      expect(variants[0].presentation).not.toBe(variants[1].presentation)
    })
  })

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      // Mock database error
      const mockOrder = vi.fn(() => Promise.resolve({ 
        data: null, 
        error: { message: 'Database connection failed' }
      }))
      const mockEq = vi.fn(() => ({
        order: mockOrder
      }))
      const mockSelectReturn = vi.fn(() => ({
        eq: mockEq
      }))
      mockSupabaseSelect.mockReturnValue(mockSelectReturn())

      const { result } = renderHook(() => useMedicineSearch())

      // Trigger variant search
      await result.current.getMedicineVariants('Levostamine')

      // Wait for error state
      await waitFor(() => {
        expect(result.current.error).toBeTruthy()
      })

      // Verify error is handled
      expect(result.current.error).toContain('Database connection failed')
      expect(result.current.medicineVariants).toHaveLength(0)
    })

    it('should handle missing presentation field gracefully', async () => {
      const mockVariantDataWithoutPresentation = [
        {
          id: '1',
          nom: 'Levostamine',
          dosage: '5mg',
          forme: 'Comprimé',
          // presentation field missing
          laboratoire: 'Lab A',
          amm: 'AMM001',
          dci: 'DCI001',
          classe: 'Classe A',
          sous_classe: 'Sous-classe A'
        }
      ]

      // Mock successful query response without presentation
      const mockOrder = vi.fn(() => Promise.resolve({ 
        data: mockVariantDataWithoutPresentation, 
        error: null 
      }))
      const mockEq = vi.fn(() => ({
        order: mockOrder
      }))
      const mockSelectReturn = vi.fn(() => ({
        eq: mockEq
      }))
      mockSupabaseSelect.mockReturnValue(mockSelectReturn())

      const { result } = renderHook(() => useMedicineSearch())

      // Trigger variant search
      await result.current.getMedicineVariants('Levostamine')

      // Wait for the result
      await waitFor(() => {
        expect(result.current.medicineVariants).toHaveLength(1)
      })

      // Verify that the variant handles missing presentation gracefully
      const variant = result.current.medicineVariants[0]
      expect(variant).toHaveProperty('presentation')
      expect(variant.presentation).toBeUndefined()
    })
  })
})
