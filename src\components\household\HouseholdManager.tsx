
import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Home, Edit2 } from 'lucide-react';
import { toast } from 'sonner';

interface HouseholdManagerProps {
  className?: string;
}

const HouseholdManager = ({ className = '' }: HouseholdManagerProps) => {
  const { householdId, householdName, createHousehold, updateHouseholdName } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [name, setName] = useState(householdName || 'Mon foyer');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSave = async () => {
    if (!name.trim()) {
      toast.error('Le nom du foyer ne peut pas être vide');
      return;
    }

    setIsSubmitting(true);
    
    try {
      if (householdId) {
        // Update existing household
        await updateHouseholdName(name);
        setIsEditing(false);
      } else {
        // Create new household
        await createHousehold(name);
        setIsCreating(false);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setName(householdName || 'Mon foyer');
    setIsEditing(false);
    setIsCreating(false);
  };

  // Create new household form
  if (!householdId || isCreating) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Home className="h-5 w-5 text-primary" />
            Créer un foyer
          </CardTitle>
          <CardDescription>
            Créez un foyer pour commencer à gérer vos médicaments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="household-name">Nom du foyer</Label>
              <Input
                id="household-name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Mon foyer"
              />
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end gap-2">
          <Button 
            variant="outline" 
            onClick={handleCancel}
            disabled={isSubmitting || !householdId}
          >
            Annuler
          </Button>
          <Button 
            onClick={handleSave}
            disabled={isSubmitting || !name.trim()}
          >
            {isSubmitting ? 'Création...' : 'Créer'}
          </Button>
        </CardFooter>
      </Card>
    );
  }

  // Edit existing household
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Home className="h-5 w-5 text-primary" />
          Mon Foyer
        </CardTitle>
        <CardDescription>
          Gérez les paramètres de votre foyer
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <div className="space-y-2">
            <Label htmlFor="edit-household-name">Nom du foyer</Label>
            <Input
              id="edit-household-name"
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
          </div>
        ) : (
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-sm text-muted-foreground mb-1 block">Nom du foyer</Label>
              <div className="text-lg font-medium">{householdName}</div>
            </div>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => setIsEditing(true)}
            >
              <Edit2 className="h-4 w-4" />
            </Button>
          </div>
        )}
      </CardContent>
      {isEditing && (
        <CardFooter className="flex justify-end gap-2">
          <Button 
            variant="outline" 
            onClick={handleCancel}
            disabled={isSubmitting}
          >
            Annuler
          </Button>
          <Button 
            onClick={handleSave}
            disabled={isSubmitting || !name.trim()}
          >
            {isSubmitting ? 'Enregistrement...' : 'Enregistrer'}
          </Button>
        </CardFooter>
      )}
    </Card>
  );
};

export default HouseholdManager;
