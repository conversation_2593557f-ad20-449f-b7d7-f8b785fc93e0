
@import url('https://fonts.googleapis.com/css2?family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 180 30% 96%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 174 100% 29%;
    --primary-foreground: 210 40% 98%;

    --secondary: 195 80% 75%;
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 12 94% 86%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 174 100% 29%;

    --radius: 1rem;

    /* Custom app colors */
    --navy: 220 45% 36%;
    --navy-foreground: 210 40% 98%;
    --teal: 174 100% 29%;
    --teal-foreground: 210 40% 98%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Ubuntu', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
    line-height: 1.5;
  }

  /* Custom classes for our app */
  .medicinet-card {
    @apply bg-white rounded-2xl shadow-md p-4 hover:shadow-lg transition-shadow duration-200;
  }
  
  .medicinet-container {
    @apply max-w-md mx-auto px-4 pb-20 pt-4;
  }

  /* Typography System - MedyTrack v1.4.0 */
  .text-greeting {
    @apply text-2xl font-bold leading-relaxed;
    font-weight: 700;
    line-height: 1.5;
  }

  .text-heading {
    @apply text-xl font-semibold leading-relaxed;
    font-weight: 600;
    line-height: 1.5;
  }

  .text-subheading {
    @apply text-lg font-medium leading-relaxed;
    font-weight: 500;
    line-height: 1.5;
  }

  .text-body {
    @apply text-base font-normal leading-relaxed;
    font-weight: 400;
    line-height: 1.5;
  }

  .text-caption {
    @apply text-sm font-normal leading-relaxed;
    font-weight: 400;
    line-height: 1.5;
  }

  .text-label {
    @apply text-sm font-medium leading-relaxed;
    font-weight: 500;
    line-height: 1.5;
  }

  /* Light Header Gradient Backgrounds */
  .header-gradient-light {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  }

  .header-gradient-teal {
    background: linear-gradient(135deg, #f0fdfa 0%, #ffffff 100%);
  }

  .header-gradient-navy {
    background: linear-gradient(135deg, #e1e7f5 0%, #ffffff 100%);
  }

  /* MedyTrack Color System */
  .text-teal {
    color: #0DCDB7;
  }

  .bg-teal {
    background-color: #0DCDB7;
  }

  .border-teal {
    border-color: #0DCDB7;
  }

  .text-navy {
    color: #2D4A8E;
  }

  .bg-navy {
    background-color: #2D4A8E;
  }

  .border-navy {
    border-color: #2D4A8E;
  }

  /* Ubuntu font utility class */
  .font-ubuntu {
    font-family: 'Ubuntu', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  }
}
