-- Fix for EditMedicine Bug: Add missing low_stock_threshold field to dashboard_medicine_alerts_view
-- This script fixes the critical bug where EditMedicine component couldn't access low_stock_threshold

-- Drop and recreate the view with the missing field
DROP VIEW IF EXISTS public.dashboard_medicine_alerts_view;

CREATE VIEW public.dashboard_medicine_alerts_view AS
SELECT
    um.id as user_medicine_id,
    um.household_id,
    um.medicine_id,
    um.custom_name,
    um.is_custom,
    um.dosage,
    um.expiration,
    um.quantity,
    um.category,
    um.notes,
    um.created_at,
    um.family_member_id,
    um.location as location_id,
    um.low_stock_threshold, -- CRITICAL FIX: Add missing low_stock_threshold field

    -- Medicine information from tunisia_medicines
    tm.nom as medicine_name,
    tm.forme,
    tm.laboratoire,
    tm.dci,
    tm.classe,
    tm.sous_classe,
    tm.amm,

    -- Create official label combining name, dosage, and form
    CASE
        WHEN um.is_custom = true THEN um.custom_name
        ELSE CONCAT_WS(' - ',
            COALESCE(tm.nom, 'Médicament inconnu'),
            NULLIF(COALESCE(um.dosage, tm.dosage), ''),
            NULLIF(tm.forme, '')
        )
    END as official_label,

    -- Location information
    l.name as location_name,

    -- Family member information
    fm.name as family_member_name,

    -- Get user's custom expiry threshold
    u.expiry_warning_days as user_expiry_threshold_months,

    -- Expiry status using user's custom threshold
    public.get_medicine_expiry_status(
        um.expiration::DATE,
        COALESCE(u.expiry_warning_days, 1)
    ) as expiration_status,

    -- Stock status
    CASE
        WHEN um.quantity = 0 THEN 'out_of_stock'
        WHEN um.quantity <= COALESCE(um.low_stock_threshold, 0) AND um.quantity > 0 THEN 'low_stock'
        ELSE 'adequate'
    END as stock_status,

    -- Additional fields for compatibility
    tm.presentation as indications

FROM public.user_medicines um
LEFT JOIN public.tunisia_medicines tm ON um.medicine_id = tm.id
LEFT JOIN public.locations l ON NULLIF(um.location, '')::uuid = l.id
LEFT JOIN public.family_members fm ON um.family_member_id = fm.id
LEFT JOIN public.users u ON um.household_id = u.household_id
WHERE um.household_id IS NOT NULL;

-- Grant permissions
GRANT SELECT ON public.dashboard_medicine_alerts_view TO authenticated;

-- Add comment
COMMENT ON VIEW public.dashboard_medicine_alerts_view IS 'Comprehensive view of medicines with alerts, using user-specific expiry thresholds stored in months. Fixed to include low_stock_threshold field for EditMedicine component.';

-- Verification query to ensure the field is included
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'dashboard_medicine_alerts_view' 
  AND column_name = 'low_stock_threshold';

-- Success message
SELECT 'EditMedicine bug fix applied successfully - low_stock_threshold field added to dashboard_medicine_alerts_view' as status;
