
import React from "react";
import { Category } from "@/types";
import { Pill, Thermometer, Flower2, Apple, Bandage, Stethoscope, Package } from "lucide-react";
import { Button } from "@/components/ui/button";

interface CategoryFilterProps {
  activeCategory: Category | 'all';
  onCategoryChange: (category: Category | 'all') => void;
}

const CategoryFilter = ({ activeCategory, onCategoryChange }: CategoryFilterProps) => {
  const categories = [
    { id: 'all', name: 'Tous', icon: Package },
    { id: 'pain', name: '<PERSON>ule<PERSON>', icon: Pill },
    { id: 'cold', name: 'Rhume', icon: Thermometer },
    { id: 'allergy', name: 'Allergie', icon: Flower2 },
    { id: 'digestion', name: 'Digestion', icon: Apple },
    { id: 'first-aid', name: 'Premiers soins', icon: Bandage },
    { id: 'prescription', name: 'Ordonnance', icon: Stethoscope }
  ];

  return (
    <div className="overflow-x-auto pb-2">
      <div className="flex space-x-2">
        {categories.map((category) => {
          const Icon = category.icon;
          const isActive = activeCategory === category.id;
          
          return (
            <Button
              key={category.id}
              variant={isActive ? "default" : "outline"}
              size="sm"
              className={`flex-shrink-0 ${isActive ? '' : 'bg-white'}`}
              onClick={() => onCategoryChange(category.id as Category | 'all')}
            >
              <Icon size={16} className="mr-1" />
              {category.name}
            </Button>
          );
        })}
      </div>
    </div>
  );
};

export default CategoryFilter;
