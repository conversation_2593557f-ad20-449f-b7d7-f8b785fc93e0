
import React from "react";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { Button } from "@/components/ui/button";

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onSearch: () => void;
  placeholder?: string;
}

const SearchBar = ({ 
  value, 
  onChange, 
  onSearch, 
  placeholder = "Rechercher un médicament..." 
}: SearchBarProps) => {
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      onSearch();
    }
  };

  return (
    <div className="relative flex items-center space-x-2">
      <div className="relative flex-grow">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4" />
        <Input
          type="text"
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyPress={handleKeyPress}
          className="pl-10 bg-white border-teal/20 focus:border-teal focus:ring-teal"
        />
      </div>
      <Button
        className="bg-teal hover:bg-teal-dark text-white"
        size="icon"
        onClick={onSearch}
        disabled={!value.trim()}
      >
        <Search size={20} />
      </Button>
    </div>
  );
};

export default SearchBar;
