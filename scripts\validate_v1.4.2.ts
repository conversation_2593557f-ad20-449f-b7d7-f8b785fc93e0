/**
 * MedyTrack v1.4.2 Validation Script
 * 
 * This script validates that all components of the new features are properly integrated:
 * 1. Custom Expiry Warning Thresholds
 * 2. Tagging System
 */

import { supabase } from '../src/integrations/supabase/client';

interface ValidationResult {
  feature: string;
  test: string;
  passed: boolean;
  message: string;
}

class V142Validator {
  private results: ValidationResult[] = [];

  private addResult(feature: string, test: string, passed: boolean, message: string) {
    this.results.push({ feature, test, passed, message });
    console.log(`${passed ? '✅' : '❌'} ${feature} - ${test}: ${message}`);
  }

  async validateCustomExpiryThresholds() {
    console.log('\n🔍 Validating Custom Expiry Thresholds...\n');

    // Test 1: Check if expiry_warning_days column exists
    try {
      const { data, error } = await supabase
        .from('users')
        .select('expiry_warning_days')
        .limit(1);
      
      if (error) throw error;
      
      this.addResult(
        'Custom Expiry Thresholds',
        'Database Column',
        true,
        'expiry_warning_days column exists and is accessible'
      );
    } catch (error) {
      this.addResult(
        'Custom Expiry Thresholds',
        'Database Column',
        false,
        `expiry_warning_days column not accessible: ${error}`
      );
    }

    // Test 2: Check if database functions exist
    try {
      const { data, error } = await supabase
        .rpc('is_medicine_expiring_soon', {
          p_expiration_date: '2024-08-15',
          p_threshold_months: 2
        });
      
      if (error) throw error;
      
      this.addResult(
        'Custom Expiry Thresholds',
        'Database Functions',
        true,
        'Custom expiry threshold functions are working'
      );
    } catch (error) {
      this.addResult(
        'Custom Expiry Thresholds',
        'Database Functions',
        false,
        `Database functions not working: ${error}`
      );
    }

    // Test 3: Check if dashboard view includes threshold
    try {
      const { data, error } = await supabase
        .from('dashboard_medicine_alerts_view')
        .select('user_expiry_threshold_months')
        .limit(1);
      
      if (error) throw error;
      
      const hasThresholdField = data && data.length > 0 && 
        data[0].user_expiry_threshold_months !== undefined;
      
      this.addResult(
        'Custom Expiry Thresholds',
        'Dashboard View',
        hasThresholdField,
        hasThresholdField 
          ? 'Dashboard view includes user expiry threshold'
          : 'Dashboard view missing threshold field'
      );
    } catch (error) {
      this.addResult(
        'Custom Expiry Thresholds',
        'Dashboard View',
        false,
        `Dashboard view error: ${error}`
      );
    }
  }

  async validateTaggingSystem() {
    console.log('\n🏷️ Validating Tagging System...\n');

    // Test 1: Check if tags table exists
    try {
      const { data, error } = await supabase
        .from('tags')
        .select('id, name, color, is_system_tag')
        .limit(1);
      
      if (error) throw error;
      
      this.addResult(
        'Tagging System',
        'Tags Table',
        true,
        'Tags table exists and is accessible'
      );
    } catch (error) {
      this.addResult(
        'Tagging System',
        'Tags Table',
        false,
        `Tags table not accessible: ${error}`
      );
    }

    // Test 2: Check if medicine_tags table exists
    try {
      const { data, error } = await supabase
        .from('medicine_tags')
        .select('id, user_medicine_id, tag_id')
        .limit(1);
      
      if (error) throw error;
      
      this.addResult(
        'Tagging System',
        'Medicine Tags Table',
        true,
        'Medicine tags junction table exists'
      );
    } catch (error) {
      this.addResult(
        'Tagging System',
        'Medicine Tags Table',
        false,
        `Medicine tags table not accessible: ${error}`
      );
    }

    // Test 3: Check if medicines_with_tags view exists
    try {
      const { data, error } = await supabase
        .from('medicines_with_tags')
        .select('user_medicine_id, tags, tag_names')
        .limit(1);
      
      if (error) throw error;
      
      this.addResult(
        'Tagging System',
        'Medicines with Tags View',
        true,
        'Medicines with tags view is working'
      );
    } catch (error) {
      this.addResult(
        'Tagging System',
        'Medicines with Tags View',
        false,
        `Medicines with tags view error: ${error}`
      );
    }

    // Test 4: Check if system tags were created
    try {
      const { data, error } = await supabase
        .from('tags')
        .select('name')
        .eq('is_system_tag', true);
      
      if (error) throw error;
      
      const expectedSystemTags = [
        'Douleur', 'Rhume', 'Allergie', 'Digestion', 
        'Premiers soins', 'Ordonnance', 'Autre'
      ];
      
      const systemTagNames = data?.map(tag => tag.name) || [];
      const hasAllSystemTags = expectedSystemTags.every(tag => 
        systemTagNames.includes(tag)
      );
      
      this.addResult(
        'Tagging System',
        'System Tags Migration',
        hasAllSystemTags,
        hasAllSystemTags 
          ? 'All system tags created successfully'
          : `Missing system tags: ${expectedSystemTags.filter(tag => !systemTagNames.includes(tag)).join(', ')}`
      );
    } catch (error) {
      this.addResult(
        'Tagging System',
        'System Tags Migration',
        false,
        `System tags check failed: ${error}`
      );
    }

    // Test 5: Check RPC functions
    try {
      const { data, error } = await supabase
        .rpc('get_household_tags', { p_household_id: 'test-household' });
      
      // This should not error even with invalid household ID
      this.addResult(
        'Tagging System',
        'RPC Functions',
        !error,
        !error 
          ? 'Tag management RPC functions are available'
          : `RPC functions error: ${error}`
      );
    } catch (error) {
      this.addResult(
        'Tagging System',
        'RPC Functions',
        false,
        `RPC functions not available: ${error}`
      );
    }
  }

  async validateIntegration() {
    console.log('\n🔗 Validating Integration...\n');

    // Test 1: Check TypeScript types are updated
    try {
      // This is a compile-time check, so we'll just verify the imports work
      const { Tag } = await import('../src/types');
      
      this.addResult(
        'Integration',
        'TypeScript Types',
        true,
        'Tag interface is available in types'
      );
    } catch (error) {
      this.addResult(
        'Integration',
        'TypeScript Types',
        false,
        `TypeScript types error: ${error}`
      );
    }

    // Test 2: Check if hooks are available
    try {
      const { useTags } = await import('../src/hooks/useTags');
      const { useUserSettings } = await import('../src/hooks/useUserSettings');
      
      this.addResult(
        'Integration',
        'React Hooks',
        true,
        'New hooks are available and importable'
      );
    } catch (error) {
      this.addResult(
        'Integration',
        'React Hooks',
        false,
        `Hooks import error: ${error}`
      );
    }

    // Test 3: Check if components are available
    try {
      const { default: TagFilter } = await import('../src/components/TagFilter');
      const { default: TagSelector } = await import('../src/components/TagSelector');
      
      this.addResult(
        'Integration',
        'React Components',
        true,
        'New components are available and importable'
      );
    } catch (error) {
      this.addResult(
        'Integration',
        'React Components',
        false,
        `Components import error: ${error}`
      );
    }
  }

  async runAllValidations() {
    console.log('🚀 Starting MedyTrack v1.4.2 Validation...\n');
    
    await this.validateCustomExpiryThresholds();
    await this.validateTaggingSystem();
    await this.validateIntegration();
    
    this.printSummary();
  }

  private printSummary() {
    console.log('\n📊 Validation Summary\n');
    
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    const percentage = Math.round((passed / total) * 100);
    
    console.log(`✅ Passed: ${passed}/${total} (${percentage}%)`);
    console.log(`❌ Failed: ${total - passed}/${total}`);
    
    if (passed === total) {
      console.log('\n🎉 All validations passed! MedyTrack v1.4.2 is ready for deployment.');
    } else {
      console.log('\n⚠️ Some validations failed. Please review and fix issues before deployment.');
      
      console.log('\nFailed Tests:');
      this.results
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`  • ${r.feature} - ${r.test}: ${r.message}`);
        });
    }
    
    console.log('\n📋 Detailed Results:');
    this.results.forEach(r => {
      console.log(`  ${r.passed ? '✅' : '❌'} ${r.feature} - ${r.test}`);
    });
  }
}

// Export for use in other scripts
export { V142Validator };

// Run validation if called directly
if (require.main === module) {
  const validator = new V142Validator();
  validator.runAllValidations().catch(console.error);
}
