import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { CheckCircle, Home, MapPin, Users, ArrowRight, ArrowLeft } from "lucide-react";
import { toast } from "sonner";
import HouseholdSetup from "@/components/onboarding/HouseholdSetup";
import LocationSetup from "@/components/onboarding/LocationSetup";
import { useLocations } from "@/hooks/useLocations";

const Onboarding = () => {
  const navigate = useNavigate();
  const { user, householdId, householdName, supabase, refreshProfile } = useAuth();
  const { locations, fetchLocations } = useLocations();
  const [currentStep, setCurrentStep] = useState(1);
  const [isCompleting, setIsCompleting] = useState(false);

  const totalSteps = 3;

  // Check if user should be here
  useEffect(() => {
    if (!user) {
      navigate('/auth');
      return;
    }

    // Check if onboarding is already completed
    const checkOnboardingStatus = async () => {
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('onboarding_completed')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error checking onboarding status:', error);
          // If column doesn't exist, continue with onboarding
          if (error.code === '42703') {
            console.log('Onboarding column not found, continuing with onboarding');
            return;
          }
          return;
        }

        if (data?.onboarding_completed) {
          navigate('/', { replace: true });
        }
      } catch (err) {
        console.error('Error checking onboarding status:', err);
      }
    };

    checkOnboardingStatus();
  }, [user, navigate, supabase]);

  // Refresh locations when on step 3 to ensure button state is correct
  useEffect(() => {
    if (currentStep === 3 && householdId) {
      console.log('On step 3, refreshing locations to check completion status...');
      fetchLocations();
    }
  }, [currentStep, householdId, fetchLocations]);

  const steps = [
    {
      id: 1,
      title: "Bienvenue dans MédiCabinet",
      description: "Configurons votre espace personnel pour gérer vos médicaments",
      icon: Home,
      completed: true
    },
    {
      id: 2,
      title: "Configurez votre foyer",
      description: "Créez ou sélectionnez votre foyer familial",
      icon: Users,
      completed: !!householdId
    },
    {
      id: 3,
      title: "Ajoutez un emplacement",
      description: "Créez au moins un emplacement pour organiser vos médicaments",
      icon: MapPin,
      completed: locations.length > 0
    }
  ];

  const currentStepData = steps[currentStep - 1];
  const progress = (currentStep / totalSteps) * 100;

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = async () => {
    console.log('Starting onboarding completion...', { householdId, locationsCount: locations.length });

    if (!householdId) {
      toast.error("Veuillez d'abord configurer votre foyer");
      setCurrentStep(2);
      return;
    }

    if (locations.length === 0) {
      toast.error("Veuillez ajouter au moins un emplacement");
      setCurrentStep(3);
      return;
    }

    setIsCompleting(true);
    try {
      console.log('Attempting to update profile for user:', user!.id);

      // Mark onboarding as completed in profiles table
      const { error: profileError } = await supabase
        .from('profiles')
        .update({ onboarding_completed: true })
        .eq('id', user!.id);

      if (profileError) {
        console.error('Error updating profile:', profileError);
        // If the column doesn't exist, we'll store it in localStorage as a fallback
        if (profileError.message?.includes('onboarding_completed') ||
            profileError.code === 'PGRST204') {
          console.warn('onboarding_completed column does not exist, using localStorage fallback');
          localStorage.setItem(`onboarding_completed_${user!.id}`, 'true');
          toast.success("Configuration terminée avec succès!");
          console.log('Using localStorage fallback for onboarding completion');
        } else {
          toast.error("Erreur lors de la finalisation de la configuration");
          return;
        }
      } else {
        console.log('Profile updated successfully in database');
        toast.success("Configuration terminée avec succès!");
      }

      // Refresh the auth context to update the onboarding status
      console.log('Refreshing profile...');
      await refreshProfile();

      // Small delay to ensure state updates
      setTimeout(() => {
        console.log('Navigating to home page...');
        navigate('/', { replace: true });
      }, 1000);

    } catch (err) {
      console.error('Error completing onboarding:', err);
      toast.error("Une erreur est survenue");
    } finally {
      setIsCompleting(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="text-center space-y-6">
            <div className="w-20 h-20 bg-navy/10 rounded-full flex items-center justify-center mx-auto">
              <Home className="w-10 h-10 text-navy" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-navy mb-2">
                Bienvenue dans MédiCabinet
              </h2>
              <p className="text-gray-600">
                Votre assistant personnel pour gérer vos médicaments en toute sécurité.
                Configurons ensemble votre espace pour une expérience optimale.
              </p>
            </div>
            <div className="bg-teal/10 p-4 rounded-lg">
              <p className="text-sm text-teal-dark font-medium">
                Cette configuration ne prendra que quelques minutes et vous permettra de :
              </p>
              <ul className="text-sm text-gray-600 mt-2 space-y-1">
                <li>• Organiser vos médicaments par emplacement</li>
                <li>• Partager l'accès avec votre famille</li>
                <li>• Recevoir des alertes de péremption</li>
              </ul>
            </div>
          </div>
        );

      case 2:
        return <HouseholdSetup onComplete={() => handleNext()} />;

      case 3:
        return <LocationSetup onComplete={() => {
          // Force refresh of locations to update the button state
          console.log('Location setup completed, refreshing locations...');
          fetchLocations();
        }} />;

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        {/* Progress Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-lg font-semibold text-navy">Configuration initiale</h1>
            <span className="text-sm text-gray-500">
              Étape {currentStep} sur {totalSteps}
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Step Indicators */}
        <div className="flex justify-between mb-8">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={`flex flex-col items-center space-y-2 ${
                index + 1 <= currentStep ? 'text-navy' : 'text-gray-400'
              }`}
            >
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  step.completed
                    ? 'bg-teal text-white'
                    : index + 1 === currentStep
                    ? 'bg-navy text-white'
                    : 'bg-gray-200'
                }`}
              >
                {step.completed ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  <step.icon className="w-5 h-5" />
                )}
              </div>
              <span className="text-xs text-center max-w-20">{step.title}</span>
            </div>
          ))}
        </div>

        {/* Main Content */}
        <Card className="mb-8">
          <CardContent className="p-8">
            {renderStepContent()}
          </CardContent>
        </Card>



        {/* Navigation */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className="flex items-center"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Précédent
          </Button>

          {currentStep === totalSteps ? (
            <Button
              onClick={() => {
                console.log('Complete button clicked', {
                  householdId,
                  locationsCount: locations.length,
                  locations: locations.map(l => l.name)
                });
                handleComplete();
              }}
              disabled={isCompleting || !householdId || locations.length === 0}
              className="bg-navy hover:bg-navy-dark text-white flex items-center"
            >
              {isCompleting ? "Finalisation..." : "Terminer la configuration"}
              <CheckCircle className="w-4 h-4 ml-2" />
            </Button>
          ) : (
            <Button
              onClick={handleNext}
              className="bg-navy hover:bg-navy-dark text-white flex items-center"
            >
              Suivant
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Onboarding;
