-- Complete Migration Script for MedyTrack v1.4.2
-- This script includes all necessary changes with proper error handling

-- 1. Add expiry_warning_days column to users table
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'expiry_warning_days'
  ) THEN
    ALTER TABLE public.users ADD COLUMN expiry_warning_days INTEGER DEFAULT 1;
    COMMENT ON COLUMN public.users.expiry_warning_days IS 'Custom expiry warning threshold in MONTHS (not days despite column name). Default is 1 month.';
  END IF;
END $$;

-- Update existing users to have default threshold
UPDATE public.users SET expiry_warning_days = 1 WHERE expiry_warning_days IS NULL;

-- 2. Create tags table
CREATE TABLE IF NOT EXISTS public.tags (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(50) NOT NULL,
  color VARCHAR(7) NOT NULL DEFAULT '#0DCDB7',
  household_id UUID NOT NULL,
  is_system_tag BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(name, household_id)
);

-- Add foreign key constraint if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'tags_household_id_fkey'
  ) THEN
    ALTER TABLE public.tags 
    ADD CONSTRAINT tags_household_id_fkey 
    FOREIGN KEY (household_id) REFERENCES households(id) ON DELETE CASCADE;
  END IF;
END $$;

-- 3. Create medicine_tags table
CREATE TABLE IF NOT EXISTS public.medicine_tags (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_medicine_id UUID NOT NULL,
  tag_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_medicine_id, tag_id)
);

-- Add foreign key constraints if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'medicine_tags_user_medicine_id_fkey'
  ) THEN
    ALTER TABLE public.medicine_tags 
    ADD CONSTRAINT medicine_tags_user_medicine_id_fkey 
    FOREIGN KEY (user_medicine_id) REFERENCES user_medicines(id) ON DELETE CASCADE;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'medicine_tags_tag_id_fkey'
  ) THEN
    ALTER TABLE public.medicine_tags 
    ADD CONSTRAINT medicine_tags_tag_id_fkey 
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE;
  END IF;
END $$;

-- 4. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_tags_household_id ON public.tags(household_id);
CREATE INDEX IF NOT EXISTS idx_tags_name_household ON public.tags(name, household_id);
CREATE INDEX IF NOT EXISTS idx_medicine_tags_medicine_id ON public.medicine_tags(user_medicine_id);
CREATE INDEX IF NOT EXISTS idx_medicine_tags_tag_id ON public.medicine_tags(tag_id);

-- 5. Enable RLS
ALTER TABLE public.tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.medicine_tags ENABLE ROW LEVEL SECURITY;

-- 6. Create RLS policies
DROP POLICY IF EXISTS "tags_household_policy" ON public.tags;
CREATE POLICY "tags_household_policy" ON public.tags
  FOR ALL USING (
    household_id = (SELECT household_id FROM users WHERE id = auth.uid())
  );

DROP POLICY IF EXISTS "medicine_tags_household_policy" ON public.medicine_tags;
CREATE POLICY "medicine_tags_household_policy" ON public.medicine_tags
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_medicines um 
      WHERE um.id = medicine_tags.user_medicine_id 
      AND um.household_id = (SELECT household_id FROM users WHERE id = auth.uid())
    )
  );

-- 7. Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.tags TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.medicine_tags TO authenticated;

-- 8. Insert system tags for all households
INSERT INTO public.tags (name, color, household_id, is_system_tag) 
SELECT 
  tag_name,
  tag_color,
  h.id as household_id,
  true as is_system_tag
FROM households h
CROSS JOIN (
  VALUES 
    ('Douleur', '#EF4444'),
    ('Rhume', '#3B82F6'),
    ('Allergie', '#F59E0B'),
    ('Digestion', '#10B981'),
    ('Premiers soins', '#8B5CF6'),
    ('Ordonnance', '#2D4A8E'),
    ('Autre', '#6B7280')
) AS default_tags(tag_name, tag_color)
ON CONFLICT (name, household_id) DO NOTHING;

-- 9. Migrate existing categories to tags
INSERT INTO public.medicine_tags (user_medicine_id, tag_id)
SELECT DISTINCT
  um.id as user_medicine_id,
  t.id as tag_id
FROM public.user_medicines um
JOIN public.tags t ON t.household_id = um.household_id AND t.is_system_tag = true
WHERE um.category IS NOT NULL
  AND um.category != ''
  AND (
    (um.category = 'pain' AND t.name = 'Douleur') OR
    (um.category = 'cold' AND t.name = 'Rhume') OR
    (um.category = 'allergy' AND t.name = 'Allergie') OR
    (um.category = 'digestion' AND t.name = 'Digestion') OR
    (um.category = 'first-aid' AND t.name = 'Premiers soins') OR
    (um.category = 'prescription' AND t.name = 'Ordonnance') OR
    (um.category = 'other' AND t.name = 'Autre')
  )
ON CONFLICT (user_medicine_id, tag_id) DO NOTHING;

-- 10. Create helper functions with correct types
DROP FUNCTION IF EXISTS public.get_household_tags(UUID);

CREATE OR REPLACE FUNCTION public.get_household_tags(p_household_id UUID)
RETURNS TABLE (
  id UUID,
  name VARCHAR(50),
  color VARCHAR(7),
  is_system_tag BOOLEAN,
  medicine_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.id,
    t.name,
    t.color,
    t.is_system_tag,
    COUNT(mt.id) as medicine_count
  FROM public.tags t
  LEFT JOIN public.medicine_tags mt ON t.id = mt.tag_id
  WHERE t.household_id = p_household_id
  GROUP BY t.id, t.name, t.color, t.is_system_tag
  ORDER BY t.is_system_tag DESC, t.name ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 11. Create add_tag_to_medicine function
DROP FUNCTION IF EXISTS public.add_tag_to_medicine(UUID, UUID);

CREATE OR REPLACE FUNCTION public.add_tag_to_medicine(
  p_user_medicine_id UUID,
  p_tag_id UUID
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Verify the medicine belongs to the user's household
  IF NOT EXISTS (
    SELECT 1 FROM user_medicines um
    WHERE um.id = p_user_medicine_id 
    AND um.household_id = (SELECT household_id FROM users WHERE id = auth.uid())
  ) THEN
    RETURN FALSE;
  END IF;
  
  -- Verify the tag belongs to the user's household
  IF NOT EXISTS (
    SELECT 1 FROM tags t
    WHERE t.id = p_tag_id 
    AND t.household_id = (SELECT household_id FROM users WHERE id = auth.uid())
  ) THEN
    RETURN FALSE;
  END IF;
  
  -- Add the tag (ignore if already exists)
  INSERT INTO public.medicine_tags (user_medicine_id, tag_id)
  VALUES (p_user_medicine_id, p_tag_id)
  ON CONFLICT (user_medicine_id, tag_id) DO NOTHING;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 12. Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.get_household_tags(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.add_tag_to_medicine(UUID, UUID) TO authenticated;

-- 13. Add helpful comments
COMMENT ON TABLE public.tags IS 'Tags for categorizing medicines with custom colors';
COMMENT ON TABLE public.medicine_tags IS 'Junction table linking medicines to tags (many-to-many)';
COMMENT ON FUNCTION public.get_household_tags(UUID) IS 'Get all tags for a household with medicine counts';
COMMENT ON FUNCTION public.add_tag_to_medicine(UUID, UUID) IS 'Add a tag to a medicine with proper authorization checks';

-- 14. Verification queries (uncomment to run)
-- SELECT 'Migration completed successfully!' as status;
-- SELECT COUNT(*) as total_tags FROM tags;
-- SELECT COUNT(*) as total_medicine_tags FROM medicine_tags;
-- SELECT name, COUNT(*) as usage_count FROM tags t 
--   JOIN medicine_tags mt ON t.id = mt.tag_id 
--   GROUP BY t.name ORDER BY usage_count DESC;
