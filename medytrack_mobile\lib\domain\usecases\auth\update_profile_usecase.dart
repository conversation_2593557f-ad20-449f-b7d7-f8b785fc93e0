import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../entities/user.dart';
import '../../repositories/auth_repository.dart';

class UpdateProfileParams {
  final String? name;
  final String? householdId;
  final String? householdName;
  final bool? isOnboardingCompleted;

  const UpdateProfileParams({
    this.name,
    this.householdId,
    this.householdName,
    this.isOnboardingCompleted,
  });
}

class UpdateProfileUseCase {
  final AuthRepository repository;

  UpdateProfileUseCase(this.repository);

  Future<Either<Failure, User>> call(UpdateProfileParams params) async {
    return await repository.updateProfile(params);
  }
}
