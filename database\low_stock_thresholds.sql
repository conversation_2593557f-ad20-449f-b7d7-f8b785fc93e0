-- Low Stock Thresholds Migration
-- This script adds customizable low stock thresholds to user_medicines table
-- and updates related views and functions.

-- 1. Add low_stock_threshold column to user_medicines table
ALTER TABLE public.user_medicines
ADD COLUMN IF NOT EXISTS low_stock_threshold INTEGER DEFAULT 0;

-- Add comment to explain the column
COMMENT ON COLUMN public.user_medicines.low_stock_threshold IS 'Custom low stock threshold for this medicine. When quantity <= threshold, medicine is considered low stock. Default is 0.';

-- 2. Create index for performance when filtering by low stock
-- This index is good for queries involving household_id, quantity, and threshold checks.
CREATE INDEX IF NOT EXISTS idx_user_medicines_low_stock
ON public.user_medicines(household_id, quantity, low_stock_threshold)
WHERE quantity <= low_stock_threshold;

-- 3. Update existing medicines to have a default threshold of 2 for non-zero quantities
-- This is a reasonable default if you want to initialize existing stock.
UPDATE public.user_medicines
SET low_stock_threshold = 2
WHERE quantity > 0 AND low_stock_threshold = 0;

-- 4. Function to check if a medicine is low stock
-- NOTE: The condition 'p_quantity > 0' means quantity 0 is not considered 'low_stock' by this function,
-- but rather 'out_of_stock'. This aligns with your later view logic.
CREATE OR REPLACE FUNCTION public.is_medicine_low_stock(
    p_quantity INTEGER,
    p_threshold INTEGER
)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN p_quantity <= p_threshold AND p_quantity > 0;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 5. Function to get low stock medicines for a household (RPC function for direct calls)
CREATE OR REPLACE FUNCTION public.get_low_stock_medicines(p_household_id UUID)
RETURNS TABLE (
    user_medicine_id UUID,
    medicine_name TEXT,
    quantity INTEGER,
    threshold INTEGER,
    location_name TEXT,
    family_member_name TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        um.id as user_medicine_id,
        COALESCE(um.custom_name, tm.nom, 'Médicament inconnu') as medicine_name,
        um.quantity,
        um.low_stock_threshold as threshold,
        l.name as location_name,
        fm.name as family_member_name
    FROM public.user_medicines um
    LEFT JOIN public.tunisia_medicines tm ON um.medicine_id = tm.id
    LEFT JOIN public.locations l ON NULLIF(um.location, '')::uuid = l.id
    LEFT JOIN public.family_members fm ON um.family_member_id = fm.id
    WHERE um.household_id = p_household_id
        AND public.is_medicine_low_stock(um.quantity, um.low_stock_threshold);
END;
$$ LANGUAGE plpgsql;

-- 6. Function to get low stock count for dashboard (RPC function)
CREATE OR REPLACE FUNCTION public.get_low_stock_count(p_household_id UUID)
RETURNS INTEGER AS $$
DECLARE
    v_count INTEGER;
BEGIN
    SELECT COUNT(*)
    INTO v_count
    FROM public.user_medicines
    WHERE household_id = p_household_id
        AND public.is_medicine_low_stock(quantity, low_stock_threshold);

    RETURN COALESCE(v_count, 0);
END;
$$ LANGUAGE plpgsql;

-- 7. Create/Replace low_stock_medicines_view
-- This view is excellent for providing a combined status for stock.
CREATE OR REPLACE VIEW public.low_stock_medicines_view AS
SELECT
