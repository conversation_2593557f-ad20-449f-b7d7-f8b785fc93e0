
import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON><PERSON>ooter,
  DialogClose,
  DialogDescription
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import LocationForm from "./LocationForm";
import { Loader2 } from "lucide-react";

interface LocationDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  name: string;
  description: string;
  setName: (name: string) => void;
  setDescription: (description: string) => void;
  confirmText: string;
  onConfirm: () => Promise<boolean>;
}

const LocationDialog = ({
  isOpen,
  onOpenChange,
  title,
  name,
  description,
  setName,
  setDescription,
  confirmText,
  onConfirm
}: LocationDialogProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleConfirm = async () => {
    setIsSubmitting(true);
    try {
      const success = await onConfirm();
      if (success) {
        onOpenChange(false);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!isSubmitting) {
        onOpenChange(open);
      }
    }}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-navy">{title}</DialogTitle>
          <DialogDescription>
            Remplissez les informations de l'emplacement ci-dessous.
          </DialogDescription>
        </DialogHeader>
        <LocationForm
          name={name}
          description={description}
          setName={setName}
          setDescription={setDescription}
          isSubmitting={isSubmitting}
        />
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline" disabled={isSubmitting} className="border-gray-300">Annuler</Button>
          </DialogClose>
          <Button onClick={handleConfirm} disabled={isSubmitting} className="bg-teal hover:bg-teal-dark text-white">
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Chargement...
              </>
            ) : (
              confirmText
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default LocationDialog;
