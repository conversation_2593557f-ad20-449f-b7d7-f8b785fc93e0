import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../widgets/common/app_logo.dart';
import '../../bloc/auth/auth_bloc.dart';

class OnboardingPage extends StatelessWidget {
  const OnboardingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const AppLogo(size: 100),
              const SizedBox(height: 32),

              Text(
                'Bienvenue dans MedyTrack!',
                style: AppTextStyles.headlineMedium.copyWith(
                  color: AppColors.navy,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 16),

              Text(
                'Configurons votre profil pour commencer à gérer vos médicaments de manière professionnelle.',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.grey600,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 48),

              // Profile Status Display
              BlocBuilder<AuthBloc, AuthState>(
                builder: (context, state) {
                  if (state is AuthOnboardingRequired) {
                    final user = state.user;
                    final status = user.profileCompletenessStatus;

                    return Container(
                      padding: const EdgeInsets.all(16),
                      margin: const EdgeInsets.only(bottom: 24),
                      decoration: BoxDecoration(
                        color: AppColors.grey100,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: AppColors.grey300),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '🔍 État du profil (Debug):',
                            style: AppTextStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.navy,
                            ),
                          ),
                          const SizedBox(height: 12),
                          ...status.entries
                              .map(
                                (entry) => Padding(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 2,
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(
                                        entry.value == true
                                            ? Icons.check_circle
                                            : Icons.cancel,
                                        color: entry.value == true
                                            ? Colors.green
                                            : Colors.red,
                                        size: 16,
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          '${entry.key}: ${entry.value}',
                                          style: AppTextStyles.bodySmall,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                              .toList(),
                          const SizedBox(height: 12),
                          Text(
                            'User: ${user.email}',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.grey600,
                            ),
                          ),
                        ],
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),

              // TEST: Complete Profile Button
              ElevatedButton(
                onPressed: () {
                  context.read<AuthBloc>().add(
                    const AuthUpdateProfileRequested(
                      name: 'Test User',
                      householdId: 'test-household-123',
                      householdName: 'Ma Famille Test',
                      isOnboardingCompleted: true,
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
                child: const Text('🚀 COMPLÉTER LE PROFIL (TEST)'),
              ),

              const SizedBox(height: 16),

              ElevatedButton(
                onPressed: () {
                  // For now, just navigate to dashboard
                  // In a real app, this would start the onboarding flow
                  context.go('/dashboard');
                },
                child: const Text('Commencer la configuration'),
              ),

              const SizedBox(height: 16),

              TextButton(
                onPressed: () {
                  context.go('/dashboard');
                },
                child: const Text('Passer pour le moment'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
