import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../widgets/common/app_logo.dart';

class OnboardingPage extends StatelessWidget {
  const OnboardingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const AppLogo(size: 100),
              const SizedBox(height: 32),
              
              Text(
                'Bienvenue dans MedyTrack!',
                style: AppTextStyles.headlineMedium.copyWith(
                  color: AppColors.navy,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 16),
              
              Text(
                'Configurons votre profil pour commencer à gérer vos médicaments de manière professionnelle.',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.grey600,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 48),
              
              ElevatedButton(
                onPressed: () {
                  // For now, just navigate to dashboard
                  // In a real app, this would start the onboarding flow
                  context.go('/dashboard');
                },
                child: const Text('Commencer la configuration'),
              ),
              
              const SizedBox(height: 16),
              
              TextButton(
                onPressed: () {
                  context.go('/dashboard');
                },
                child: const Text('Passer pour le moment'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
