import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../widgets/common/app_logo.dart';
import '../../bloc/auth/auth_bloc.dart';

class OnboardingPageProduction extends StatefulWidget {
  const OnboardingPageProduction({super.key});

  @override
  State<OnboardingPageProduction> createState() => _OnboardingPageProductionState();
}

class _OnboardingPageProductionState extends State<OnboardingPageProduction> {
  int currentStep = 1;
  final int totalSteps = 3;
  final TextEditingController householdNameController = TextEditingController(text: 'Mon foyer');
  bool isCompleting = false;

  @override
  void dispose() {
    householdNameController.dispose();
    super.dispose();
  }

  List<OnboardingStep> get steps => [
    OnboardingStep(
      id: 1,
      title: 'Bienvenue dans MedyTrack',
      description: 'Configurons votre espace personnel pour gérer vos médicaments',
      icon: Icons.home,
      completed: true,
    ),
    OnboardingStep(
      id: 2,
      title: 'Configurez votre foyer',
      description: 'Créez votre foyer familial',
      icon: Icons.people,
      completed: householdNameController.text.trim().isNotEmpty,
    ),
    OnboardingStep(
      id: 3,
      title: 'Finalisation',
      description: 'Terminez la configuration de votre profil',
      icon: Icons.check_circle,
      completed: false,
    ),
  ];

  double get progress => (currentStep / totalSteps) * 100;

  void handleNext() {
    if (currentStep < totalSteps) {
      setState(() {
        currentStep++;
      });
    }
  }

  void handlePrevious() {
    if (currentStep > 1) {
      setState(() {
        currentStep--;
      });
    }
  }

  Future<void> handleComplete() async {
    if (householdNameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez saisir un nom pour votre foyer'),
          backgroundColor: Colors.red,
        ),
      );
      setState(() {
        currentStep = 2;
      });
      return;
    }

    setState(() {
      isCompleting = true;
    });

    try {
      // Generate a unique household ID
      final householdId = 'household_${DateTime.now().millisecondsSinceEpoch}';
      
      // Complete profile with household data (like web app)
      context.read<AuthBloc>().add(
        AuthUpdateProfileRequested(
          name: 'User', // Basic name
          householdId: householdId,
          householdName: householdNameController.text.trim(),
          isOnboardingCompleted: true,
        ),
      );

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Configuration terminée avec succès!'),
          backgroundColor: Colors.green,
        ),
      );

    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        isCompleting = false;
      });
    }
  }

  Widget buildStepContent() {
    switch (currentStep) {
      case 1:
        return _buildWelcomeStep();
      case 2:
        return _buildHouseholdStep();
      case 3:
        return _buildCompletionStep();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildWelcomeStep() {
    return Column(
      children: [
        const AppLogo(size: 80),
        const SizedBox(height: 24),
        Text(
          'Bienvenue dans MedyTrack!',
          style: AppTextStyles.headlineMedium.copyWith(
            color: AppColors.navy,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Text(
          'MedyTrack vous aide à:',
          style: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildFeatureItem('Gérer vos médicaments facilement'),
            _buildFeatureItem('Organiser vos médicaments par emplacement'),
            _buildFeatureItem('Partager l\'accès avec votre famille'),
            _buildFeatureItem('Recevoir des alertes de péremption'),
          ],
        ),
      ],
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: AppColors.teal,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHouseholdStep() {
    return Column(
      children: [
        Icon(
          Icons.people,
          size: 64,
          color: AppColors.navy,
        ),
        const SizedBox(height: 24),
        Text(
          'Configurez votre foyer',
          style: AppTextStyles.headlineMedium.copyWith(
            color: AppColors.navy,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Text(
          'Donnez un nom à votre foyer pour organiser vos médicaments',
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.grey600,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        TextField(
          controller: householdNameController,
          decoration: InputDecoration(
            labelText: 'Nom du foyer',
            hintText: 'Mon foyer',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            prefixIcon: Icon(Icons.home, color: AppColors.teal),
          ),
          onChanged: (value) {
            setState(() {}); // Refresh to update step completion
          },
        ),
      ],
    );
  }

  Widget _buildCompletionStep() {
    return Column(
      children: [
        Icon(
          Icons.check_circle,
          size: 64,
          color: AppColors.teal,
        ),
        const SizedBox(height: 24),
        Text(
          'Prêt à commencer!',
          style: AppTextStyles.headlineMedium.copyWith(
            color: AppColors.navy,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Text(
          'Votre foyer "${householdNameController.text.trim()}" est configuré.',
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.grey600,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Text(
          'Cliquez sur "Terminer" pour accéder à votre tableau de bord.',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.grey600,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthAuthenticated) {
          // Profile completed successfully, navigate to dashboard
          context.go('/dashboard');
        } else if (state is AuthError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
          setState(() {
            isCompleting = false;
          });
        }
      },
      child: Scaffold(
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                // Progress indicator
                LinearProgressIndicator(
                  value: progress / 100,
                  backgroundColor: AppColors.grey200,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.teal),
                ),
                const SizedBox(height: 8),
                Text(
                  'Étape $currentStep sur $totalSteps',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.grey600,
                  ),
                ),
                const SizedBox(height: 32),

                // Step content
                Expanded(
                  child: buildStepContent(),
                ),

                // Navigation buttons
                Row(
                  children: [
                    if (currentStep > 1)
                      Expanded(
                        child: OutlinedButton(
                          onPressed: handlePrevious,
                          child: const Text('Précédent'),
                        ),
                      ),
                    if (currentStep > 1) const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: currentStep == totalSteps
                            ? (isCompleting ? null : handleComplete)
                            : handleNext,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.teal,
                          foregroundColor: Colors.white,
                        ),
                        child: isCompleting
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : Text(currentStep == totalSteps ? 'Terminer' : 'Suivant'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class OnboardingStep {
  final int id;
  final String title;
  final String description;
  final IconData icon;
  final bool completed;

  OnboardingStep({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.completed,
  });
}
