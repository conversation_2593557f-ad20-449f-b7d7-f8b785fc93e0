
import { Link, useLocation } from 'react-router-dom';
import { Home, Bell, LayoutDashboard, Pill } from 'lucide-react';

const BottomNavigation = () => {
  const location = useLocation();
  const currentPath = location.pathname;

  const isActive = (path: string) => {
    if (path === '/' && currentPath === '/') return true;
    if (path !== '/' && currentPath.startsWith(path)) return true;
    return false;
  };

  const getActiveColor = (path: string) => {
    if (isActive(path)) {
      // Use navy blue for home page, teal for others
      return path === '/' ? 'text-navy' : 'text-teal';
    }
    return 'text-gray-500';
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 flex justify-around items-center p-2 z-50">
      <Link
        to="/"
        className={`flex flex-col items-center p-2 ${getActiveColor('/')}`}
      >
        <Home size={24} />
        <span className="text-xs mt-1">Accueil</span>
      </Link>
      <Link
        to="/my-medicines"
        className={`flex flex-col items-center p-2 ${getActiveColor('/my-medicines')}`}
      >
        <Pill size={24} />
        <span className="text-xs mt-1">Mes Médicaments</span>
      </Link>
      <Link
        to="/dashboard"
        className={`flex flex-col items-center p-2 ${getActiveColor('/dashboard')}`}
      >
        <LayoutDashboard size={24} />
        <span className="text-xs mt-1">Tableau</span>
      </Link>
    </div>
  );
};

export default BottomNavigation;
