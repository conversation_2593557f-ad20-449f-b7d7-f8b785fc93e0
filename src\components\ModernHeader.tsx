import React from "react";
import { <PERSON>, ArrowLeft } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Link, useNavigate } from "react-router-dom";
import ProfileMenu from "@/components/ProfileMenu";
import { useNotificationCount } from "@/hooks/useNotificationCount";
import featureFlags from "@/config/featureFlags";

interface ModernHeaderProps {
  title?: string;
  userName?: string;
  showAvatar?: boolean;
  showNotifications?: boolean;
  showBackButton?: boolean;
  showGreeting?: boolean;
  subtitle?: string;
  variant?: 'light' | 'teal' | 'navy';
}

const ModernHeader = ({
  title = "MedyTrack",
  userName,
  showAvatar = true,
  showNotifications = true,
  showBackButton = false,
  showGreeting = false,
  subtitle,
  variant = 'light',
}: ModernHeaderProps) => {
  const navigate = useNavigate();
  const { notificationCount } = useNotificationCount();
  
  // Only show notifications if the feature flag is enabled
  const displayNotifications = showNotifications && featureFlags.enableNotifications;

  // Generate personalized greeting
  const getGreeting = () => {
    const hour = new Date().getHours();
    let greeting = "Bonjour";

    if (hour < 12) {
      greeting = "Bonjour";
    } else if (hour < 18) {
      greeting = "Bon après-midi";
    } else {
      greeting = "Bonsoir";
    }

    return userName ? `${greeting}, ${userName}` : greeting;
  };

  const gradientClass = variant === 'teal'
    ? 'header-gradient-teal'
    : variant === 'navy'
    ? 'header-gradient-navy'
    : 'header-gradient-light';

  return (
    <div className={`${gradientClass} rounded-xl p-8 mb-8 shadow-sm border border-gray-100`}>
      <div className="flex justify-between items-start">
        {/* Left side - Title/Greeting */}
        <div className="flex-1">
          <div className="flex items-center mb-3">
            {showBackButton && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate(-1)}
                className="text-navy hover:bg-gray-100 mr-3"
              >
                <ArrowLeft size={22} />
              </Button>
            )}

            {showGreeting ? (
              <h1 className="text-3xl font-bold text-navy leading-tight">
                {getGreeting()} 👋
              </h1>
            ) : (
              <h1 className="text-2xl font-bold text-navy leading-tight">{title}</h1>
            )}
          </div>

          {subtitle && (
            <p className="text-base text-gray-600 opacity-90 mt-2">
              {subtitle}
            </p>
          )}

          {showGreeting && title !== "MedyTrack" && (
            <p className="text-base text-gray-600 opacity-90 mt-2">
              {title}
            </p>
          )}
        </div>

        {/* Right side - Navigation elements */}
        <div className="flex items-center gap-4 ml-6">
          {displayNotifications && (
            <Link to="/alerts" className="relative">
              <div className="relative p-2 rounded-full border-2 border-navy hover:bg-navy/5 transition-colors">
                <Bell size={22} className="text-navy" />
                {notificationCount > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -top-1 -right-1 w-5 h-5 p-0 flex items-center justify-center rounded-full text-xs"
                  >
                    {notificationCount}
                  </Badge>
                )}
              </div>
            </Link>
          )}

          {showAvatar && (
            <div className="relative">
              <ProfileMenu />
              {/* Notification indicator on avatar */}
              {notificationCount > 0 && !displayNotifications && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-white"></div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ModernHeader;
