import { describe, it, expect } from 'vitest'
import { formatMedicineVariantName, formatCompleteMedicineName, formatMedicineSearchSubtitle } from '@/utils/medicineUtils'
import { MedicineVariant } from '@/hooks/useMedicineSearch'
import { Medicine } from '@/types'

describe('Presentation Field Core Functionality', () => {
  describe('✅ MedicineVariant Interface with Presentation', () => {
    it('should include presentation field in MedicineVariant', () => {
      const variant: MedicineVariant = {
        id: '1',
        nom: 'Levostamine',
        dosage: '5mg',
        forme: 'Comprimé',
        presentation: 'Boîte de 30 comprimés',
        laboratoire: 'Lab A',
        amm: 'AMM001',
        dci: 'DCI001',
        classe: 'Classe A',
        sous_classe: 'Sous-classe A'
      }

      expect(variant).toHaveProperty('presentation')
      expect(variant.presentation).toBe('Boîte de 30 comprimés')
    })

    it('should distinguish between variants with same name/dosage/form but different presentations', () => {
      const variant1: MedicineVariant = {
        id: '1',
        nom: 'Levostamine',
        dosage: '5mg',
        forme: 'Comprimé',
        presentation: 'Boîte de 30 comprimés',
        laboratoire: 'Lab A',
        amm: 'AMM001',
        dci: 'DCI001',
        classe: 'Classe A',
        sous_classe: 'Sous-classe A'
      }

      const variant2: MedicineVariant = {
        id: '2',
        nom: 'Levostamine',
        dosage: '5mg',
        forme: 'Comprimé',
        presentation: 'Flacon de 100ml',
        laboratoire: 'Lab A',
        amm: 'AMM002',
        dci: 'DCI001',
        classe: 'Classe A',
        sous_classe: 'Sous-classe A'
      }

      // Same name, dosage, form
      expect(variant1.nom).toBe(variant2.nom)
      expect(variant1.dosage).toBe(variant2.dosage)
      expect(variant1.forme).toBe(variant2.forme)
      
      // Different presentations
      expect(variant1.presentation).not.toBe(variant2.presentation)
      expect(variant1.presentation).toBe('Boîte de 30 comprimés')
      expect(variant2.presentation).toBe('Flacon de 100ml')
    })
  })

  describe('✅ Medicine Interface with Presentation', () => {
    it('should include presentation field in Medicine interface', () => {
      const medicine: Medicine = {
        id: 'med-1',
        name: 'Levostamine',
        dosage: '5mg',
        form: 'Comprimé',
        presentation: 'Boîte de 30 comprimés',
        quantity: 1,
        expiryDate: '2025-12-31',
        category: 'other',
        location: 'loc-1',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }

      expect(medicine).toHaveProperty('presentation')
      expect(medicine.presentation).toBe('Boîte de 30 comprimés')
    })
  })

  describe('✅ Utility Functions with Presentation', () => {
    it('should format medicine variant name with presentation', () => {
      const variant: MedicineVariant = {
        id: '1',
        nom: 'Levostamine',
        dosage: '5mg',
        forme: 'Comprimé',
        presentation: 'Boîte de 30 comprimés',
        laboratoire: 'Lab A',
        amm: 'AMM001',
        dci: 'DCI001',
        classe: 'Classe A',
        sous_classe: 'Sous-classe A'
      }

      const formattedName = formatMedicineVariantName(variant)
      expect(formattedName).toBe('Levostamine - 5mg - Comprimé - Boîte de 30 comprimés')
    })

    it('should format complete medicine name with presentation', () => {
      const medicine: Medicine = {
        id: 'med-1',
        name: 'Levostamine',
        dosage: '5mg',
        form: 'Comprimé',
        presentation: 'Boîte de 30 comprimés',
        quantity: 1,
        expiryDate: '2025-12-31',
        category: 'other',
        location: 'loc-1',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }

      const formattedName = formatCompleteMedicineName(medicine)
      expect(formattedName).toBe('Levostamine - 5mg - Comprimé - Boîte de 30 comprimés')
    })

    it('should format medicine search subtitle with presentation', () => {
      const subtitle = formatMedicineSearchSubtitle('5mg', 'Comprimé', 'Boîte de 30 comprimés')
      expect(subtitle).toBe('5mg - Comprimé - Boîte de 30 comprimés')
    })

    it('should handle missing presentation gracefully', () => {
      const medicine: Medicine = {
        id: 'med-1',
        name: 'Levostamine',
        dosage: '5mg',
        form: 'Comprimé',
        // presentation: undefined
        quantity: 1,
        expiryDate: '2025-12-31',
        category: 'other',
        location: 'loc-1',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }

      const formattedName = formatCompleteMedicineName(medicine)
      expect(formattedName).toBe('Levostamine - 5mg - Comprimé')
    })

    it('should handle custom medicines without presentation', () => {
      const customMedicine: Medicine = {
        id: 'med-1',
        name: 'Custom Medicine',
        custom_name: 'Mon médicament personnel',
        is_custom: true,
        quantity: 1,
        expiryDate: '2025-12-31',
        category: 'other',
        location: 'loc-1',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }

      const formattedName = formatCompleteMedicineName(customMedicine)
      expect(formattedName).toBe('Mon médicament personnel')
    })
  })

  describe('✅ Presentation Field Problem Resolution', () => {
    it('should create unique identifiers for medicines with same name/dosage/form but different presentations', () => {
      const variant1: MedicineVariant = {
        id: '1',
        nom: 'Levostamine',
        dosage: '5mg',
        forme: 'Comprimé',
        presentation: 'Boîte de 30 comprimés',
        laboratoire: 'Lab A',
        amm: 'AMM001',
        dci: 'DCI001',
        classe: 'Classe A',
        sous_classe: 'Sous-classe A'
      }

      const variant2: MedicineVariant = {
        id: '2',
        nom: 'Levostamine',
        dosage: '5mg',
        forme: 'Comprimé',
        presentation: 'Flacon de 100ml',
        laboratoire: 'Lab A',
        amm: 'AMM002',
        dci: 'DCI001',
        classe: 'Classe A',
        sous_classe: 'Sous-classe A'
      }

      const name1 = formatMedicineVariantName(variant1)
      const name2 = formatMedicineVariantName(variant2)

      // Should be different due to presentation
      expect(name1).not.toBe(name2)
      expect(name1).toBe('Levostamine - 5mg - Comprimé - Boîte de 30 comprimés')
      expect(name2).toBe('Levostamine - 5mg - Comprimé - Flacon de 100ml')
    })

    it('should solve the original problem: distinguishing between identical medicines with different presentations', () => {
      // Before: Both would show as "Levostamine - 5mg - Comprimé"
      // After: They show with distinct presentations
      
      const medicines = [
        {
          nom: 'Levostamine',
          dosage: '5mg',
          forme: 'Comprimé',
          presentation: 'Boîte de 30 comprimés'
        },
        {
          nom: 'Levostamine',
          dosage: '5mg',
          forme: 'Comprimé',
          presentation: 'Flacon de 100ml'
        }
      ]

      const displayNames = medicines.map(med => 
        formatMedicineSearchSubtitle(med.dosage, med.forme, med.presentation)
      )

      expect(displayNames[0]).toBe('5mg - Comprimé - Boîte de 30 comprimés')
      expect(displayNames[1]).toBe('5mg - Comprimé - Flacon de 100ml')
      expect(displayNames[0]).not.toBe(displayNames[1])
    })
  })
})
