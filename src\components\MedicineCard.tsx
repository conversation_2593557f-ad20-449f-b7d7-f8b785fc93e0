
import { Medicine } from "@/types";
import { formatDate, formatDateShort, isExpired, isNearExpiry, getCategoryIcon, getCategoryColor, getCategoryTextColor } from "@/utils/helpers";
import { formatCompleteMedicineName } from "@/utils/medicineUtils";
import { useUserSettings } from "@/hooks/useUserSettings";
import { Pill, AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface MedicineCardProps {
  medicine: Medicine;
  onClick?: () => void;
}

const MedicineCard = ({ medicine, onClick }: MedicineCardProps) => {
  const { settings } = useUserSettings();
  const expired = isExpired(medicine.expiryDate);
  const nearExpiry = isNearExpiry(medicine.expiryDate, settings.expiryWarningMonths);
  const lowQuantity = medicine.quantity <= 3 && medicine.quantity > 0;
  const outOfStock = medicine.quantity === 0;



  // Get the complete display name including dosage and form
  const displayName = formatCompleteMedicineName(medicine);

  // Get thick left/right border colors based on medicine status
  const getBorderColor = () => {
    if (expired) return "border-l-4 border-r-4 border-red-500";
    if (nearExpiry) return "border-l-4 border-r-4 border-amber-500";
    if (!medicine.expiryDate) return "border-l-4 border-r-4 border-gray-400";
    return "border-l-4 border-r-4 border-teal";
  };

  // Get hover background color based on status
  const getHoverBgColor = () => {
    if (expired) return "hover:bg-red-50";
    if (nearExpiry) return "hover:bg-amber-50";
    if (!medicine.expiryDate) return "hover:bg-gray-50";
    return "hover:bg-teal/5";
  };

  return (
    <div
      className={`medicinet-card ${getBorderColor()} ${getHoverBgColor()} cursor-pointer transition-colors`}
      onClick={onClick}
    >
      <div className="flex justify-between items-start mb-2">
        <div>
          <h3 className="font-bold text-lg text-navy">{displayName}</h3>
        </div>
        <div>
          {expired && (
            <Badge variant="destructive" className="flex items-center gap-1">
              <AlertCircle size={14} />
              Expiré
            </Badge>
          )}
          {nearExpiry && !expired && (
            <Badge variant="outline" className="bg-amber-100 border-amber-200 text-amber-800 flex items-center gap-1">
              Expire bientôt
            </Badge>
          )}
        </div>
      </div>

      <div className="flex justify-between items-center mt-3">
        <div className="flex items-center gap-2">
          <div className={`p-1.5 rounded-full ${getCategoryColor(medicine.category)}`}>
            <Pill size={16} className={getCategoryTextColor(medicine.category)} />
          </div>
          <span className="text-sm">{medicine.locationName || "Emplacement non spécifié"}</span>
        </div>
        
        <div className="flex items-center gap-2">
          <span className={`text-sm font-medium ${outOfStock ? 'text-destructive' : lowQuantity ? 'text-amber-600' : 'text-mint-dark'}`}>
            {outOfStock ? 'Rupture' : `${medicine.quantity} restants`}
          </span>
          <span className="text-xs text-gray-600">
            Exp: {formatDateShort(medicine.expiryDate)}
          </span>
        </div>
      </div>
    </div>
  );
};

export default MedicineCard;
