import { describe, it, expect } from 'vitest';
import { 
  groupMedicines, 
  generateMedicineGroupKey, 
  getGroupedMedicinesArray,
  detectDuplicateMedicines,
  getGroupedMedicineStats,
  searchGroupedMedicines
} from '@/utils/medicineUtils';
import { Medicine } from '@/types';

// Mock medicine data for testing
const createMockMedicine = (overrides: Partial<Medicine> = {}): Medicine => ({
  id: 'test-id',
  name: 'Paracétamol',
  dosage: '500mg',
  form: 'Comprimé',
  presentation: 'Boîte de 30',
  quantity: 1,
  expiryDate: '2025-12-31',
  category: 'pain',
  location: 'loc-1',
  locationName: 'Armoire à pharmacie',
  createdAt: '2025-01-01',
  updatedAt: '2025-01-01',
  is_custom: false,
  ...overrides
});

describe('Medicine Grouping Utils', () => {
  describe('generateMedicineGroupKey', () => {
    it('should generate consistent keys for same medicine', () => {
      const medicine1 = createMockMedicine();
      const medicine2 = createMockMedicine({ id: 'different-id', quantity: 2 });
      
      const key1 = generateMedicineGroupKey(medicine1);
      const key2 = generateMedicineGroupKey(medicine2);
      
      expect(key1).toBe(key2);
      expect(key1).toBe('paracétamol|500mg|comprimé');
    });

    it('should generate different keys for different medicines', () => {
      const medicine1 = createMockMedicine();
      const medicine2 = createMockMedicine({ dosage: '1000mg' });
      
      const key1 = generateMedicineGroupKey(medicine1);
      const key2 = generateMedicineGroupKey(medicine2);
      
      expect(key1).not.toBe(key2);
    });

    it('should handle custom medicines', () => {
      const customMedicine = createMockMedicine({
        is_custom: true,
        custom_name: 'Mon médicament',
        name: 'ignored'
      });
      
      const key = generateMedicineGroupKey(customMedicine);
      expect(key).toBe('custom:mon médicament');
    });
  });

  describe('groupMedicines', () => {
    it('should group medicines with same name/dosage/form', () => {
      const medicines = [
        createMockMedicine({ id: '1', quantity: 10, expiryDate: '2025-06-01' }),
        createMockMedicine({ id: '2', quantity: 5, expiryDate: '2025-12-01' }),
        createMockMedicine({ id: '3', dosage: '1000mg', quantity: 3 })
      ];
      
      const grouped = groupMedicines(medicines);
      
      expect(grouped.size).toBe(2);
      
      const paracetamol500 = Array.from(grouped.values()).find(g => g.dosage === '500mg');
      expect(paracetamol500?.totalQuantity).toBe(15);
      expect(paracetamol500?.medicines.length).toBe(2);
      expect(paracetamol500?.earliestExpiry).toBe('2025-06-01');
      expect(paracetamol500?.hasMultipleExpiries).toBe(true);
    });

    it('should handle empty array', () => {
      const grouped = groupMedicines([]);
      expect(grouped.size).toBe(0);
    });
  });

  describe('detectDuplicateMedicines', () => {
    it('should detect duplicate medicines', () => {
      const existingMedicines = [
        createMockMedicine({ id: '1' }),
        createMockMedicine({ id: '2', dosage: '1000mg' })
      ];
      
      const newMedicine = {
        name: 'Paracétamol',
        dosage: '500mg',
        form: 'Comprimé'
      };
      
      const duplicates = detectDuplicateMedicines(newMedicine, existingMedicines);
      expect(duplicates.length).toBe(1);
      expect(duplicates[0].id).toBe('1');
    });

    it('should not detect duplicates for different medicines', () => {
      const existingMedicines = [
        createMockMedicine({ id: '1', dosage: '1000mg' })
      ];
      
      const newMedicine = {
        name: 'Paracétamol',
        dosage: '500mg',
        form: 'Comprimé'
      };
      
      const duplicates = detectDuplicateMedicines(newMedicine, existingMedicines);
      expect(duplicates.length).toBe(0);
    });
  });

  describe('getGroupedMedicineStats', () => {
    it('should calculate correct statistics', () => {
      const medicines = [
        createMockMedicine({ id: '1', quantity: 10, expiryDate: '2024-01-01' }), // expired
        createMockMedicine({ id: '2', quantity: 5, expiryDate: '2024-01-01' }), // expired (same group)
        createMockMedicine({ id: '3', dosage: '1000mg', expiryDate: '2025-07-01' }), // expiring soon
        createMockMedicine({ id: '4', name: 'Ibuprofène', expiryDate: '2026-01-01' }) // active
      ];
      
      const grouped = groupMedicines(medicines);
      const groupedArray = getGroupedMedicinesArray(grouped);
      const stats = getGroupedMedicineStats(groupedArray);
      
      expect(stats.totalGroups).toBe(3);
      expect(stats.totalIndividualMedicines).toBe(4);
      expect(stats.groupsWithMultipleEntries).toBe(1);
      expect(stats.expiredGroups).toBe(1);
      expect(stats.activeGroups).toBe(1);
    });
  });

  describe('searchGroupedMedicines', () => {
    it('should search by medicine name', () => {
      const medicines = [
        createMockMedicine({ id: '1', name: 'Paracétamol' }),
        createMockMedicine({ id: '2', name: 'Ibuprofène' })
      ];
      
      const grouped = groupMedicines(medicines);
      const groupedArray = getGroupedMedicinesArray(grouped);
      const results = searchGroupedMedicines(groupedArray, 'para');
      
      expect(results.length).toBe(1);
      expect(results[0].name).toBe('Paracétamol');
    });

    it('should return all medicines for empty search', () => {
      const medicines = [
        createMockMedicine({ id: '1' }),
        createMockMedicine({ id: '2', name: 'Ibuprofène' })
      ];
      
      const grouped = groupMedicines(medicines);
      const groupedArray = getGroupedMedicinesArray(grouped);
      const results = searchGroupedMedicines(groupedArray, '');
      
      expect(results.length).toBe(2);
    });
  });
});
