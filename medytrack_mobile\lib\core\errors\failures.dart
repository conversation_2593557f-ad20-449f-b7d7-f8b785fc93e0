import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  final String message;
  
  const Failure(this.message);
  
  @override
  List<Object> get props => [message];
}

// General failures
class ServerFailure extends Failure {
  const ServerFailure(super.message);
}

class CacheFailure extends Failure {
  const CacheFailure(super.message);
}

class NetworkFailure extends Failure {
  const NetworkFailure(super.message);
}

// Authentication failures
class AuthenticationFailure extends Failure {
  const AuthenticationFailure(super.message);
}

class AuthorizationFailure extends Failure {
  const AuthorizationFailure(super.message);
}

// Validation failures
class ValidationFailure extends Failure {
  const ValidationFailure(super.message);
}

// Medicine failures
class MedicineFailure extends Failure {
  const MedicineFailure(super.message);
}

// Storage failures
class StorageFailure extends Failure {
  const StorageFailure(super.message);
}

// Camera failures
class CameraFailure extends Failure {
  const CameraFailure(super.message);
}

// Permission failures
class PermissionFailure extends Failure {
  const PermissionFailure(super.message);
}
