
import React from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MapPin, Pencil, Trash2 } from "lucide-react";
import { Location } from "@/types";

interface LocationCardProps {
  location: Location;
  onEdit: (location: Location) => void;
  onDelete: (location: Location) => void;
}

const LocationCard = ({ location, onEdit, onDelete }: LocationCardProps) => {
  return (
    <Card className="p-4 border-teal/20 hover:border-teal/40 transition-colors">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <div className="bg-teal/10 p-2 rounded-full">
            <MapPin size={24} className="text-teal" />
          </div>
          <div>
            <p className="font-medium text-navy">{location.name}</p>
            {location.description && (
              <p className="text-sm text-gray-600">{location.description}</p>
            )}
          </div>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onEdit(location)}
            className="text-teal hover:bg-teal/10"
          >
            <Pencil size={18} />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onDelete(location)}
            className="text-red-500 hover:bg-red-50"
          >
            <Trash2 size={18} />
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default LocationCard;
