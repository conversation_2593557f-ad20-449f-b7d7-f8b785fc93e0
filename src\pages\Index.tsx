
import React from "react";
import { useNavigate } from "react-router-dom";
import { useUserMedicines } from "@/hooks/useUserMedicines";
import { useAuth } from "@/contexts/AuthContext";
import ModernHeader from "@/components/ModernHeader";
import QuickSearchBar from "@/components/home/<USER>";
import LatestAdditions from "@/components/home/<USER>";
import AlertSummary from "@/components/home/<USER>";
import WelcomeCard from "@/components/home/<USER>";
import MedicineStatsCards from "@/components/home/<USER>";
import Layout from "@/components/Layout";
import featureFlags from "@/config/featureFlags";

const Index = () => {
  const navigate = useNavigate();
  const { profile } = useAuth();
  const { medicines } = useUserMedicines();

  return (
    <Layout>
      {/* Modern Header with Greeting and Navigation */}
      <ModernHeader
        userName={profile?.name}
        showGreeting={true}
        title="Bienvenue dans votre espace MedyTrack"
        variant="navy"
      />

      {/* Quick Search - Positioned immediately after header */}
      <div className="mb-6">
        <QuickSearchBar onSelect={(medicine) => navigate(`/medicine/${medicine.id}`)} />
      </div>

      <div className="space-y-6">
        {/* Medicine Statistics Cards */}
        {medicines.length > 0 && <MedicineStatsCards />}

        {/* Alert Summary (only show if there are alerts) */}
        <AlertSummary />

        {/* No Medicines Welcome Card */}
        {medicines.length === 0 && <WelcomeCard onAddClick={() => navigate("/add-medicine")} />}

        {/* Latest Additions */}
        {medicines.length > 0 && <LatestAdditions medicines={medicines.slice(0, 3)} />}
      </div>
    </Layout>
  );
};

export default Index;
