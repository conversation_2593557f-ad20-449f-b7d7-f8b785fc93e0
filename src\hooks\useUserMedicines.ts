
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Medicine, Category } from '@/types';
import { formatCompleteMedicineName } from '@/utils/medicineUtils';
import { toast } from 'sonner';

export const useUserMedicines = () => {
  const [medicines, setMedicines] = useState<Medicine[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user, householdId } = useAuth();



  const fetchMedicines = async () => {
    if (!user || !householdId) return;
    setIsLoading(true);

    try {
      // Get user_medicines data first
      const { data: userMedicinesData, error: userMedicinesError } = await supabase
        .from('user_medicines')
        .select('*')
        .eq('household_id', householdId)
        .order('created_at', { ascending: false });

      if (userMedicinesError) throw userMedicinesError;

      // Get locations data
      const { data: locationsData, error: locationsError } = await supabase
        .from('locations')
        .select('id, name')
        .eq('household_id', householdId);

      if (locationsError) throw locationsError;

      // Get family members data
      const { data: familyMembersData, error: familyMembersError } = await supabase
        .from('family_members')
        .select('id, name')
        .eq('household_id', householdId);

      if (familyMembersError) throw familyMembersError;

      // Get tunisia medicines data for non-custom medicines
      const medicineIds = userMedicinesData
        ?.filter(med => !med.is_custom && med.medicine_id)
        .map(med => med.medicine_id) || [];

      let tunisiaMedicinesData: any[] = [];
      if (medicineIds.length > 0) {
        const { data: tmData, error: tmError } = await supabase
          .from('tunisia_medicines')
          .select('*')
          .in('id', medicineIds);

        if (tmError) throw tmError;
        tunisiaMedicinesData = tmData || [];
      }

      // Create lookup maps
      const locationsMap = new Map(locationsData?.map(loc => [loc.id, loc.name]) || []);
      const familyMembersMap = new Map(familyMembersData?.map(fm => [fm.id, fm.name]) || []);
      const tunisiaMedicinesMap = new Map(tunisiaMedicinesData.map(tm => [tm.id, tm]) || []);



      // Format the data to match the expected Medicine type
      const formattedData: Medicine[] = (userMedicinesData || [])
        .map((item: any) => {
          // Get the appropriate name based on whether it's a custom medicine or not
          let baseMedicineName: string;
          if (item.is_custom) {
            baseMedicineName = item.custom_name || 'Médicament manuel';
          } else {
            // For database medicines, use the tunisia_medicines data
            const tunisiaMedicine = tunisiaMedicinesMap.get(item.medicine_id);
            baseMedicineName = tunisiaMedicine?.nom || 'Médicament inconnu';
          }

          // Handle expiry date - ensure we get the correct date format
          const expiryDate = item.expiration || new Date().toISOString().split('T')[0];

          // Get related data from maps
          const locationName = locationsMap.get(item.location) || 'Non spécifié';
          const familyMemberName = familyMembersMap.get(item.family_member_id) || '';
          const tunisiaMedicine = tunisiaMedicinesMap.get(item.medicine_id);

          return {
            id: item.id,
            name: baseMedicineName, // Use base name only, formatting will be done by components when needed
            dosage: item.dosage || tunisiaMedicine?.dosage || '',
            quantity: item.quantity || 0,
            expiryDate: expiryDate,
            category: (item.category || 'other') as Category,
            location: item.location || '',
            locationName: locationName,
            notes: item.notes || '',
            barcode: tunisiaMedicine?.amm || '',
            medicine_id: item.medicine_id,
            custom_name: item.custom_name || undefined,
            is_custom: item.is_custom || false,
            form: tunisiaMedicine?.forme || '',
            holder: tunisiaMedicine?.laboratoire || '',
            lowStockThreshold: item.low_stock_threshold || 0,
            createdAt: item.created_at || new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            indications: tunisiaMedicine?.indications || '',
            familyMember: item.family_member_id ? {
              id: item.family_member_id,
              name: familyMemberName
            } : null
          };
        })
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());



      setMedicines(formattedData);
    } catch (err) {
      console.error('Error fetching medicines:', err);
      toast.error('Erreur lors du chargement des médicaments');
    } finally {
      setIsLoading(false);
    }
  };

  const addMedicine = async (medicine: Omit<Medicine, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (!user || !householdId) return null;

    try {
      // Format the data according to the database schema - filter out "none" values
      const newMedicine = {
        medicine_id: medicine.medicine_id || null,
        custom_name: medicine.custom_name || null,
        is_custom: medicine.is_custom || false,
        dosage: medicine.dosage || null,
        quantity: medicine.quantity,
        expiration: medicine.expiryDate,
        category: medicine.category,
        location: medicine.location && medicine.location !== 'none' ? medicine.location : null,
        notes: medicine.notes || null,
        low_stock_threshold: (medicine as any).lowStockThreshold || 0,
        household_id: householdId,
        family_member_id: medicine.familyMember?.id && medicine.familyMember.id !== 'none' ? medicine.familyMember.id : null
      };

      const { data, error } = await supabase
        .from('user_medicines')
        .insert(newMedicine)
        .select()
        .single();

      if (error) {
        console.error('Error adding medicine:', error);
        throw error;
      }
      
      // Show confirmation toast after successful addition
      toast.success('Médicament ajouté avec succès', {
        description: `${medicine.is_custom ? medicine.custom_name : medicine.name || 'Le médicament'} a été ajouté à votre armoire.`
      });
      
      await fetchMedicines();
      return data.id;
    } catch (err) {
      console.error('Error adding medicine:', err);
      toast.error('Erreur lors de l\'ajout du médicament');
      throw err;
    }
  };

  const updateMedicine = async (id: string, updates: Partial<Medicine>) => {
    try {
      // Handle location and family member updates - filter out "none" values
      const location = updates.location && updates.location !== 'none' ? updates.location : null;
      const familyMemberId = updates.familyMember?.id && updates.familyMember.id !== 'none' ? updates.familyMember.id : null;

      const updateData = {
        ...(updates.dosage !== undefined && { dosage: updates.dosage }),
        ...(updates.quantity !== undefined && { quantity: updates.quantity }),
        ...(updates.expiryDate !== undefined && { expiration: updates.expiryDate }),
        ...(updates.category !== undefined && { category: updates.category }),
        ...(location !== undefined && { location }),
        ...(updates.notes !== undefined && { notes: updates.notes }),
        ...((updates as any).lowStockThreshold !== undefined && { low_stock_threshold: (updates as any).lowStockThreshold }),
        ...(familyMemberId !== undefined && { family_member_id: familyMemberId })
      };
      
      const { error } = await supabase
        .from('user_medicines')
        .update(updateData)
        .eq('id', id)
        .eq('household_id', householdId);
      
      if (error) throw error;
      
      await fetchMedicines();
      toast.success('Médicament mis à jour avec succès');
      return true;
    } catch (err) {
      console.error('Error updating medicine:', err);
      toast.error('Erreur lors de la mise à jour du médicament');
      return false;
    }
  };

  const deleteMedicine = async (id: string) => {
    try {
      const { error } = await supabase
        .from('user_medicines')
        .delete()
        .eq('id', id)
        .eq('household_id', householdId);
      
      if (error) throw error;
      
      setMedicines(medicines.filter(med => med.id !== id));
      toast.success('Médicament supprimé avec succès');
      return true;
    } catch (err) {
      console.error('Error deleting medicine:', err);
      toast.error('Erreur lors de la suppression du médicament');
      return false;
    }
  };

  useEffect(() => {
    if (householdId) {
      fetchMedicines();
    }
  }, [householdId]);

  return {
    medicines,
    isLoading,
    fetchMedicines,
    addMedicine,
    updateMedicine,
    deleteMedicine,
    householdId
  };
};
