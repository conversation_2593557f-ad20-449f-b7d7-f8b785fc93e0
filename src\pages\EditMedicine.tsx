import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Save, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { Medicine, Tag } from "@/types";
import { useUserMedicines } from "@/hooks/useUserMedicines";
import { useLocations } from "@/hooks/useLocations";
import { useFamilyMembers } from "@/hooks/useFamilyMembers";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import MonthYearPicker from "@/components/ui/month-year-picker";
import ModernHeader from "@/components/ModernHeader";
import TagSelector from "@/components/TagSelector";
import BottomNavigation from "@/components/BottomNavigation";

const EditMedicine = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { updateMedicine, updateMedicineTags } = useUserMedicines();
  const { locations } = useLocations();
  const { familyMembers } = useFamilyMembers();
  const { householdId } = useAuth();
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [medicine, setMedicine] = useState<Medicine | null>(null);
  
  const [formData, setFormData] = useState({
    custom_name: '',
    quantity: 1,
    category: 'other', // Keep for backward compatibility
    tags: [] as Tag[], // New tag system
    location: '',
    familyMember: '',
    notes: '',
    lowStockThreshold: 0,
    expiryDate: new Date().toISOString().split('T')[0],
    dosage: '',
  });

  useEffect(() => {
    const fetchMedicine = async () => {
      if (!id || !householdId) return;
      
      try {
        setLoading(true);
        
        // Fetch medicine data from the dashboard view
        const { data: viewData, error: viewError } = await supabase
          .from('dashboard_medicine_alerts_view')
          .select('*')
          .eq('user_medicine_id', id)
          .eq('household_id', householdId)
          .maybeSingle();
          
        if (viewError) throw viewError;
        
        if (!viewData) {
          toast.error('Médicament non trouvé');
          navigate('/my-medicines');
          return;
        }
        
        // Get additional details from user_medicines table
        const { data: medicineData, error: medicineError } = await supabase
          .from('user_medicines')
          .select('*')
          .eq('id', id)
          .eq('household_id', householdId)
          .maybeSingle();
          
        if (medicineError) throw medicineError;
        
        if (!medicineData) {
          toast.error('Médicament non trouvé');
          navigate('/my-medicines');
          return;
        }
        
        // Create medicine object
        const medicineObj: Medicine = {
          id: viewData.user_medicine_id,
          name: viewData.is_custom ? (viewData.custom_name || 'Médicament manuel') : (viewData.medicine_name || viewData.official_label || 'Médicament inconnu'),
          dosage: viewData.dosage || '',
          quantity: viewData.quantity || 0,
          expiryDate: viewData.expiration || new Date().toISOString().split('T')[0],
          category: viewData.category || 'other',
          location: viewData.location_id || '',
          locationName: viewData.location_name || 'Non spécifié',
          notes: viewData.notes || '',
          barcode: viewData.amm,
          medicine_id: viewData.medicine_id,
          custom_name: viewData.custom_name,
          is_custom: viewData.is_custom || false,
          createdAt: viewData.created_at || new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          familyMember: viewData.family_member_id ? {
            id: viewData.family_member_id,
            name: viewData.family_member_name || ''
          } : null
        };
        
        setMedicine(medicineObj);
        
        // Populate form data
        setFormData({
          custom_name: medicineObj.is_custom ? (medicineObj.custom_name || '') : '',
          quantity: medicineObj.quantity,
          category: medicineObj.category, // Keep for backward compatibility
          tags: medicineObj.tags || [], // Load existing tags
          location: medicineObj.location || '',
          familyMember: medicineObj.familyMember?.id || '',
          notes: medicineObj.notes || '',
          lowStockThreshold: formData.lowStockThreshold || 0,
          expiryDate: medicineObj.expiryDate,
          dosage: medicineObj.dosage || '',
        });
        
      } catch (error) {
        console.error('Error fetching medicine:', error);
        toast.error('Erreur lors du chargement du médicament');
        navigate('/my-medicines');
      } finally {
        setLoading(false);
      }
    };

    fetchMedicine();
  }, [id, householdId, navigate]);

  // Set default location when locations load
  useEffect(() => {
    if (locations.length > 0 && !formData.location) {
      setFormData(prev => ({
        ...prev,
        location: locations[0].id
      }));
    }
  }, [locations, formData.location]);

  // Set default family member when family members load
  useEffect(() => {
    if (familyMembers.length > 0 && !formData.familyMember) {
      setFormData(prev => ({
        ...prev,
        familyMember: familyMembers[0].id
      }));
    }
  }, [familyMembers, formData.familyMember]);

  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!medicine) return;
    
    if (formData.quantity <= 0) {
      toast.error('La quantité doit être supérieure à 0');
      return;
    }

    try {
      setSaving(true);
      
      const medicineToUpdate: Partial<Medicine> = {
        quantity: formData.quantity,
        expiryDate: formData.expiryDate,
        category: formData.category as any,
        location: formData.location || '',
        notes: formData.notes,
        lowStockThreshold: formData.lowStockThreshold,
        dosage: formData.dosage,
        familyMember: formData.familyMember ?
          { id: formData.familyMember, name: '' } : null
      };

      // For custom medicines, also update the custom name
      if (medicine.is_custom) {
        medicineToUpdate.custom_name = formData.custom_name;
      }

      const success = await updateMedicine(medicine.id, medicineToUpdate);

      // Update tags separately
      if (success && formData.tags) {
        const tagIds = formData.tags.map(tag => tag.id);
        await updateMedicineTags(medicine.id, tagIds);
      }

      if (success) {
        toast.success('Médicament modifié avec succès');
        navigate(`/medicine/${medicine.id}`);
      }
    } catch (error) {
      console.error('Error updating medicine:', error);
      toast.error('Erreur lors de la modification du médicament');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Chargement...</span>
        </div>
      </div>
    );
  }

  if (!medicine) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Médicament non trouvé</p>
          <Button onClick={() => navigate('/my-medicines')} className="mt-4">
            Retour à mes médicaments
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="medicinet-container animate-fade-in pb-20">
      <ModernHeader
        title="Modifier le médicament"
        subtitle="Modifiez les informations de votre médicament"
        showBackButton={true}
        variant="navy"
      />

      {/* Form */}
      <div className="space-y-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Medicine Name (read-only for database medicines, editable for custom) */}
          <div className="space-y-2">
            <Label htmlFor="name">Nom du médicament</Label>
            {medicine.is_custom ? (
              <Input
                id="name"
                value={formData.custom_name}
                onChange={(e) => handleFormChange('custom_name', e.target.value)}
                placeholder="Nom du médicament"
                required
              />
            ) : (
              <Input
                id="name"
                value={medicine.name}
                disabled
                className="bg-gray-100"
              />
            )}
          </div>

          {/* Dosage */}
          <div className="space-y-2">
            <Label htmlFor="dosage">Dosage</Label>
            <Input
              id="dosage"
              value={formData.dosage}
              onChange={(e) => handleFormChange('dosage', e.target.value)}
              placeholder="Ex: 500mg, 1 comprimé"
            />
          </div>

          {/* Quantity */}
          <div className="space-y-2">
            <Label htmlFor="quantity">Quantité</Label>
            <Input
              id="quantity"
              type="number"
              min="1"
              value={formData.quantity}
              onChange={(e) => handleFormChange('quantity', parseInt(e.target.value) || 1)}
              required
            />
          </div>

          {/* Expiry Date */}
          <MonthYearPicker
            label="Date d'expiration"
            value={formData.expiryDate}
            onChange={(date) => handleFormChange('expiryDate', date)}
            required
          />

          {/* Tags */}
          <div className="space-y-2">
            <Label>Étiquettes</Label>
            <TagSelector
              selectedTags={formData.tags}
              onTagsChange={(tags) => handleFormChange('tags', tags)}
              placeholder="Sélectionner des étiquettes..."
            />
            <p className="text-xs text-gray-500">
              Organisez vos médicaments avec des étiquettes personnalisées
            </p>
          </div>

          {/* Location */}
          <div className="space-y-2">
            <Label htmlFor="location">Emplacement</Label>
            <Select value={formData.location} onValueChange={(value) => handleFormChange('location', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Sélectionner un emplacement" />
              </SelectTrigger>
              <SelectContent>
                {locations.map((location) => (
                  <SelectItem key={location.id} value={location.id}>
                    {location.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Family Member */}
          <div className="space-y-2">
            <Label htmlFor="familyMember">Membre de la famille</Label>
            <Select value={formData.familyMember} onValueChange={(value) => handleFormChange('familyMember', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Sélectionner un membre" />
              </SelectTrigger>
              <SelectContent>
                {familyMembers.map((member) => (
                  <SelectItem key={member.id} value={member.id}>
                    {member.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Low Stock Threshold */}
          <div className="space-y-2">
            <Label htmlFor="lowStockThreshold">Seuil de stock faible</Label>
            <Input
              id="lowStockThreshold"
              type="number"
              min="0"
              value={formData.lowStockThreshold || 0}
              onChange={(e) => handleFormChange('lowStockThreshold', parseInt(e.target.value) || 0)}
              placeholder="0"
            />
            <p className="text-xs text-gray-500">
              Vous serez alerté quand la quantité atteint ce seuil
            </p>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleFormChange('notes', e.target.value)}
              placeholder="Notes additionnelles..."
              rows={3}
            />
          </div>

          {/* Submit Button */}
          <Button 
            type="submit" 
            className="w-full bg-teal hover:bg-teal-dark text-white"
            disabled={saving}
          >
            {saving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Modification en cours...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Modifier le médicament
              </>
            )}
          </Button>
        </form>
      </div>

      <BottomNavigation />
    </div>
  );
};

export default EditMedicine;
