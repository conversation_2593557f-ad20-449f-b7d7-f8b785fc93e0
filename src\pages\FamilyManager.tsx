
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Plus, User, ArrowLeft } from "lucide-react";
import { useFamilyMembers } from "@/hooks/useFamilyMembers";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import FamilyMemberCard from "@/components/family/FamilyMemberCard";
import FamilyMemberDialog from "@/components/family/FamilyMemberDialog";
import DeleteConfirmDialog from "@/components/dialogs/DeleteConfirmDialog";
import ModernHeader from "@/components/ModernHeader";

const relationOptions = [
  { value: "self", label: "Moi" },
  { value: "child", label: "Enfant" },
  { value: "parent", label: "Parent" },
  { value: "other", label: "Autre" }
];

const FamilyManager = () => {
  const navigate = useNavigate();
  const { familyMembers, isLoading, addFamilyMember, updateFamilyMember, deleteFamilyMember } = useFamilyMembers();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [name, setName] = useState("");
  const [relation, setRelation] = useState<string>("");
  const [birthDate, setBirthDate] = useState("");
  const [selectedMember, setSelectedMember] = useState<{ id: string, name: string } | null>(null);
  const { user } = useAuth();

  const handleAdd = async () => {
    if (!name.trim()) {
      toast.error('Veuillez saisir un nom');
      return;
    }

    const result = await addFamilyMember(name, relation, birthDate);
    if (result) {
      setName("");
      setRelation("");
      setBirthDate("");
      setIsDialogOpen(false);
    }
  };

  const handleEdit = async () => {
    if (!selectedMember || !name.trim()) {
      toast.error('Veuillez saisir un nom');
      return;
    }

    const result = await updateFamilyMember(selectedMember.id, { name, relation, birthDate });
    if (result) {
      setName("");
      setRelation("");
      setBirthDate("");
      setSelectedMember(null);
      setIsEditDialogOpen(false);
    }
  };

  const handleDelete = async () => {
    if (!selectedMember) return;

    const result = await deleteFamilyMember(selectedMember.id);
    if (result) {
      setSelectedMember(null);
      setIsDeleteDialogOpen(false);
    }
  };

  const openEditDialog = (member: { id: string, name: string, relation?: string, birthDate?: string }) => {
    setSelectedMember(member);
    setName(member.name);
    setRelation(member.relation || "");
    setBirthDate(member.birthDate || "");
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (member: { id: string, name: string }) => {
    setSelectedMember(member);
    setIsDeleteDialogOpen(true);
  };

  const getRelationLabel = (relationValue?: string) => {
    return relationOptions.find(r => r.value === relationValue)?.label || relationValue;
  };

  return (
    <div className="medicinet-container animate-fade-in pb-20">
      <ModernHeader
        title="Gestion de la famille"
        subtitle="Gérez les membres de votre famille"
        showBackButton={true}
        variant="navy"
      />

      {isLoading ? (
        <div className="flex justify-center py-12">
          <Loader2 className="animate-spin h-8 w-8 text-teal" />
        </div>
      ) : (
        <>
          <div className="space-y-4 mb-6">
            <Card className="p-4 border-navy/20">
              <div className="flex items-center space-x-4">
                <div className="bg-navy/10 p-2 rounded-full">
                  <User className="text-navy" />
                </div>
                <div>
                  <p className="font-medium text-navy">{user?.user_metadata?.name || user?.email}</p>
                  <p className="text-sm text-gray-600">Vous (Compte principal)</p>
                </div>
              </div>
            </Card>

            {familyMembers.map((member) => (
              <FamilyMemberCard
                key={member.id}
                member={member}
                relationLabel={getRelationLabel(member.relation)}
                onEdit={openEditDialog}
                onDelete={openDeleteDialog}
              />
            ))}

            <Button
              className="w-full py-2 mt-4 bg-teal hover:bg-teal-dark text-white"
              onClick={() => setIsDialogOpen(true)}
            >
              <Plus size={18} className="mr-2" />
              Ajouter un membre de famille
            </Button>
          </div>

          {/* Add Member Dialog */}
          <FamilyMemberDialog
            isOpen={isDialogOpen}
            onOpenChange={setIsDialogOpen}
            title="Ajouter un membre de famille"
            name={name}
            relation={relation}
            birthDate={birthDate}
            setName={setName}
            setRelation={setRelation}
            setBirthDate={setBirthDate}
            relationOptions={relationOptions}
            confirmText="Ajouter"
            onConfirm={handleAdd}
          />

          {/* Edit Member Dialog */}
          <FamilyMemberDialog
            isOpen={isEditDialogOpen}
            onOpenChange={setIsEditDialogOpen}
            title="Modifier le membre"
            name={name}
            relation={relation}
            birthDate={birthDate}
            setName={setName}
            setRelation={setRelation}
            setBirthDate={setBirthDate}
            relationOptions={relationOptions}
            confirmText="Enregistrer"
            onConfirm={handleEdit}
          />

          {/* Delete Confirmation Dialog */}
          <DeleteConfirmDialog
            isOpen={isDeleteDialogOpen}
            onOpenChange={setIsDeleteDialogOpen}
            title="Confirmer la suppression"
            description={`Êtes-vous sûr de vouloir supprimer ${selectedMember?.name} ?`}
            onConfirm={handleDelete}
          />
        </>
      )}
    </div>
  );
};

export default FamilyManager;
