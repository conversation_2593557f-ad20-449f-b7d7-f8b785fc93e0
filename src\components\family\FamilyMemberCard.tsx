
import React from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { User, Pencil, Trash2 } from "lucide-react";
import { FamilyMember } from "@/types";

interface FamilyMemberCardProps {
  member: FamilyMember;
  relationLabel?: string;
  onEdit: (member: FamilyMember) => void;
  onDelete: (member: FamilyMember) => void;
}

const FamilyMemberCard = ({ member, relationLabel, onEdit, onDelete }: FamilyMemberCardProps) => {
  return (
    <Card className="p-4 border-teal/20 hover:border-teal/40 transition-colors">
      <div className="flex justify-between items-start">
        <div className="flex items-center space-x-4">
          <div className="bg-teal/10 p-2 rounded-full">
            <User size={24} className="text-teal" />
          </div>
          <div>
            <p className="font-medium text-navy">{member.name}</p>
            {relationLabel && (
              <p className="text-sm text-gray-600">{relationLabel}</p>
            )}
            {member.birthDate && (
              <p className="text-sm text-gray-600">
                Né(e) le {new Date(member.birthDate).toLocaleDateString('fr-FR')}
              </p>
            )}
          </div>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onEdit(member)}
            className="text-teal hover:bg-teal/10"
          >
            <Pencil size={18} />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onDelete(member)}
            className="text-red-500 hover:bg-red-50"
          >
            <Trash2 size={18} />
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default FamilyMemberCard;
