name: medytrack_mobile
description: "MedyTrack - Professional Medicine Management Mobile Application"
publish_to: 'none'
version: 1.4.2+1

environment:
  sdk: '>=3.8.0 <4.0.0'
  flutter: ">=3.32.0"

dependencies:
  flutter:
    sdk: flutter

  # State Management (Group 1)
  flutter_bloc: ^9.1.1
  equatable: ^2.0.5

  # Backend & Storage (Group 2)
  supabase_flutter: ^2.6.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  flutter_secure_storage: ^9.2.2

  # Navigation & DI (Group 3)
  go_router: ^14.2.7
  get_it: ^7.7.0
  injectable: ^2.4.4

  # UI & Media (Group 4)
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.4.1

  # UI Components
  cupertino_icons: ^1.0.8

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting
  flutter_lints: ^6.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/
  
  # fonts:
  #   - family: Ubuntu
  #     fonts:
  #       - asset: assets/fonts/Ubuntu-Regular.ttf
  #       - asset: assets/fonts/Ubuntu-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Ubuntu-Bold.ttf
  #         weight: 700
  #       - asset: assets/fonts/Ubuntu-Light.ttf
  #         weight: 300