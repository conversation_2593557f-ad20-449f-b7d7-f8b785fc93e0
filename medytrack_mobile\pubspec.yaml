name: medytrack_mobile
description: "MedyTrack - Professional Medicine Management Mobile Application"
publish_to: 'none'
version: 1.4.2+1

environment:
  sdk: '>=3.8.0 <4.0.0'
  flutter: ">=3.32.0"

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_bloc: ^8.1.6
  equatable: ^2.0.5

  # Functional Programming
  dartz: ^0.10.1

  # Navigation
  go_router: ^14.2.7

  # Dependency Injection
  get_it: ^7.7.0
  injectable: ^2.4.4

  # Network & API
  supabase_flutter: ^2.6.0
  dio: ^5.7.0
  connectivity_plus: ^6.0.5

  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  flutter_secure_storage: ^9.2.2
  shared_preferences: ^2.3.2

  # UI Components
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.4.1
  shimmer: ^3.0.0
  lottie: ^3.1.2

  # Camera & Media
  camera: ^0.11.0+2
  image_picker: ^1.1.2
  image_cropper: ^8.0.2
  permission_handler: ^11.3.1

  # Barcode Scanning
  mobile_scanner: ^5.2.3
  
  # Authentication
  local_auth: ^2.3.0
  crypto: ^3.0.5
  
  # Notifications (commented out for initial setup)
  # firebase_core: ^3.6.0
  # firebase_messaging: ^15.1.3
  # flutter_local_notifications: ^19.3.0
  
  # Utils
  intl: ^0.20.2 # This was updated earlier and seems to be resolved.
  uuid: ^4.5.1
  path_provider: ^2.1.4
  url_launcher: ^6.3.1
  share_plus: ^11.0.0
  
  # Forms & Validation
  flutter_form_builder: ^10.1.0
  form_builder_validators: ^11.0.0
  
  # Charts & Analytics
  fl_chart: ^1.0.0
  
  # Performance
  flutter_cache_manager: ^3.4.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.13
  injectable_generator: ^2.6.2
  hive_generator: ^2.0.1 # <--- CHANGED TO THIS VERSION
  json_annotation: ^4.9.0
  json_serializable: ^6.8.0
  
  # Explicitly added analyzer and _fe_analyzer_shared are REMOVED again
  # Let pub resolve them based on other dependencies.

  # Linting
  flutter_lints: ^6.0.0
  
  # Testing
  bloc_test: ^10.0.0
  mocktail: ^1.0.4
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/
  
  fonts:
    - family: Ubuntu
      fonts:
        - asset: assets/fonts/Ubuntu-Regular.ttf
        - asset: assets/fonts/Ubuntu-Medium.ttf
          weight: 500
        - asset: assets/fonts/Ubuntu-Bold.ttf
          weight: 700
        - asset: assets/fonts/Ubuntu-Light.ttf
          weight: 300