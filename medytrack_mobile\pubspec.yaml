name: medytrack_mobile
description: "MedyTrack - Professional Medicine Management Mobile Application"
publish_to: 'none'
version: 1.4.2+1

environment:
  sdk: '>=3.8.0 <4.0.0'
  flutter: ">=3.32.0"

dependencies:
  flutter:
    sdk: flutter

  # UI Components
  cupertino_icons: ^1.0.8
  
  # Forms & Validation
  flutter_form_builder: ^10.1.0
  form_builder_validators: ^11.0.0
  
  # Charts & Analytics
  fl_chart: ^1.0.0
  
  # Performance
  flutter_cache_manager: ^3.4.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.13
  injectable_generator: ^2.6.2
  hive_generator: ^2.0.1 # <--- CHANGED TO THIS VERSION
  json_annotation: ^4.9.0
  json_serializable: ^6.8.0
  
  # Explicitly added analyzer and _fe_analyzer_shared are REMOVED again
  # Let pub resolve them based on other dependencies.

  # Linting
  flutter_lints: ^6.0.0
  
  # Testing
  bloc_test: ^10.0.0
  mocktail: ^1.0.4
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/
  
  fonts:
    - family: Ubuntu
      fonts:
        - asset: assets/fonts/Ubuntu-Regular.ttf
        - asset: assets/fonts/Ubuntu-Medium.ttf
          weight: 500
        - asset: assets/fonts/Ubuntu-Bold.ttf
          weight: 700
        - asset: assets/fonts/Ubuntu-Light.ttf
          weight: 300