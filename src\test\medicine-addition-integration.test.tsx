import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { BrowserRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import AddMedicine from '@/pages/AddMedicine'
import { AuthProvider } from '@/contexts/AuthContext'

// Enhanced mock for Supabase with presentation field
const mockSupabaseData = [
  {
    id: '1',
    nom: 'Levostamine',
    dosage: '5mg',
    forme: 'Comprimé',
    presentation: 'Boîte de 30 comprimés',
    laboratoire: 'Lab A',
    amm: 'AMM001',
    dci: 'DCI001',
    classe: 'Classe A',
    sous_classe: 'Sous-classe A'
  },
  {
    id: '2',
    nom: 'Levostamine',
    dosage: '5mg',
    forme: 'Comprimé',
    presentation: 'Flacon de 100ml',
    laboratoire: 'Lab A',
    amm: 'AMM002',
    dci: 'DCI001',
    classe: 'Classe A',
    sous_classe: 'Sous-classe A'
  }
]

// Mock Supabase with presentation data
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        or: vi.fn(() => ({
          limit: vi.fn(() => Promise.resolve({ 
            data: mockSupabaseData.map(item => ({
              name: item.nom,
              laboratoire: item.laboratoire,
              dci: item.dci,
              classe: item.classe
            })), 
            error: null 
          }))
        })),
        eq: vi.fn(() => ({
          order: vi.fn(() => Promise.resolve({ 
            data: mockSupabaseData, 
            error: null 
          }))
        }))
      }))
    }))
  }
}))

// Mock useUserMedicines hook
vi.mock('@/hooks/useUserMedicines', () => ({
  useUserMedicines: () => ({
    addMedicine: vi.fn().mockResolvedValue(true),
    medicines: [],
    isLoading: false,
    error: null
  })
}))

// Mock useLocations hook
vi.mock('@/hooks/useLocations', () => ({
  useLocations: () => ({
    locations: [
      { id: 'loc-1', name: 'Armoire à pharmacie', icon: 'home' }
    ],
    isLoading: false,
    error: null
  })
}))

// Mock useFamilyMembers hook
vi.mock('@/hooks/useFamilyMembers', () => ({
  useFamilyMembers: () => ({
    familyMembers: [
      { id: 'user-1', name: 'Test User' }
    ],
    isLoading: false,
    error: null
  })
}))

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <AuthProvider>
          {children}
        </AuthProvider>
      </BrowserRouter>
    </QueryClientProvider>
  )
}

describe('Medicine Addition Integration with Presentation Field', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should complete full medicine addition workflow with presentation', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <AddMedicine />
      </TestWrapper>
    )

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('Ajouter un médicament')).toBeInTheDocument()
    })

    // Use quick manual entry
    const quickEntryButton = screen.getByText('Entrée manuelle')
    await user.click(quickEntryButton)

    // Should show the form
    await waitFor(() => {
      expect(screen.getByText('Enregistrer')).toBeInTheDocument()
    })

    // Fill in medicine name
    const nameInput = screen.getByLabelText(/nom du médicament/i)
    await user.type(nameInput, 'Levostamine - 5mg - Comprimé - Boîte de 30')

    // Fill in quantity
    const quantityInput = screen.getByLabelText(/quantité/i)
    await user.clear(quantityInput)
    await user.type(quantityInput, '2')

    // Select location
    const locationSelect = screen.getByRole('combobox', { name: /emplacement/i })
    await user.click(locationSelect)
    await user.click(screen.getByText('Armoire à pharmacie'))

    // Submit form
    const submitButton = screen.getByText('Enregistrer')
    await user.click(submitButton)

    // Should call addMedicine with presentation data
    // Note: In a real test, we would verify the actual API call
    await waitFor(() => {
      // Form should be submitted successfully
      expect(submitButton).not.toBeDisabled()
    })
  })

  it('should handle medicine search and variant selection with presentation', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <AddMedicine />
      </TestWrapper>
    )

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('Ajouter un médicament')).toBeInTheDocument()
    })

    // Search for medicine
    const searchInput = screen.getByPlaceholderText(/rechercher un médicament/i)
    await user.type(searchInput, 'Levostamine')

    // Wait for search results
    await waitFor(() => {
      // Should show search results
      expect(screen.getByText(/recherche en cours/i) || screen.getByText('Levostamine')).toBeInTheDocument()
    }, { timeout: 3000 })

    // If search results appear, test variant selection
    const searchResults = screen.queryByText('Levostamine')
    if (searchResults) {
      await user.click(searchResults)

      // Should show variant selector with presentation
      await waitFor(() => {
        expect(screen.getByText('Présentation')).toBeInTheDocument()
      })

      // Select presentation
      const presentationSelect = screen.getByRole('combobox', { name: /présentation/i })
      await user.click(presentationSelect)
      
      // Should show presentation options
      await waitFor(() => {
        expect(screen.getByText('Boîte de 30 comprimés') || screen.getByText('Flacon de 100ml')).toBeInTheDocument()
      })
    }
  })

  it('should show manual entry fallback when no search results', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <AddMedicine />
      </TestWrapper>
    )

    // Search for non-existent medicine
    const searchInput = screen.getByPlaceholderText(/rechercher un médicament/i)
    await user.type(searchInput, 'NonExistentMedicine')

    // Wait for no results message
    await waitFor(() => {
      expect(screen.getByText(/aucun médicament trouvé/i) || screen.getByText(/créer.*manuellement/i)).toBeInTheDocument()
    }, { timeout: 3000 })

    // Should show manual entry option
    const manualEntryButton = screen.getByText(/créer.*manuellement/i)
    await user.click(manualEntryButton)

    // Should show form
    await waitFor(() => {
      expect(screen.getByText('Enregistrer')).toBeInTheDocument()
    })
  })

  it('should preserve presentation data through form submission', async () => {
    const user = userEvent.setup()
    
    // Mock the addMedicine function to capture the data
    const mockAddMedicine = vi.fn().mockResolvedValue(true)
    
    vi.mocked(require('@/hooks/useUserMedicines').useUserMedicines).mockReturnValue({
      addMedicine: mockAddMedicine,
      medicines: [],
      isLoading: false,
      error: null
    })

    render(
      <TestWrapper>
        <AddMedicine />
      </TestWrapper>
    )

    // Use manual entry
    const quickEntryButton = screen.getByText('Entrée manuelle')
    await user.click(quickEntryButton)

    // Fill form with presentation data
    const nameInput = screen.getByLabelText(/nom du médicament/i)
    await user.type(nameInput, 'Test Medicine - 10mg - Tablet - Box of 20')

    // Submit form
    const submitButton = screen.getByText('Enregistrer')
    await user.click(submitButton)

    // Verify that addMedicine was called with the correct data structure
    await waitFor(() => {
      expect(mockAddMedicine).toHaveBeenCalledWith(
        expect.objectContaining({
          custom_name: 'Test Medicine - 10mg - Tablet - Box of 20',
          is_custom: true,
          presentation: null // For custom medicines, presentation should be null
        })
      )
    })
  })
})
