
import React from "react";
import { Location } from "@/types";
import { Home, Luggage, Heart, Map } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface LocationSelectorProps {
  locations: Location[];
  selectedLocation: string;
  onLocationChange: (locationId: string) => void;
}

const LocationSelector = ({ locations, selectedLocation, onLocationChange }: LocationSelectorProps) => {
  const getLocationIcon = (iconName: string) => {
    switch (iconName) {
      case 'home':
        return <Home size={16} />;
      case 'luggage':
        return <Luggage size={16} />;
      case 'heart':
        return <Heart size={16} />;
      default:
        return <Map size={16} />;
    }
  };

  return (
    <Select value={selectedLocation} onValueChange={onLocationChange}>
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Choisir un emplacement" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">Tous les emplacements</SelectItem>
        {locations.map((location) => (
          <SelectItem key={location.id} value={location.id}>
            <div className="flex items-center">
              <span className="mr-2">{getLocationIcon(location.icon)}</span>
              {location.name}
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default LocationSelector;
