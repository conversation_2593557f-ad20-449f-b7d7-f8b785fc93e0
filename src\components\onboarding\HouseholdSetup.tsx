import React, { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Users, Plus, Home, CheckCircle, Mail, UserCheck, X } from "lucide-react";
import { toast } from "sonner";

interface HouseholdSetupProps {
  onComplete: () => void;
}

const HouseholdSetup = ({ onComplete }: HouseholdSetupProps) => {
  const { user, householdId, householdName, createHousehold, updateHouseholdName, supabase } = useAuth();
  const [mode, setMode] = useState<'invitation' | 'new'>('new');
  const [newHouseholdName, setNewHouseholdName] = useState('');
  const [editHouseholdName, setEditHouseholdName] = useState(householdName || '');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [pendingInvitation, setPendingInvitation] = useState<any>(null);
  const [isLoadingInvitation, setIsLoadingInvitation] = useState(true);

  useEffect(() => {
    // If user already has a household, show it as existing
    if (householdId) {
      setEditHouseholdName(householdName || '');
      onComplete(); // Skip this step if already has household
    } else {
      // Check for pending invitations
      checkForPendingInvitation();
    }
  }, [householdId, householdName]);

  const checkForPendingInvitation = async () => {
    if (!user?.email) return;

    try {
      setIsLoadingInvitation(true);
      const { data, error } = await supabase.rpc('get_pending_invitation_for_user', {
        user_email: user.email
      });

      if (error) {
        console.error('Error checking for invitations:', error);
        // If it's a column error (function not updated), just proceed with new household
        if (error.code === '42703' || error.message?.includes('email does not exist')) {
          console.log('Invitation function needs to be updated, proceeding with new household creation');
        }
        setMode('new');
        return;
      }

      if (data && data.length > 0) {
        setPendingInvitation(data[0]);
        setMode('invitation');
      } else {
        setMode('new');
      }
    } catch (err) {
      console.error('Error checking invitations:', err);
      setMode('new');
    } finally {
      setIsLoadingInvitation(false);
    }
  };

  const handleAcceptInvitation = async () => {
    if (!pendingInvitation || !user) return;

    setIsSubmitting(true);
    try {
      const { data, error } = await supabase.rpc('accept_invitation_during_onboarding', {
        p_invitation_id: pendingInvitation.id,
        p_user_id: user.id
      });

      if (error) throw error;

      if (data?.success) {
        toast.success(`Vous avez rejoint le foyer "${data.household_name}" avec succès!`);
        // Skip remaining onboarding steps since user joined existing household
        window.location.href = '/'; // Force full page reload to update auth context
      } else {
        toast.error(data?.error || 'Erreur lors de l\'acceptation de l\'invitation');
      }
    } catch (err) {
      console.error('Error accepting invitation:', err);
      toast.error('Erreur lors de l\'acceptation de l\'invitation');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeclineInvitation = () => {
    setPendingInvitation(null);
    setMode('new');
    toast.info('Invitation déclinée. Vous pouvez maintenant créer votre propre foyer.');
  };

  const handleCreateHousehold = async () => {
    if (!newHouseholdName.trim()) {
      toast.error('Veuillez saisir un nom pour votre foyer');
      return;
    }

    setIsSubmitting(true);
    try {
      const { error } = await createHousehold(newHouseholdName.trim());
      if (!error) {
        toast.success('Foyer créé avec succès!');
        onComplete();
      }
    } catch (err) {
      console.error('Error creating household:', err);
      toast.error('Erreur lors de la création du foyer');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateHousehold = async () => {
    if (!editHouseholdName.trim()) {
      toast.error('Veuillez saisir un nom pour votre foyer');
      return;
    }

    if (editHouseholdName.trim() === householdName) {
      // No change needed
      onComplete();
      return;
    }

    setIsSubmitting(true);
    try {
      const { error } = await updateHouseholdName(editHouseholdName.trim());
      if (!error) {
        toast.success('Nom du foyer mis à jour!');
        onComplete();
      }
    } catch (err) {
      console.error('Error updating household:', err);
      toast.error('Erreur lors de la mise à jour du foyer');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmit = () => {
    if (mode === 'new') {
      handleCreateHousehold();
    } else if (mode === 'invitation') {
      handleAcceptInvitation();
    }
  };

  const canProceed = () => {
    if (mode === 'new') {
      return newHouseholdName.trim().length > 0;
    }
    return true;
  };

  if (isLoadingInvitation) {
    return (
      <div className="text-center space-y-4">
        <div className="w-8 h-8 border-2 border-teal border-t-transparent rounded-full animate-spin mx-auto"></div>
        <p className="text-gray-600">Vérification des invitations en cours...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-navy/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <Users className="w-8 h-8 text-navy" />
        </div>
        <h2 className="text-xl font-bold text-navy mb-2">
          Configurez votre foyer
        </h2>
        <p className="text-gray-600">
          Un foyer vous permet de partager la gestion des médicaments avec votre famille.
        </p>
      </div>

      {mode === 'invitation' && pendingInvitation ? (
        // User has a pending invitation
        <Card className="border-teal/20">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-navy">
              <Mail className="w-5 h-5" />
              Invitation reçue
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-teal/10 p-4 rounded-lg">
              <h3 className="font-medium text-navy mb-2">
                Vous êtes invité(e) à rejoindre le foyer "{pendingInvitation.household_name}"
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                Invité par: {pendingInvitation.invited_by_email}
              </p>

              <div className="text-sm text-gray-600">
                <p className="font-medium mb-1">Permissions accordées:</p>
                <ul className="space-y-1">
                  {pendingInvitation.permissions?.can_add_medicines && (
                    <li>• Ajouter des médicaments</li>
                  )}
                  {pendingInvitation.permissions?.can_edit_medicines && (
                    <li>• Modifier des médicaments</li>
                  )}
                  {pendingInvitation.permissions?.can_delete_medicines && (
                    <li>• Supprimer des médicaments</li>
                  )}
                  {pendingInvitation.permissions?.can_manage_family && (
                    <li>• Gérer les membres de la famille</li>
                  )}
                  {pendingInvitation.permissions?.can_invite_others && (
                    <li>• Inviter d'autres membres</li>
                  )}
                </ul>
              </div>
            </div>

            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={handleDeclineInvitation}
                disabled={isSubmitting}
                className="flex-1"
              >
                <X className="w-4 h-4 mr-2" />
                Décliner
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="flex-1 bg-teal hover:bg-teal-dark text-white"
              >
                {isSubmitting ? (
                  'Acceptation...'
                ) : (
                  <>
                    <UserCheck className="w-4 h-4 mr-2" />
                    Accepter
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        // User doesn't have a household and no invitation - create new
        <Card className="border-teal/20">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-navy">
              <Plus className="w-5 h-5" />
              Créer un nouveau foyer
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="new-household-name">Nom du foyer</Label>
              <Input
                id="new-household-name"
                value={newHouseholdName}
                onChange={(e) => setNewHouseholdName(e.target.value)}
                placeholder="Ex: Famille Dupont"
                disabled={isSubmitting}
              />
              <p className="text-xs text-gray-500 mt-1">
                Choisissez un nom qui représente votre famille ou votre domicile
              </p>
            </div>

            <Button
              onClick={handleSubmit}
              disabled={!canProceed() || isSubmitting}
              className="w-full bg-teal hover:bg-teal-dark text-white"
            >
              {isSubmitting ? 'Création...' : 'Créer le foyer'}
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default HouseholdSetup;
