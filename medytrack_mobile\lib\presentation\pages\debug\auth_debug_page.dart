import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../bloc/auth/auth_bloc.dart' as auth;
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/config/app_config.dart';

class AuthDebugPage extends StatefulWidget {
  const AuthDebugPage({super.key});

  @override
  State<AuthDebugPage> createState() => _AuthDebugPageState();
}

class _AuthDebugPageState extends State<AuthDebugPage> {
  final _emailController = TextEditingController(text: AppConfig.testUserEmail);
  final _passwordController = TextEditingController(
    text: AppConfig.testUserPassword,
  );
  final _nameController = TextEditingController(text: 'Test User');

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Authentication Debug'),
        backgroundColor: AppColors.navy,
        foregroundColor: Colors.white,
      ),
      body: BlocBuilder<auth.AuthBloc, auth.AuthState>(
        builder: (context, state) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Current Auth State
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Current Authentication State',
                          style: AppTextStyles.headlineSmall.copyWith(
                            color: AppColors.navy,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        _buildStateInfo(state),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 20),

                // Supabase Info
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Supabase Configuration',
                          style: AppTextStyles.headlineSmall.copyWith(
                            color: AppColors.navy,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        _buildSupabaseInfo(),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 20),

                // Test Authentication
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Test Authentication',
                          style: AppTextStyles.headlineSmall.copyWith(
                            color: AppColors.navy,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Email Field
                        TextField(
                          controller: _emailController,
                          decoration: const InputDecoration(
                            labelText: 'Email',
                            border: OutlineInputBorder(),
                          ),
                        ),
                        const SizedBox(height: 12),

                        // Password Field
                        TextField(
                          controller: _passwordController,
                          obscureText: true,
                          decoration: const InputDecoration(
                            labelText: 'Password',
                            border: OutlineInputBorder(),
                          ),
                        ),
                        const SizedBox(height: 12),

                        // Name Field
                        TextField(
                          controller: _nameController,
                          decoration: const InputDecoration(
                            labelText: 'Name (for sign up)',
                            border: OutlineInputBorder(),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Action Buttons
                        Row(
                          children: [
                            Expanded(
                              child: ElevatedButton(
                                onPressed: state is auth.AuthLoading
                                    ? null
                                    : _signUp,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.teal,
                                  foregroundColor: Colors.white,
                                ),
                                child: const Text('Sign Up'),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: ElevatedButton(
                                onPressed: state is auth.AuthLoading
                                    ? null
                                    : _signIn,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.navy,
                                  foregroundColor: Colors.white,
                                ),
                                child: const Text('Sign In'),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),

                        // Additional Actions
                        Row(
                          children: [
                            Expanded(
                              child: ElevatedButton(
                                onPressed: state is auth.AuthLoading
                                    ? null
                                    : _signOut,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red,
                                  foregroundColor: Colors.white,
                                ),
                                child: const Text('Sign Out'),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: ElevatedButton(
                                onPressed: state is auth.AuthLoading
                                    ? null
                                    : _checkAuth,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.grey,
                                  foregroundColor: Colors.white,
                                ),
                                child: const Text('Check Auth'),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),

                        // Profile Completion Actions
                        if (state is auth.AuthOnboardingRequired) ...[
                          ElevatedButton(
                            onPressed: state is auth.AuthLoading
                                ? null
                                : _completeProfile,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.purple,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('🚀 Complete Profile (Simulate)'),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                if (state is auth.AuthLoading)
                  const Padding(
                    padding: EdgeInsets.all(16),
                    child: Center(child: CircularProgressIndicator()),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStateInfo(auth.AuthState state) {
    String stateText;
    Color stateColor;
    Widget? additionalInfo;

    if (state is auth.AuthInitial) {
      stateText = 'Initial - Not checked yet';
      stateColor = Colors.grey;
    } else if (state is auth.AuthLoading) {
      stateText = 'Loading - Checking authentication...';
      stateColor = Colors.blue;
    } else if (state is auth.AuthAuthenticated) {
      stateText = 'Authenticated - User: ${state.user.email}';
      stateColor = Colors.green;
      additionalInfo = _buildProfileCompletenessInfo(state.user);
    } else if (state is auth.AuthUnauthenticated) {
      stateText = 'Unauthenticated - No user logged in';
      stateColor = Colors.orange;
    } else if (state is auth.AuthOnboardingRequired) {
      stateText = 'Onboarding Required - User: ${state.user.email}';
      stateColor = Colors.purple;
      additionalInfo = _buildProfileCompletenessInfo(state.user);
    } else if (state is auth.AuthError) {
      stateText = 'Error - ${state.message}';
      stateColor = Colors.red;
    } else {
      stateText = 'Unknown state: ${state.runtimeType}';
      stateColor = Colors.grey;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: stateColor.withOpacity(0.1),
            border: Border.all(color: stateColor),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            stateText,
            style: AppTextStyles.bodyMedium.copyWith(
              color: stateColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        if (additionalInfo != null) ...[
          const SizedBox(height: 12),
          additionalInfo,
        ],
      ],
    );
  }

  Widget _buildProfileCompletenessInfo(dynamic user) {
    final status = user.profileCompletenessStatus;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        border: Border.all(color: Colors.blue),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Profile Completeness Status:',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 8),
          ...status.entries.map((entry) {
            final value = entry.value;
            final displayValue = value is bool
                ? (value ? '✅ Yes' : '❌ No')
                : (value?.toString() ?? 'null');

            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Text(
                '${entry.key}: $displayValue',
                style: AppTextStyles.bodySmall,
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildSupabaseInfo() {
    final client = Supabase.instance.client;
    final currentUser = client.auth.currentUser;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('URL: ${AppConfig.supabaseUrl}'),
        const SizedBox(height: 8),
        Text('Current User: ${currentUser?.email ?? 'None'}'),
        const SizedBox(height: 8),
        Text('Session: ${currentUser != null ? 'Active' : 'None'}'),
      ],
    );
  }

  void _signUp() {
    context.read<auth.AuthBloc>().add(
      auth.AuthSignUpRequested(
        email: _emailController.text,
        password: _passwordController.text,
        name: _nameController.text,
      ),
    );
  }

  void _signIn() {
    context.read<auth.AuthBloc>().add(
      auth.AuthSignInRequested(
        email: _emailController.text,
        password: _passwordController.text,
      ),
    );
  }

  void _signOut() {
    context.read<auth.AuthBloc>().add(auth.AuthSignOutRequested());
  }

  void _checkAuth() {
    context.read<auth.AuthBloc>().add(auth.AuthCheckRequested());
  }

  void _completeProfile() {
    // Simulate completing the profile by updating user metadata
    context.read<auth.AuthBloc>().add(
      auth.AuthUpdateProfileRequested(
        name: _nameController.text.isNotEmpty
            ? _nameController.text
            : 'Test User',
        householdId: 'test-household-id',
        householdName: 'Test Household',
        isOnboardingCompleted: true,
      ),
    );
  }
}
