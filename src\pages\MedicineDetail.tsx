
import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import ModernHeader from "@/components/ModernHeader";
import BottomNavigation from "@/components/BottomNavigation";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Trash2, Share, Edit, Pill } from "lucide-react";
import { formatDate, isExpired, isNearExpiry } from "@/utils/helpers";
import { formatCompleteMedicineName } from "@/utils/medicineUtils";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useUserMedicines } from "@/hooks/useUserMedicines";
import { useLocations } from "@/hooks/useLocations";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";

interface MedicineDetails {
  id: string;
  name: string;
  dosage?: string;
  quantity: number;
  expiryDate: string;
  category: string;
  location?: string;
  locationName?: string;
  notes?: string;
  barcode?: string;
  createdAt: string;
  updatedAt: string;
  isCustom: boolean;
  familyMember?: {
    id: string;
    name: string;
  } | null;
}

const MedicineDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { locations } = useLocations();
  const { deleteMedicine } = useUserMedicines();
  const { user, householdId } = useAuth();
  const [medicine, setMedicine] = useState<MedicineDetails | null>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const fetchMedicine = async () => {
      if (!id || !householdId) return;
      
      try {
        // First, get basic information from the dashboard view
        const { data: viewData, error: viewError } = await supabase
          .from('dashboard_medicine_alerts_view')
          .select('*')
          .eq('user_medicine_id', id)
          .eq('household_id', householdId)
          .maybeSingle();
          
        if (viewError) {
          throw viewError;
        }
        
        if (!viewData) {
          toast({
            title: "Médicament non trouvé",
            description: "Ce médicament n'existe pas ou a été supprimé."
          });
          return;
        }
        
        // Get additional details from user_medicines table which has category, notes, etc.
        const { data: medicineData, error: medicineError } = await supabase
          .from('user_medicines')
          .select('*')
          .eq('id', id)
          .eq('household_id', householdId)
          .maybeSingle();
          
        if (medicineError) {
          console.error('Error fetching additional medicine details:', medicineError);
        }
        
        // Get the appropriate name based on whether it's a custom medicine
        const medicineName = viewData.is_custom
          ? viewData.custom_name
          : (viewData.medicine_name || viewData.official_label || 'Médicament inconnu');

        const medicineData_temp = {
          id: viewData.user_medicine_id,
          name: medicineName,
          dosage: viewData.dosage || '',
          form: viewData.forme || '',
          quantity: viewData.quantity || 0,
          expiryDate: viewData.expiration || new Date().toISOString().split('T')[0],
          category: medicineData?.category || 'other',
          location: viewData.location_id || '',
          locationName: viewData.location_name || 'Non spécifié',
          notes: medicineData?.notes || '',
          barcode: viewData.amm || undefined,
          createdAt: medicineData?.created_at || new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          is_custom: viewData.is_custom || false,
          custom_name: viewData.custom_name,
          familyMember: viewData.family_member_id ? {
            id: viewData.family_member_id,
            name: viewData.family_member_name || 'Non spécifié'
          } : null
        };

        // Fetch indications from tunisia_medicines if it's not a custom medicine
        let indications = '';
        if (!viewData.is_custom && viewData.medicine_id) {
          const { data: tunisiaMedicine } = await supabase
            .from('tunisia_medicines')
            .select('indications')
            .eq('id', viewData.medicine_id)
            .maybeSingle();

          indications = tunisiaMedicine?.indications || '';
        }

        setMedicine({
          id: medicineData_temp.id,
          name: formatCompleteMedicineName(medicineData_temp as any),
          dosage: medicineData_temp.dosage,
          quantity: medicineData_temp.quantity,
          expiryDate: medicineData_temp.expiryDate,
          category: medicineData_temp.category,
          location: medicineData_temp.location,
          locationName: medicineData_temp.locationName,
          notes: medicineData_temp.notes,
          barcode: medicineData_temp.barcode,
          createdAt: medicineData_temp.createdAt,
          updatedAt: medicineData_temp.updatedAt,
          isCustom: medicineData_temp.is_custom,
          familyMember: medicineData_temp.familyMember,
          indications: indications
        });
      } catch (err) {
        console.error('Error fetching medicine:', err);
        toast({
          title: "Erreur",
          description: "Impossible de charger les détails du médicament."
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchMedicine();
  }, [id, householdId]);
  
  if (loading) {
    return (
      <div className="medicinet-container flex justify-center items-center h-screen">
        <Loader2 className="animate-spin h-8 w-8 text-primary" />
      </div>
    );
  }
  
  if (!medicine) {
    return (
      <div className="medicinet-container">
        <ModernHeader
          title="Médicament non trouvé"
          subtitle="Ce médicament n'existe pas ou a été supprimé"
          showBackButton={true}
          variant="navy"
        />
        <div className="text-center py-10">
          <p>Ce médicament n'existe pas ou a été supprimé.</p>
          <Button onClick={() => navigate("/")} className="mt-4">
            Retour à l'accueil
          </Button>
        </div>
      </div>
    );
  }
  
  const location = locations.find(loc => loc.id === medicine?.location);
  
  const handleShare = () => {
    toast({
      title: "Fonction démo",
      description: "Le partage sera disponible dans une future version.",
    });
  };

  const handleEdit = () => {
    navigate(`/edit-medicine/${medicine?.id}`);
  };

  const handleDelete = async () => {
    if (medicine && await deleteMedicine(medicine.id)) {
      toast({
        title: "Médicament supprimé",
        description: `${medicine.name} a été supprimé de votre armoire à pharmacie.`,
      });
      navigate("/");
    }
  };
  
  return (
    <div className="medicinet-container animate-fade-in pb-20">
      <ModernHeader
        title="Détails du médicament"
        subtitle="Informations complètes sur votre médicament"
        showBackButton={true}
        variant="navy"
      />

      <div className="bg-white rounded-2xl p-6 shadow-md mb-6 border border-teal/20">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-3">
            <div className="bg-teal/10 p-3 rounded-full">
              <Pill size={24} className="text-teal" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-navy">{medicine?.name}</h2>
            </div>
          </div>
          
          <div>
            {medicine && isExpired(medicine.expiryDate) && <Badge variant="destructive">Expiré</Badge>}
            {medicine && isNearExpiry(medicine.expiryDate) && !isExpired(medicine.expiryDate) && <Badge variant="outline" className="bg-amber-100 text-amber-800">Expire bientôt</Badge>}
          </div>
        </div>
        
        <div className="mt-6 space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600">Quantité</p>
              <p className="font-medium text-navy">{medicine?.quantity} {medicine?.quantity > 1 ? 'unités' : 'unité'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Expiration</p>
              <p className="font-medium text-navy">{medicine && formatDate(medicine.expiryDate)}</p>
            </div>
          </div>
          
          <div>
            <p className="text-sm text-gray-600">Emplacement</p>
            <p className="font-medium text-navy">{medicine?.locationName || 'Non spécifié'}</p>
          </div>

          {medicine?.familyMember && (
            <div>
              <p className="text-sm text-gray-600">Membre de famille</p>
              <p className="font-medium text-navy">{medicine.familyMember.name || 'Non spécifié'}</p>
            </div>
          )}

          <div>
            <p className="text-sm text-gray-600">Catégorie</p>
            <p className="font-medium capitalize text-navy">
              {medicine?.category === 'pain' && 'Douleur'}
              {medicine?.category === 'cold' && 'Rhume'}
              {medicine?.category === 'allergy' && 'Allergie'}
              {medicine?.category === 'digestion' && 'Digestion'}
              {medicine?.category === 'first-aid' && 'Premiers soins'}
              {medicine?.category === 'prescription' && 'Ordonnance'}
              {medicine?.category === 'other' && 'Autre'}
            </p>
          </div>
          
          {medicine?.notes && (
            <div>
              <p className="text-sm text-gray-600">Notes</p>
              <p className="text-navy">{medicine.notes}</p>
            </div>
          )}

          {medicine?.indications && (
            <div>
              <p className="text-sm text-gray-600">Indications thérapeutiques</p>
              <p className="text-navy">{medicine.indications}</p>
            </div>
          )}
        </div>
      </div>
      
      <div className="flex gap-4 mb-24">
        <Button className="flex-1 bg-teal hover:bg-teal-dark text-white" onClick={handleEdit}>
          <Edit size={18} className="mr-2" />
          Modifier
        </Button>
        <Button variant="outline" className="flex-1 border-teal text-teal hover:bg-teal/10" onClick={handleShare}>
          <Share size={18} className="mr-2" />
          Partager
        </Button>
        <Button variant="destructive" size="icon" onClick={handleDelete}>
          <Trash2 size={18} />
        </Button>
      </div>
      
      <BottomNavigation />
    </div>
  );
};

export default MedicineDetail;
