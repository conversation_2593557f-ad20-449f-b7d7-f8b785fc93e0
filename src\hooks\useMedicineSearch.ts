
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Medicine } from '@/types';

export interface MedicineVariant {
  id: string;
  nom: string;
  dosage: string;
  forme: string;
  presentation: string;
  laboratoire: string;
  amm: string;
  dci: string;
  classe: string;
  sous_classe: string;
}

export const useMedicineSearch = () => {
  const [medicines, setMedicines] = useState<Medicine[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [medicineVariants, setMedicineVariants] = useState<MedicineVariant[]>([]);
  const [isLoadingVariants, setIsLoadingVariants] = useState(false);

  const searchMedicines = async (query: string) => {
    if (query.trim().length < 2) {
      setMedicines([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const { data, error: supabaseError } = await supabase
        .from('tunisia_medicines')
        .select(`
          nom,
          laboratoire,
          dci,
          classe
        `)
        .or(`nom.ilike.%${query}%,amm.eq.${query}`)
        .limit(50);

      if (supabaseError) throw supabaseError;

      // Group by medicine name to avoid duplicates
      const uniqueMedicines = new Map();
      (data || []).forEach((item) => {
        const key = item.nom?.toLowerCase();
        if (key && !uniqueMedicines.has(key)) {
          uniqueMedicines.set(key, {
            id: key, // Use name as temporary ID for search results
            name: item.nom || 'Unknown',
            barcode: '',
            form: '',
            holder: item.laboratoire || '',
            dosage: '',
            quantity: 1,
            expiryDate: new Date().toISOString().split('T')[0],
            category: 'other',
            location: '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            notes: `${item.dci ? 'DCI: ' + item.dci : ''}${item.classe ? ', Classe: ' + item.classe : ''}`
          });
        }
      });

      setMedicines(Array.from(uniqueMedicines.values()).slice(0, 10));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setMedicines([]);
    } finally {
      setIsLoading(false);
    }
  };

  const getMedicineVariants = async (medicineName: string) => {
    if (!medicineName) {
      setMedicineVariants([]);
      return;
    }

    setIsLoadingVariants(true);
    setError(null);

    try {
      const { data, error: supabaseError } = await supabase
        .from('tunisia_medicines')
        .select(`
          id,
          nom,
          dosage,
          forme,
          presentation,
          laboratoire,
          amm,
          dci,
          classe,
          sous_classe
        `)
        .eq('nom', medicineName)
        .order('dosage', { ascending: true })
        .order('forme', { ascending: true })
        .order('presentation', { ascending: true });

      if (supabaseError) throw supabaseError;

      setMedicineVariants(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setMedicineVariants([]);
    } finally {
      setIsLoadingVariants(false);
    }
  };

  return {
    medicines,
    searchMedicines,
    isLoading,
    error,
    medicineVariants,
    getMedicineVariants,
    isLoadingVariants
  };
};
