import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Calendar,
  Package,
  AlertTriangle,
  Clock,
  ChevronDown,
  ChevronUp,
  MapPin,
  Users,
  Eye,
  Plus
} from "lucide-react";
import { GroupedMedicine } from "@/utils/medicineUtils";
import { formatDateShort, isExpired, isNearExpiry } from "@/utils/helpers";
import { formatCompleteMedicineName } from "@/utils/medicineUtils";
import { cn } from "@/lib/utils";
import { getMedicineIcon } from "@/utils/medicineIcons";
import { Medicine } from "@/types";

interface GroupedMedicineCardProps {
  groupedMedicine: GroupedMedicine;
  onClick: () => void;
  onMedicineClick?: (medicine: Medicine) => void;
  showExpandButton?: boolean;
  defaultExpanded?: boolean;
}

const GroupedMedicineCard = ({
  groupedMedicine,
  onClick,
  onMedicineClick,
  showExpandButton = true,
  defaultExpanded = false
}: GroupedMedicineCardProps) => {
  const {
    name,
    dosage,
    form,
    presentation,
    totalQuantity,
    earliestExpiry,
    hasMultipleExpiries,
    medicines
  } = groupedMedicine;

  // State for expand/collapse functionality
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  const hasMultipleMedicines = medicines.length > 1;

  // Determine expiry status based on earliest expiry
  const isEarliestExpired = isExpired(earliestExpiry);
  const isEarliestNearExpiry = isNearExpiry(earliestExpiry);
  
  // Get status colors and text using MedyTrack's design system (updated border styling)
  const getExpiryStatus = () => {
    if (isEarliestExpired) {
      return {
        borderColor: 'border-l-4 border-red-500',
        hoverBg: 'hover:bg-red-50',
        text: 'Expiré',
        icon: AlertTriangle,
        badgeClass: 'bg-red-100 text-red-800 text-sm font-bold'
      };
    }
    if (isEarliestNearExpiry) {
      return {
        borderColor: 'border-l-4 border-amber-500',
        hoverBg: 'hover:bg-amber-50',
        text: 'Expire bientôt',
        icon: Clock,
        badgeClass: 'bg-amber-100 text-amber-800 text-sm font-bold'
      };
    }
    return {
      borderColor: 'border-l-4 border-teal',
      hoverBg: 'hover:bg-teal/5',
      text: 'Valide',
      icon: Calendar,
      badgeClass: 'bg-green-100 text-green-800 text-sm font-bold'
    };
  };

  const status = getExpiryStatus();
  const StatusIcon = status.icon;
  const MedicineIcon = getMedicineIcon(form || '');

  // Get medicine name only (without dosage and form)
  const getMedicineName = () => {
    return name || "Médicament";
  };

  // Get dosage and form separately
  const getDosageAndForm = () => {
    const parts = [];
    if (dosage) parts.push(dosage);
    if (form) parts.push(form);
    return parts.join(" - ");
  };

  // Handle expand/collapse toggle
  const handleExpandToggle = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering the card onClick
    setIsExpanded(!isExpanded);
  };

  // Handle individual medicine click
  const handleMedicineClick = (medicine: Medicine, e: React.MouseEvent) => {
    e.stopPropagation();
    if (onMedicineClick) {
      onMedicineClick(medicine);
    }
  };

  // Get category display name in French
  const getCategoryLabel = (category: string) => {
    const categoryMap: Record<string, string> = {
      'pain': 'Douleur',
      'cold': 'Rhume',
      'allergy': 'Allergie',
      'digestion': 'Digestion',
      'first-aid': 'Premiers soins',
      'prescription': 'Ordonnance',
      'other': 'Autre'
    };
    return categoryMap[category] || category;
  };

  return (
    <Card
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-md mb-3",
        status.borderColor,
        status.hoverBg
      )}
      onClick={onClick}
    >
      <CardContent className="p-4">
        {/* Primary Information Section */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3 flex-1">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 rounded-full bg-teal/10 flex items-center justify-center">
                <MedicineIcon size={24} className="text-teal" />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              {/* Medicine Name - Bold, 18px, Navy */}
              <h3 className="font-bold text-navy text-lg leading-tight mb-1">
                {getMedicineName()}
              </h3>

              {/* Dosage and Form - Bold, 14px, Black */}
              {getDosageAndForm() && (
                <p className="font-bold text-black text-sm leading-tight">
                  {getDosageAndForm()}
                </p>
              )}

              {/* Presentation */}
              {presentation && (
                <p className="text-sm text-gray-600 truncate mt-1">
                  {presentation}
                </p>
              )}
            </div>
          </div>

          {/* Status Indicator - High Visibility */}
          <div className="flex flex-col items-end gap-1 ml-3">
            <Badge
              variant="secondary"
              className={cn(status.badgeClass)}
            >
              <StatusIcon size={12} className="mr-1" />
              {status.text}
            </Badge>

            {/* Expiration Date - MM/YY format */}
            <div className="text-xs text-gray-600">
              {formatDateShort(earliestExpiry)}
            </div>

            {hasMultipleMedicines && (
              <Badge variant="outline" className="text-xs">
                {medicines.length} entrées
              </Badge>
            )}
            {groupedMedicine.isCustom && (
              <Badge className="bg-teal/10 text-teal border-teal/20 text-xs">
                Manuel
              </Badge>
            )}
          </div>
        </div>

        {/* Secondary Information Section */}
        <div className="space-y-2">
          {/* Quantity and Location Row */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Package size={16} className="text-teal" />
              <span className="text-sm font-medium text-black">
                {totalQuantity} unité{totalQuantity > 1 ? 's' : ''}
              </span>
            </div>

            <div className="flex items-center gap-1">
              <MapPin size={14} className="text-teal" />
              <span className="text-sm text-black">
                {(() => {
                  const uniqueLocations = new Set(medicines.map(med => med.locationName || 'Non spécifié'));
                  if (uniqueLocations.size === 1) {
                    return Array.from(uniqueLocations)[0];
                  }
                  return `${uniqueLocations.size} emplacements`;
                })()}
              </span>
            </div>
          </div>

          {/* Multiple Expiry Indicator and Expand Button Row */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {hasMultipleExpiries && (
                <div className="flex items-center gap-1 text-xs text-gray-600">
                  <Calendar size={12} className="text-teal" />
                  <Plus size={8} className="text-teal" />
                  <span>Dates multiples</span>
                </div>
              )}
            </div>

            {hasMultipleMedicines && showExpandButton && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleExpandToggle}
                className="h-8 px-2 text-teal hover:text-teal hover:bg-teal/10"
              >
                <Eye size={14} className="mr-1" />
                {isExpanded ? 'Masquer' : 'Détails'}
                {isExpanded ? <ChevronUp size={14} className="ml-1" /> : <ChevronDown size={14} className="ml-1" />}
              </Button>
            )}
          </div>
        </div>

        {/* Multiple expiry warning */}
        {hasMultipleExpiries && !isExpanded && (
          <div className="mt-3 p-2 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-xs text-blue-800 flex items-center gap-1">
              <AlertTriangle size={12} />
              Plusieurs dates d'expiration pour ce médicament
            </p>
          </div>
        )}

        {/* Expanded view with individual medicines */}
        {isExpanded && hasMultipleMedicines && (
          <div className="mt-4 border-t border-gray-200 pt-4">
            <h4 className="text-sm font-semibold text-navy mb-3 flex items-center gap-2">
              <Package size={14} />
              Détail des entrées ({medicines.length})
            </h4>
            <div className="space-y-2">
              {medicines.map((medicine, index) => {
                const medicineExpired = isExpired(medicine.expiryDate);
                const medicineNearExpiry = isNearExpiry(medicine.expiryDate);
                const lowStock = medicine.quantity <= 2;
                const outOfStock = medicine.quantity === 0;

                return (
                  <div
                    key={medicine.id}
                    className={cn(
                      "p-3 rounded-lg border cursor-pointer transition-all duration-200",
                      medicineExpired ? "border-red-200 bg-red-50 hover:bg-red-100" :
                      medicineNearExpiry ? "border-amber-200 bg-amber-50 hover:bg-amber-100" :
                      "border-gray-200 bg-gray-50 hover:bg-gray-100"
                    )}
                    onClick={(e) => handleMedicineClick(medicine, e)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-xs font-medium text-gray-500">
                            #{index + 1}
                          </span>
                          <span className={cn(
                            "text-sm font-medium",
                            medicineExpired ? "text-red-700" :
                            medicineNearExpiry ? "text-amber-700" :
                            "text-navy"
                          )}>
                            {medicine.quantity} unité{medicine.quantity > 1 ? 's' : ''}
                          </span>
                          <span className="text-xs text-gray-500">•</span>
                          <span className={cn(
                            "text-sm",
                            medicineExpired ? "text-red-600" :
                            medicineNearExpiry ? "text-amber-600" :
                            "text-gray-700"
                          )}>
                            Exp: {formatDateShort(medicine.expiryDate)}
                          </span>
                        </div>

                        <div className="flex items-center gap-4 text-xs text-gray-600">
                          <div className="flex items-center gap-1">
                            <MapPin size={12} />
                            <span>{medicine.locationName || 'Non spécifié'}</span>
                          </div>

                          {medicine.familyMember && (
                            <div className="flex items-center gap-1">
                              <Users size={12} />
                              <span>{medicine.familyMember.name}</span>
                            </div>
                          )}

                          <div className="bg-gray-200 px-2 py-0.5 rounded-full">
                            {getCategoryLabel(medicine.category)}
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col items-end gap-1">
                        {medicineExpired && (
                          <Badge variant="destructive" className="text-xs">
                            Expiré
                          </Badge>
                        )}
                        {medicineNearExpiry && !medicineExpired && (
                          <Badge className="bg-amber-100 text-amber-800 border-amber-200 text-xs">
                            Expire bientôt
                          </Badge>
                        )}
                        {(outOfStock || lowStock) && (
                          <Badge variant="outline" className={cn(
                            "text-xs",
                            outOfStock ? "border-red-300 text-red-700" : "border-amber-300 text-amber-700"
                          )}>
                            {outOfStock ? "Rupture" : "Stock bas"}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default GroupedMedicineCard;
