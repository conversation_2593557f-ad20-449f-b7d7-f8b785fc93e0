
import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON><PERSON>ooter,
  DialogClose
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import FamilyMemberForm from "./FamilyMemberForm";

interface FamilyMemberDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  name: string;
  relation: string;
  birthDate: string;
  setName: (name: string) => void;
  setRelation: (relation: string) => void;
  setBirthDate: (birthDate: string) => void;
  relationOptions: Array<{ value: string; label: string; }>;
  confirmText: string;
  onConfirm: () => void;
}

const FamilyMemberDialog = ({
  isOpen,
  onOpenChange,
  title,
  name,
  relation,
  birthDate,
  setName,
  setRelation,
  setBirthDate,
  relationOptions,
  confirmText,
  onConfirm
}: FamilyMemberDialogProps) => {
  return (
    <Dialog open={isOpen} onO<PERSON>Change={onO<PERSON><PERSON>hange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-navy">{title}</DialogTitle>
        </DialogHeader>
        <FamilyMemberForm
          name={name}
          relation={relation}
          birthDate={birthDate}
          setName={setName}
          setRelation={setRelation}
          setBirthDate={setBirthDate}
          relationOptions={relationOptions}
        />
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline" className="border-gray-300">Annuler</Button>
          </DialogClose>
          <Button onClick={onConfirm} className="bg-teal hover:bg-teal-dark text-white">{confirmText}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default FamilyMemberDialog;
