import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../entities/medicine.dart';
import '../../repositories/medicine_repository.dart';

class AddMedicineUseCase {
  final MedicineRepository repository;

  AddMedicineUseCase(this.repository);

  Future<Either<Failure, Medicine>> call(Medicine medicine) async {
    return await repository.addMedicine(medicine);
  }
}
