part of 'auth_bloc.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

/// Event to check current authentication status
class AuthCheckRequested extends AuthEvent {}

/// Event to sign in with email and password
class AuthSignInRequested extends AuthEvent {
  final String email;
  final String password;

  const AuthSignInRequested({
    required this.email,
    required this.password,
  });

  @override
  List<Object?> get props => [email, password];
}

/// Event to sign up with email, password, and name
class AuthSignUpRequested extends AuthEvent {
  final String email;
  final String password;
  final String name;

  const AuthSignUpRequested({
    required this.email,
    required this.password,
    required this.name,
  });

  @override
  List<Object?> get props => [email, password, name];
}

/// Event to sign out
class AuthSignOutRequested extends AuthEvent {}

/// Event when user data is updated
class AuthUserUpdated extends AuthEvent {
  final User user;

  const AuthUserUpdated(this.user);

  @override
  List<Object?> get props => [user];
}

/// Event to request password reset
class AuthPasswordResetRequested extends AuthEvent {
  final String email;

  const AuthPasswordResetRequested(this.email);

  @override
  List<Object?> get props => [email];
}

/// Event to verify email
class AuthEmailVerificationRequested extends AuthEvent {}

/// Event to resend email verification
class AuthEmailVerificationResendRequested extends AuthEvent {}

/// Event to sign in with biometrics
class AuthBiometricSignInRequested extends AuthEvent {}

/// Event to enable biometric authentication
class AuthBiometricEnableRequested extends AuthEvent {}

/// Event to disable biometric authentication
class AuthBiometricDisableRequested extends AuthEvent {}

/// Event to refresh authentication token
class AuthTokenRefreshRequested extends AuthEvent {}

/// Event to delete user account
class AuthAccountDeletionRequested extends AuthEvent {
  final String password;

  const AuthAccountDeletionRequested(this.password);

  @override
  List<Object?> get props => [password];
}
