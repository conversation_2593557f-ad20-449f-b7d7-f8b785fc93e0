import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/medicine.dart';

/// Medicine card component that mirrors the web app's medicine cards
/// Features thick left border, medicine name in bold navy, status badges
class MedicineCard extends StatelessWidget {
  final Medicine medicine;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool showHouseholdMember;

  const MedicineCard({
    super.key,
    required this.medicine,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.showHouseholdMember = false,
  });

  @override
  Widget build(BuildContext context) {
    final statusColor = _getStatusColor(medicine.status);
    final formIcon = _getFormIcon(medicine.form);
    
    return Card(
      elevation: 2,
      shadowColor: AppColors.shadowLight,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      color: AppColors.cardLight,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border(
              left: BorderSide(
                color: statusColor,
                width: 4,
              ),
              right: BorderSide(
                color: statusColor,
                width: 4,
              ),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with medicine name and status badge
                Row(
                  children: [
                    // Form icon
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: AppColors.teal.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        formIcon,
                        color: AppColors.teal,
                        size: 16,
                      ),
                    ),
                    
                    const SizedBox(width: 12),
                    
                    // Medicine name
                    Expanded(
                      child: Text(
                        medicine.name,
                        style: AppTextStyles.medicineTitle,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    
                    // Status badge
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: statusColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getStatusText(medicine.status),
                        style: AppTextStyles.statusBadge,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // Dosage and form
                Text(
                  '${medicine.dosage} - ${medicine.form}',
                  style: AppTextStyles.medicineDosage,
                ),
                
                const SizedBox(height: 8),
                
                // Details row
                Row(
                  children: [
                    // Quantity
                    Icon(
                      LucideIcons.package,
                      size: 14,
                      color: AppColors.grey600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Qté: ${medicine.quantity}',
                      style: AppTextStyles.medicineDetails,
                    ),
                    
                    const SizedBox(width: 16),
                    
                    // Expiry date
                    Icon(
                      LucideIcons.calendar,
                      size: 14,
                      color: AppColors.grey600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _formatExpiryDate(medicine.expiryDate),
                      style: AppTextStyles.medicineDetails,
                    ),
                  ],
                ),
                
                const SizedBox(height: 8),
                
                // Location and household member
                Row(
                  children: [
                    // Location
                    Icon(
                      LucideIcons.mapPin,
                      size: 14,
                      color: AppColors.grey600,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        medicine.location,
                        style: AppTextStyles.medicineDetails,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    
                    // Household member (if enabled)
                    if (showHouseholdMember && medicine.householdMemberName != null) ...[
                      const SizedBox(width: 8),
                      Icon(
                        LucideIcons.user,
                        size: 14,
                        color: AppColors.grey600,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        medicine.householdMemberName!,
                        style: AppTextStyles.medicineDetails,
                      ),
                    ],
                  ],
                ),
                
                // Action buttons (if provided)
                if (onEdit != null || onDelete != null) ...[
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      if (onEdit != null)
                        IconButton(
                          onPressed: onEdit,
                          icon: const Icon(LucideIcons.edit2),
                          iconSize: 16,
                          color: AppColors.teal,
                          style: IconButton.styleFrom(
                            backgroundColor: AppColors.teal.withOpacity(0.1),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      
                      if (onEdit != null && onDelete != null)
                        const SizedBox(width: 8),
                      
                      if (onDelete != null)
                        IconButton(
                          onPressed: onDelete,
                          icon: const Icon(LucideIcons.trash2),
                          iconSize: 16,
                          color: AppColors.error,
                          style: IconButton.styleFrom(
                            backgroundColor: AppColors.error.withOpacity(0.1),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    return AppColors.getMedicineStatusColor(status);
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'expired':
        return 'Expiré';
      case 'expiring_soon':
        return 'Expire bientôt';
      case 'low_stock':
        return 'Stock faible';
      case 'adequate':
        return 'Adéquat';
      case 'out_of_stock':
        return 'Rupture';
      default:
        return 'Inconnu';
    }
  }

  IconData _getFormIcon(String form) {
    switch (form.toLowerCase()) {
      case 'comprimé':
      case 'comprimés':
      case 'tablet':
      case 'tablets':
        return LucideIcons.pill;
      case 'gélule':
      case 'gélules':
      case 'capsule':
      case 'capsules':
        return LucideIcons.capsule;
      case 'sirop':
      case 'solution':
      case 'suspension':
        return LucideIcons.beaker;
      case 'injection':
      case 'ampoule':
        return LucideIcons.syringe;
      case 'crème':
      case 'pommade':
      case 'gel':
        return LucideIcons.droplet;
      case 'gouttes':
      case 'collyre':
        return LucideIcons.droplets;
      default:
        return LucideIcons.pill;
    }
  }

  String _formatExpiryDate(DateTime expiryDate) {
    final month = expiryDate.month.toString().padLeft(2, '0');
    final year = expiryDate.year.toString().substring(2);
    return '$month/$year';
  }
}
