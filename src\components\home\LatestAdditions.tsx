
import React from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Medicine } from "@/types";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import { isExpired, isNearExpiry, formatDateShort } from "@/utils/helpers";
import { formatCompleteMedicineName } from "@/utils/medicineUtils";
import { Users } from "lucide-react";

interface LatestAdditionsProps {
  medicines: Medicine[];
}

const LatestAdditions = ({ medicines }: LatestAdditionsProps) => {
  const navigate = useNavigate();
  
  if (medicines.length === 0) return null;

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold text-navy">🆕 Derniers ajouts</h2>
      <div className="space-y-3">
        {medicines.map((medicine) => {

          // Get the complete display name including dosage and form
          const displayName = formatCompleteMedicineName(medicine);

          // Check expiry status for border colors
          const expired = medicine.expiryDate ? isExpired(medicine.expiryDate) : false;
          const nearExpiry = medicine.expiryDate ? isNearExpiry(medicine.expiryDate) : false;

          // Get thick left/right border colors based on medicine status
          const getBorderColor = () => {
            if (expired) return "border-l-4 border-r-4 border-red-500";
            if (nearExpiry) return "border-l-4 border-r-4 border-amber-500";
            if (!medicine.expiryDate) return "border-l-4 border-r-4 border-gray-400";
            return "border-l-4 border-r-4 border-teal";
          };

          // Get hover background color based on status
          const getHoverBgColor = () => {
            if (expired) return "hover:bg-red-50";
            if (nearExpiry) return "hover:bg-amber-50";
            if (!medicine.expiryDate) return "hover:bg-gray-50";
            return "hover:bg-teal/5";
          };

          return (
            <Card
              key={medicine.id}
              className={`cursor-pointer ${getBorderColor()} ${getHoverBgColor()} transition-colors`}
              onClick={() => navigate(`/medicine/${medicine.id}`)}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="font-bold text-navy">{displayName}</h3>
                    <p className="text-sm text-gray-600">
                      {medicine.locationName || "Aucun emplacement"}
                    </p>
                    {medicine.expiryDate && (
                      <p className="text-xs text-gray-500 mt-1">
                        Exp: {formatDateShort(medicine.expiryDate)}
                      </p>
                    )}
                    <div className="flex items-center justify-between mt-1">
                      <p className="text-xs text-gray-500">
                        {formatDistanceToNow(new Date(medicine.createdAt), {
                          addSuffix: true,
                          locale: fr
                        })}
                      </p>
                      {medicine.familyMember && (
                        <div className="flex items-center space-x-1 text-xs text-teal">
                          <Users size={12} />
                          <span>Ajouté par {medicine.familyMember.name}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  {medicine.is_custom && (
                    <Badge className="bg-teal/10 text-teal border-teal/20">Saisie manuelle</Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default LatestAdditions;
