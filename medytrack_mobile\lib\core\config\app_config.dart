/// Application configuration constants
class AppConfig {
  // Supabase Configuration
  static const String supabaseUrl = 'https://wzzykbnebhyvdoagpwvk.supabase.co';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind6enlrYm5lYmh5dmRvYWdwd3ZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUwMTMzNjMsImV4cCI6MjA2MDU4OTM2M30.LYLDFRLZB4qtuzxTjRuZqnepRwrg3BNGLwdnwU4D0Es';

  // App Information
  static const String appName = 'MedyTrack';
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';

  // API Configuration
  static const Duration apiTimeout = Duration(seconds: 30);
  static const int maxRetryAttempts = 3;

  // Cache Configuration
  static const Duration cacheExpiration = Duration(hours: 24);
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB

  // Notification Configuration
  static const String notificationChannelId = 'medytrack_notifications';
  static const String notificationChannelName = 'MedyTrack Notifications';
  static const String notificationChannelDescription =
      'Notifications for medicine expiry and low stock alerts';

  // Security Configuration
  static const Duration sessionTimeout = Duration(hours: 24);
  static const int maxLoginAttempts = 5;
  static const Duration lockoutDuration = Duration(minutes: 15);

  // Feature Flags
  static const bool enableBiometricAuth = true;
  static const bool enablePushNotifications = true;
  static const bool enableOfflineMode = true;
  static const bool enableAnalytics = true;
  static const bool enableCrashReporting = true;

  // UI Configuration
  static const double defaultPadding = 16.0;
  static const double defaultRadius = 12.0;
  static const Duration animationDuration = Duration(milliseconds: 300);

  // Medicine Configuration
  static const int defaultExpiryWarningMonths = 1;
  static const int maxExpiryWarningMonths = 12;
  static const int defaultLowStockThreshold = 5;

  // Search Configuration
  static const int minSearchLength = 2;
  static const int maxSearchResults = 50;
  static const Duration searchDebounceDelay = Duration(milliseconds: 500);

  // Image Configuration
  static const int maxImageSize = 2 * 1024 * 1024; // 2MB
  static const double imageQuality = 0.8;
  static const int imageMaxWidth = 1024;
  static const int imageMaxHeight = 1024;

  // Pagination Configuration
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Sync Configuration
  static const Duration syncInterval = Duration(minutes: 15);
  static const Duration backgroundSyncInterval = Duration(hours: 1);

  // Error Reporting
  static const bool enableErrorReporting = true;
  static const String sentryDsn = ''; // Add Sentry DSN if using Sentry

  // Development Configuration
  static const bool isDebugMode = bool.fromEnvironment(
    'DEBUG',
    defaultValue: false,
  );
  static const bool enableLogging = bool.fromEnvironment(
    'ENABLE_LOGGING',
    defaultValue: true,
  );

  // Platform-specific Configuration
  static const Map<String, dynamic> androidConfig = {
    'minSdkVersion': 26,
    'targetSdkVersion': 34,
    'compileSdkVersion': 34,
  };

  static const Map<String, dynamic> iosConfig = {
    'minimumOSVersion': '14.0',
    'targetOSVersion': '17.0',
  };

  // Database Configuration
  static const String localDatabaseName = 'medytrack_local.db';
  static const int localDatabaseVersion = 1;

  // Backup Configuration
  static const Duration backupInterval = Duration(days: 1);
  static const int maxBackupFiles = 7;

  // Privacy Configuration
  static const Duration dataRetentionPeriod = Duration(
    days: 365 * 2,
  ); // 2 years
  static const bool enableDataCollection = true;
  static const bool enableCrashlytics = true;

  // Performance Configuration
  static const int maxConcurrentRequests = 5;
  static const Duration requestTimeout = Duration(seconds: 30);
  static const int maxMemoryUsage = 200 * 1024 * 1024; // 200MB

  // Localization Configuration
  static const String defaultLocale = 'fr';
  static const List<String> supportedLocales = ['fr', 'en', 'ar'];

  // Theme Configuration
  static const bool enableDynamicTheming = true;
  static const bool enableDarkMode = true;

  // Accessibility Configuration
  static const bool enableAccessibility = true;
  static const double minTouchTargetSize = 44.0;

  // Testing Configuration
  static const bool enableTestMode = bool.fromEnvironment(
    'TEST_MODE',
    defaultValue: false,
  );
  static const String testUserEmail = '<EMAIL>';
  static const String testUserPassword = 'TestPassword123!';
}
