import React, { useState } from "react";
import { Tag } from "@/types";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Plus, X, Edit2, Trash2 } from "lucide-react";
import { useTags } from "@/hooks/useTags";
import { toast } from "sonner";

interface TagFilterProps {
  activeTags: string[];
  onTagsChange: (tagIds: string[]) => void;
  showCreateButton?: boolean;
}

const TagFilter: React.FC<TagFilterProps> = ({ 
  activeTags, 
  onTagsChange, 
  showCreateButton = true 
}) => {
  const { tags, createTag, updateTag, deleteTag, isLoading } = useTags();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingTag, setEditingTag] = useState<Tag | null>(null);
  const [newTagName, setNewTagName] = useState("");
  const [newTagColor, setNewTagColor] = useState("#0DCDB7");
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleTagToggle = (tagId: string) => {
    if (activeTags.includes(tagId)) {
      onTagsChange(activeTags.filter(id => id !== tagId));
    } else {
      onTagsChange([...activeTags, tagId]);
    }
  };

  const handleCreateTag = async () => {
    if (!newTagName.trim()) {
      toast.error("Le nom de l'étiquette est requis");
      return;
    }

    setIsCreating(true);
    const success = await createTag(newTagName.trim(), newTagColor);
    if (success) {
      setNewTagName("");
      setNewTagColor("#0DCDB7");
      setIsCreateDialogOpen(false);
    }
    setIsCreating(false);
  };

  const handleEditTag = (tag: Tag) => {
    setEditingTag(tag);
    setNewTagName(tag.name);
    setNewTagColor(tag.color);
    setIsEditDialogOpen(true);
  };

  const handleUpdateTag = async () => {
    if (!editingTag || !newTagName.trim()) {
      toast.error("Le nom de l'étiquette est requis");
      return;
    }

    setIsUpdating(true);
    const success = await updateTag(editingTag.id, newTagName.trim(), newTagColor);
    if (success) {
      setIsEditDialogOpen(false);
      setEditingTag(null);
      setNewTagName("");
      setNewTagColor("#0DCDB7");
    }
    setIsUpdating(false);
  };

  const handleDeleteTag = async (tag: Tag) => {
    if (tag.isSystemTag) {
      toast.error("Impossible de supprimer une étiquette système");
      return;
    }

    if (tag.medicineCount && tag.medicineCount > 0) {
      toast.error("Impossible de supprimer une étiquette utilisée par des médicaments");
      return;
    }

    const success = await deleteTag(tag.id);
    if (success && activeTags.includes(tag.id)) {
      onTagsChange(activeTags.filter(id => id !== tag.id));
    }
  };

  const clearAllFilters = () => {
    onTagsChange([]);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-4">
        <div className="animate-spin h-6 w-6 border-2 border-teal border-t-transparent rounded-full" />
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-navy">Filtrer par étiquettes</h3>
        <div className="flex items-center gap-2">
          {activeTags.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="text-xs text-gray-500 hover:text-gray-700"
            >
              <X size={12} className="mr-1" />
              Effacer
            </Button>
          )}
          {showCreateButton && (
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="text-xs">
                  <Plus size={12} className="mr-1" />
                  Nouvelle étiquette
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Créer une nouvelle étiquette</DialogTitle>
                  <DialogDescription>
                    Ajoutez une étiquette personnalisée pour organiser vos médicaments.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="tag-name">Nom de l'étiquette</Label>
                    <Input
                      id="tag-name"
                      value={newTagName}
                      onChange={(e) => setNewTagName(e.target.value)}
                      placeholder="Ex: Vitamines, Urgence..."
                      maxLength={50}
                    />
                  </div>
                  <div>
                    <Label htmlFor="tag-color">Couleur</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        id="tag-color"
                        type="color"
                        value={newTagColor}
                        onChange={(e) => setNewTagColor(e.target.value)}
                        className="w-16 h-10"
                      />
                      <Badge style={{ backgroundColor: newTagColor, color: 'white' }}>
                        {newTagName || "Aperçu"}
                      </Badge>
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Annuler
                  </Button>
                  <Button onClick={handleCreateTag} disabled={isCreating}>
                    {isCreating ? "Création..." : "Créer"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </div>

      <div className="overflow-x-auto pb-2">
        <div className="flex flex-wrap gap-2">
          {tags.map((tag) => {
            const isActive = activeTags.includes(tag.id);
            
            return (
              <div key={tag.id} className="flex items-center gap-1">
                <Button
                  variant={isActive ? "default" : "outline"}
                  size="sm"
                  className={`flex-shrink-0 ${isActive ? '' : 'bg-white hover:bg-gray-50'}`}
                  style={isActive ? { backgroundColor: tag.color, borderColor: tag.color } : {}}
                  onClick={() => handleTagToggle(tag.id)}
                >
                  <span className="mr-1">{tag.name}</span>
                  {tag.medicineCount !== undefined && tag.medicineCount > 0 && (
                    <Badge variant="secondary" className="ml-1 text-xs">
                      {tag.medicineCount}
                    </Badge>
                  )}
                </Button>
                
                {!tag.isSystemTag && showCreateButton && (
                  <div className="flex items-center">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
                      onClick={() => handleEditTag(tag)}
                    >
                      <Edit2 size={12} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 text-gray-400 hover:text-red-600"
                      onClick={() => handleDeleteTag(tag)}
                      disabled={tag.medicineCount && tag.medicineCount > 0}
                    >
                      <Trash2 size={12} />
                    </Button>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Edit Tag Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Modifier l'étiquette</DialogTitle>
            <DialogDescription>
              Modifiez le nom et la couleur de votre étiquette personnalisée.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-tag-name">Nom de l'étiquette</Label>
              <Input
                id="edit-tag-name"
                value={newTagName}
                onChange={(e) => setNewTagName(e.target.value)}
                placeholder="Ex: Vitamines, Urgence..."
                maxLength={50}
              />
            </div>
            <div>
              <Label htmlFor="edit-tag-color">Couleur</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="edit-tag-color"
                  type="color"
                  value={newTagColor}
                  onChange={(e) => setNewTagColor(e.target.value)}
                  className="w-16 h-10"
                />
                <Badge style={{ backgroundColor: newTagColor, color: 'white' }}>
                  {newTagName || "Aperçu"}
                </Badge>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Annuler
            </Button>
            <Button onClick={handleUpdateTag} disabled={isUpdating}>
              {isUpdating ? "Mise à jour..." : "Mettre à jour"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TagFilter;
