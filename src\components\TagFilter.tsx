import React from "react";
import { Tag } from "@/types";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";
import { useTags } from "@/hooks/useTags";

interface TagFilterProps {
  activeTags: string[];
  onTagsChange: (tagIds: string[]) => void;
}

const TagFilter: React.FC<TagFilterProps> = ({
  activeTags,
  onTagsChange
}) => {
  const { tags, isLoading } = useTags();


  const handleTagToggle = (tagId: string) => {
    if (activeTags.includes(tagId)) {
      onTagsChange(activeTags.filter(id => id !== tagId));
    } else {
      onTagsChange([...activeTags, tagId]);
    }
  };



  const clearAllFilters = () => {
    onTagsChange([]);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-4">
        <div className="animate-spin h-6 w-6 border-2 border-teal border-t-transparent rounded-full" />
      </div>
    );
  }

  // Group tags by category
  const therapeuticTags = tags.filter(tag => tag.category === 'therapeutic');
  const usageTags = tags.filter(tag => tag.category === 'usage');

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-navy">Filtrer par étiquettes pharmaceutiques</h3>
        {activeTags.length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            <X size={12} className="mr-1" />
            Effacer
          </Button>
        )}
      </div>

      {/* Therapeutic Classes */}
      <div className="space-y-2">
        <h4 className="text-xs font-medium text-gray-600 flex items-center">
          💊 Classes Thérapeutiques
        </h4>
        <div className="overflow-x-auto pb-2">
          <div className="flex flex-wrap gap-2">
            {therapeuticTags.map((tag) => {
              const isActive = activeTags.includes(tag.id);

              return (
                <Button
                  key={tag.id}
                  variant={isActive ? "default" : "outline"}
                  size="sm"
                  className={`flex-shrink-0 ${isActive ? '' : 'bg-white hover:bg-gray-50'}`}
                  style={isActive ? { backgroundColor: tag.color, borderColor: tag.color } : {}}
                  onClick={() => handleTagToggle(tag.id)}
                >
                  <span className="mr-1">{tag.name}</span>
                  {tag.medicineCount !== undefined && tag.medicineCount > 0 && (
                    <Badge variant="secondary" className="ml-1 text-xs">
                      {tag.medicineCount}
                    </Badge>
                  )}
                </Button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Usage/Domain Tags */}
      <div className="space-y-2">
        <h4 className="text-xs font-medium text-gray-600 flex items-center">
          🩺 Domaines d'Usage
        </h4>
        <div className="overflow-x-auto pb-2">
          <div className="flex flex-wrap gap-2">
            {usageTags.map((tag) => {
              const isActive = activeTags.includes(tag.id);

              return (
                <Button
                  key={tag.id}
                  variant={isActive ? "default" : "outline"}
                  size="sm"
                  className={`flex-shrink-0 ${isActive ? '' : 'bg-white hover:bg-gray-50'}`}
                  style={isActive ? { backgroundColor: tag.color, borderColor: tag.color } : {}}
                  onClick={() => handleTagToggle(tag.id)}
                >
                  <span className="mr-1">{tag.name}</span>
                  {tag.medicineCount !== undefined && tag.medicineCount > 0 && (
                    <Badge variant="secondary" className="ml-1 text-xs">
                      {tag.medicineCount}
                    </Badge>
                  )}
                </Button>
              );
            })}
          </div>
        </div>
      </div>

      <div className="text-xs text-muted-foreground">
        Étiquettes pharmaceutiques standardisées pour une organisation professionnelle
      </div>
    </div>
  );
};

export default TagFilter;
