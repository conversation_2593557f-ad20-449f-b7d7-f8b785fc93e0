
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { AlertTriangle, Trash2 } from "lucide-react";
import { toast } from "sonner";

interface DeleteAccountDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  userId: string;
}

const DeleteAccountDialog = ({ isOpen, onOpenChange, userId }: DeleteAccountDialogProps) => {
  const navigate = useNavigate();
  const [confirmText, setConfirmText] = useState("");
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDeleteAccount = async () => {
    if (confirmText !== "SUPPRIMER") {
      toast.error("Veuillez saisir le texte de confirmation exactement comme demandé");
      return;
    }
    
    setIsDeleting(true);
    try {
      const { error } = await supabase.functions.invoke("delete-account", {
        body: { userId }
      });
      
      if (error) {
        toast.error("Erreur lors de la suppression du compte", {
          description: error.message
        });
        return;
      }
      
      toast.success("Compte supprimé avec succès");
      await supabase.auth.signOut();
      navigate("/auth");
    } catch (error) {
      toast.error("Une erreur est survenue");
    } finally {
      setIsDeleting(false);
      onOpenChange(false);
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            Supprimer votre compte
          </AlertDialogTitle>
          <AlertDialogDescription>
            Cette action est irréversible. Elle supprimera définitivement votre compte
            et toutes vos données personnelles.
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <Alert variant="destructive" className="mt-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Attention</AlertTitle>
          <AlertDescription>
            Vous êtes sur le point de supprimer votre compte. Toutes vos données seront perdues.
          </AlertDescription>
        </Alert>
        
        <div className="mt-4 space-y-2">
          <Label htmlFor="confirm">
            Pour confirmer, veuillez saisir <span className="font-bold">SUPPRIMER</span> ci-dessous :
          </Label>
          <Input
            id="confirm"
            value={confirmText}
            onChange={(e) => setConfirmText(e.target.value)}
            placeholder="SUPPRIMER"
          />
        </div>
        
        <AlertDialogFooter className="mt-4">
          <AlertDialogCancel disabled={isDeleting}>Annuler</AlertDialogCancel>
          <Button 
            variant="destructive" 
            onClick={handleDeleteAccount} 
            disabled={confirmText !== "SUPPRIMER" || isDeleting}
            className="gap-2"
          >
            {isDeleting && <span className="animate-spin">⏳</span>}
            <Trash2 className="h-4 w-4" />
            {isDeleting ? 'Suppression...' : 'Supprimer définitivement'}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteAccountDialog;
