import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import GroupedMedicineCard from '@/components/medicines/GroupedMedicineCard';
import { GroupedMedicine } from '@/utils/medicineUtils';
import { Medicine } from '@/types';

// Mock the medicine icons utility
vi.mock('@/utils/medicineIcons', () => ({
  getMedicineIcon: () => vi.fn()
}));

// Mock the helpers
vi.mock('@/utils/helpers', () => ({
  formatDateShort: (date: string) => date.substring(0, 7), // MM/YY format
  isExpired: (date: string) => new Date(date) < new Date('2025-01-01'),
  isNearExpiry: (date: string) => {
    const expiry = new Date(date);
    const today = new Date('2025-01-01');
    const thirtyDays = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);
    return expiry >= today && expiry <= thirtyDays;
  }
}));

const createMockMedicine = (overrides: Partial<Medicine> = {}): Medicine => ({
  id: 'test-id',
  name: 'Paracétamol',
  dosage: '500mg',
  form: 'Comprimé',
  presentation: 'Boîte de 30',
  quantity: 10,
  expiryDate: '2025-12-31',
  category: 'pain',
  location: 'loc-1',
  locationName: 'Armoire à pharmacie',
  createdAt: '2025-01-01',
  updatedAt: '2025-01-01',
  is_custom: false,
  ...overrides
});

const createMockGroupedMedicine = (overrides: Partial<GroupedMedicine> = {}): GroupedMedicine => ({
  name: 'Paracétamol',
  dosage: '500mg',
  form: 'Comprimé',
  presentation: 'Boîte de 30',
  totalQuantity: 15,
  earliestExpiry: '2025-12-31',
  hasMultipleExpiries: false,
  medicines: [
    createMockMedicine({ id: '1', quantity: 10 }),
    createMockMedicine({ id: '2', quantity: 5 })
  ],
  groupKey: 'paracétamol|500mg|comprimé',
  isCustom: false,
  ...overrides
});

describe('GroupedMedicineCard', () => {
  const mockOnClick = vi.fn();
  const mockOnMedicineClick = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render grouped medicine information correctly', () => {
    const groupedMedicine = createMockGroupedMedicine();
    
    render(
      <GroupedMedicineCard 
        groupedMedicine={groupedMedicine}
        onClick={mockOnClick}
      />
    );

    expect(screen.getByText('Paracétamol - 500mg - Comprimé')).toBeInTheDocument();
    expect(screen.getByText('Boîte de 30')).toBeInTheDocument();
    expect(screen.getByText('15 unités')).toBeInTheDocument();
    expect(screen.getByText('2 entrées')).toBeInTheDocument();
  });

  it('should show expired status for expired medicines', () => {
    const groupedMedicine = createMockGroupedMedicine({
      earliestExpiry: '2024-01-01' // Expired date
    });
    
    render(
      <GroupedMedicineCard 
        groupedMedicine={groupedMedicine}
        onClick={mockOnClick}
      />
    );

    expect(screen.getByText('Expiré')).toBeInTheDocument();
  });

  it('should show expiring status for medicines expiring soon', () => {
    const groupedMedicine = createMockGroupedMedicine({
      earliestExpiry: '2025-01-15' // Within 30 days
    });
    
    render(
      <GroupedMedicineCard 
        groupedMedicine={groupedMedicine}
        onClick={mockOnClick}
      />
    );

    expect(screen.getByText('Expire bientôt')).toBeInTheDocument();
  });

  it('should handle expand/collapse functionality', () => {
    const groupedMedicine = createMockGroupedMedicine({
      medicines: [
        createMockMedicine({ id: '1', quantity: 10 }),
        createMockMedicine({ id: '2', quantity: 5 })
      ]
    });
    
    render(
      <GroupedMedicineCard 
        groupedMedicine={groupedMedicine}
        onClick={mockOnClick}
        onMedicineClick={mockOnMedicineClick}
      />
    );

    // Should show expand button for multiple medicines
    const expandButton = screen.getByText('Détails');
    expect(expandButton).toBeInTheDocument();

    // Click to expand
    fireEvent.click(expandButton);
    
    // Should show individual medicine entries
    expect(screen.getByText('Détail des entrées (2)')).toBeInTheDocument();
    expect(screen.getByText('#1')).toBeInTheDocument();
    expect(screen.getByText('#2')).toBeInTheDocument();
  });

  it('should handle custom medicines correctly', () => {
    const groupedMedicine = createMockGroupedMedicine({
      name: 'Mon médicament personnel',
      isCustom: true,
      medicines: [createMockMedicine({ 
        is_custom: true, 
        custom_name: 'Mon médicament personnel' 
      })]
    });
    
    render(
      <GroupedMedicineCard 
        groupedMedicine={groupedMedicine}
        onClick={mockOnClick}
      />
    );

    expect(screen.getByText('Manuel')).toBeInTheDocument();
    expect(screen.getByText('Mon médicament personnel - 500mg - Comprimé')).toBeInTheDocument();
  });

  it('should call onClick when card is clicked', () => {
    const groupedMedicine = createMockGroupedMedicine();
    
    render(
      <GroupedMedicineCard 
        groupedMedicine={groupedMedicine}
        onClick={mockOnClick}
      />
    );

    // Click on the card itself (not a button)
    const card = screen.getByText('Paracétamol - 500mg - Comprimé').closest('div[class*="cursor-pointer"]');
    fireEvent.click(card!);
    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  it('should show multiple locations when medicines are in different locations', () => {
    const groupedMedicine = createMockGroupedMedicine({
      medicines: [
        createMockMedicine({ id: '1', locationName: 'Cuisine' }),
        createMockMedicine({ id: '2', locationName: 'Chambre' })
      ]
    });
    
    render(
      <GroupedMedicineCard 
        groupedMedicine={groupedMedicine}
        onClick={mockOnClick}
      />
    );

    expect(screen.getByText('2 emplacements')).toBeInTheDocument();
  });

  it('should not show expand button when showExpandButton is false', () => {
    const groupedMedicine = createMockGroupedMedicine();
    
    render(
      <GroupedMedicineCard 
        groupedMedicine={groupedMedicine}
        onClick={mockOnClick}
        showExpandButton={false}
      />
    );

    expect(screen.queryByText('Détails')).not.toBeInTheDocument();
  });
});
