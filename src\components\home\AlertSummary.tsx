
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { AlertCircle, Pill } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";

interface AlertCounts {
  expired: number;
  expiringSoon: number;
  lowStock: number;
}

const AlertSummary = () => {
  const [alertCounts, setAlertCounts] = useState<AlertCounts>({
    expired: 0,
    expiringSoon: 0,
    lowStock: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const { householdId } = useAuth();

  useEffect(() => {
    const fetchAlertCounts = async () => {
      if (!householdId) return;
      
      try {
        const { data, error } = await supabase
          .from('dashboard_medicine_alerts_view')
          .select('expiration_status, stock_status')
          .eq('household_id', householdId);
          
        if (error) throw error;
        
        if (data) {
          const counts = {
            expired: 0,
            expiringSoon: 0,
            lowStock: 0
          };
          
          data.forEach((item) => {
            if (item.expiration_status === 'expired') {
              counts.expired++;
            } else if (item.expiration_status === 'expiring_soon') {
              counts.expiringSoon++;
            }
            
            if (item.stock_status === 'low_stock') {
              counts.lowStock++;
            }
          });
          
          setAlertCounts(counts);
        }
      } catch (err) {
        console.error('Error fetching alert counts:', err);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchAlertCounts();
  }, [householdId]);
  
  const navigateToAlerts = (tab: string) => {
    navigate(`/alerts?tab=${tab}`);
  };
  
  const totalAlerts = alertCounts.expired + alertCounts.expiringSoon + alertCounts.lowStock;
  
  if (isLoading || totalAlerts === 0) return null;

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center gap-2 mb-2">
          <AlertCircle size={18} className="text-amber-500" />
          <h3 className="font-medium text-navy">Alertes</h3>
        </div>
        
        <div className="flex items-center justify-around text-center">
          <div
            className="py-2 px-3 rounded-md hover:bg-red-50 cursor-pointer transition-colors"
            onClick={() => navigateToAlerts('expired')}
          >
            <div className="text-red-500 font-bold text-xl">{alertCounts.expired}</div>
            <div className="text-xs text-gray-600">Expirés</div>
          </div>

          <div className="h-10 w-px bg-gray-200"></div>

          <div
            className="py-2 px-3 rounded-md hover:bg-amber-50 cursor-pointer transition-colors"
            onClick={() => navigateToAlerts('expiring-soon')}
          >
            <div className="text-amber-500 font-bold text-xl">{alertCounts.expiringSoon}</div>
            <div className="text-xs text-gray-600">Bientôt</div>
          </div>
          
          <div className="h-10 w-px bg-gray-200"></div>
          
          <div
            className="py-2 px-3 rounded-md hover:bg-teal/10 cursor-pointer transition-colors"
            onClick={() => navigateToAlerts('low-quantity')}
          >
            <div className="text-teal font-bold text-xl">{alertCounts.lowStock}</div>
            <div className="text-xs text-gray-600">Stock bas</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AlertSummary;
