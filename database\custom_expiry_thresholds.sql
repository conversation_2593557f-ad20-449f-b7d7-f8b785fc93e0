-- Custom Expiry Thresholds Migration
-- This script adds customizable expiry warning thresholds to users table
-- Note: Despite column name using "days", this stores MONTHS for MedyTrack's month-based approach

-- 1. Add expiry_warning_days column to users table
ALTER TABLE public.users
ADD COLUMN IF NOT EXISTS expiry_warning_days INTEGER DEFAULT 1;

-- Add comment to explain the column (stores months, not days)
COMMENT ON COLUMN public.users.expiry_warning_days IS 'Custom expiry warning threshold in MONTHS (not days despite column name). When medicine expires within this many months, it is considered expiring soon. Default is 1 month.';

-- 2. Create index for performance when filtering by expiry threshold
CREATE INDEX IF NOT EXISTS idx_users_expiry_warning
ON public.users(id, expiry_warning_days);

-- 3. Update existing users to have a default threshold of 1 month
UPDATE public.users
SET expiry_warning_days = 1
WHERE expiry_warning_days IS NULL;

-- 4. Function to check if a medicine is expiring soon based on user's custom threshold
CREATE OR REPLACE FUNCTION public.is_medicine_expiring_soon(
    p_expiration_date DATE,
    p_threshold_months INTEGER DEFAULT 1
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Return false if no expiration date
    IF p_expiration_date IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Check if medicine expires within the threshold months from today
    RETURN p_expiration_date > CURRENT_DATE 
           AND p_expiration_date <= (CURRENT_DATE + INTERVAL '1 month' * p_threshold_months);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 5. Function to get expiry status based on user's custom threshold
CREATE OR REPLACE FUNCTION public.get_medicine_expiry_status(
    p_expiration_date DATE,
    p_threshold_months INTEGER DEFAULT 1
)
RETURNS TEXT AS $$
BEGIN
    -- Return null if no expiration date
    IF p_expiration_date IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Check if expired
    IF p_expiration_date < CURRENT_DATE THEN
        RETURN 'expired';
    END IF;
    
    -- Check if expiring soon based on custom threshold
    IF p_expiration_date <= (CURRENT_DATE + INTERVAL '1 month' * p_threshold_months) THEN
        RETURN 'expiring_soon';
    END IF;
    
    -- Otherwise, it's valid
    RETURN 'valid';
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 6. Update dashboard_medicine_alerts_view to use custom expiry thresholds
-- First, drop the existing view
DROP VIEW IF EXISTS public.dashboard_medicine_alerts_view;

-- Recreate the view with custom expiry threshold logic
CREATE VIEW public.dashboard_medicine_alerts_view AS
SELECT
    um.id as user_medicine_id,
    um.household_id,
    um.medicine_id,
    um.custom_name,
    um.is_custom,
    um.dosage,
    um.expiration,
    um.quantity,
    um.category,
    um.notes,
    um.created_at,
    um.family_member_id,
    um.location as location_id,
    um.low_stock_threshold, -- Add missing low_stock_threshold field

    -- Medicine information from tunisia_medicines
    tm.nom as medicine_name,
    tm.forme,
    tm.laboratoire,
    tm.dci,
    tm.classe,
    tm.sous_classe,
    tm.amm,

    -- Create official label combining name, dosage, and form
    CASE
        WHEN um.is_custom = true THEN um.custom_name
        ELSE CONCAT_WS(' - ',
            COALESCE(tm.nom, 'Médicament inconnu'),
            NULLIF(COALESCE(um.dosage, tm.dosage), ''),
            NULLIF(tm.forme, '')
        )
    END as official_label,

    -- Location information
    l.name as location_name,

    -- Family member information
    fm.name as family_member_name,

    -- Get user's custom expiry threshold
    u.expiry_warning_days as user_expiry_threshold_months,

    -- Expiry status using user's custom threshold
    public.get_medicine_expiry_status(
        um.expiration::DATE,
        COALESCE(u.expiry_warning_days, 1)
    ) as expiration_status,

    -- Stock status
    CASE
        WHEN um.quantity = 0 THEN 'out_of_stock'
        WHEN um.quantity <= COALESCE(um.low_stock_threshold, 0) AND um.quantity > 0 THEN 'low_stock'
        ELSE 'adequate'
    END as stock_status,

    -- Additional fields for compatibility
    tm.presentation as indications

FROM public.user_medicines um
LEFT JOIN public.tunisia_medicines tm ON um.medicine_id = tm.id
LEFT JOIN public.locations l ON NULLIF(um.location, '')::uuid = l.id
LEFT JOIN public.family_members fm ON um.family_member_id = fm.id
LEFT JOIN public.users u ON um.household_id = u.household_id
WHERE um.household_id IS NOT NULL;

-- 7. Update medicines_expiring_soon view to use custom thresholds
DROP VIEW IF EXISTS public.medicines_expiring_soon;

CREATE VIEW public.medicines_expiring_soon AS
SELECT
    um.id as user_medicine_id,
    um.household_id,
    CASE
        WHEN um.is_custom = true THEN um.custom_name
        ELSE COALESCE(tm.nom, 'Médicament inconnu')
    END as medicine_name,
    um.dosage,
    um.expiration,
    um.quantity,
    l.name as location_name
FROM public.user_medicines um
LEFT JOIN public.tunisia_medicines tm ON um.medicine_id = tm.id
LEFT JOIN public.locations l ON NULLIF(um.location, '')::uuid = l.id
LEFT JOIN public.users u ON um.household_id = u.household_id
WHERE um.household_id IS NOT NULL
  AND um.expiration IS NOT NULL
  AND public.is_medicine_expiring_soon(
      um.expiration::DATE,
      COALESCE(u.expiry_warning_days, 1)
  ) = true;

-- 8. Update user_dashboard_data view to use custom thresholds
DROP VIEW IF EXISTS public.user_dashboard_data;

CREATE VIEW public.user_dashboard_data AS
SELECT
    um.household_id,
    COUNT(*) as total_medicines,
    COUNT(DISTINCT um.location) as location_count,
    COUNT(CASE
        WHEN public.is_medicine_expiring_soon(
            um.expiration::DATE,
            COALESCE(u.expiry_warning_days, 1)
        ) = true
        THEN 1
    END) as expiring_soon_count
FROM public.user_medicines um
LEFT JOIN public.users u ON um.household_id = u.household_id
WHERE um.household_id IS NOT NULL
GROUP BY um.household_id;

-- 9. Grant necessary permissions
GRANT SELECT ON public.dashboard_medicine_alerts_view TO authenticated;
GRANT SELECT ON public.medicines_expiring_soon TO authenticated;
GRANT SELECT ON public.user_dashboard_data TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_medicine_expiring_soon(DATE, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_medicine_expiry_status(DATE, INTEGER) TO authenticated;

-- 10. Add helpful comments
COMMENT ON VIEW public.dashboard_medicine_alerts_view IS 'Comprehensive view of medicines with alerts, using user-specific expiry thresholds stored in months';
COMMENT ON VIEW public.medicines_expiring_soon IS 'View of medicines expiring soon based on each user''s custom threshold in months';
COMMENT ON VIEW public.user_dashboard_data IS 'Dashboard summary data using user-specific expiry thresholds';
COMMENT ON FUNCTION public.is_medicine_expiring_soon(DATE, INTEGER) IS 'Check if medicine is expiring soon based on custom threshold in months';
COMMENT ON FUNCTION public.get_medicine_expiry_status(DATE, INTEGER) IS 'Get medicine expiry status (expired/expiring_soon/valid) based on custom threshold in months';
