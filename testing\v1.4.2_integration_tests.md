# MedyTrack v1.4.2 Integration Testing Plan

## Overview

This document outlines the comprehensive testing plan for MedyTrack v1.4.2, covering both the Custom Expiry Threshold and Tagging System features.

## Test Environment Setup

### Prerequisites
- Database with migration scripts applied
- Test user accounts with existing medicines
- Various expiry dates for testing thresholds
- Mix of categorized and uncategorized medicines

### Test Data Requirements
- Users with different expiry preferences (1-12 months)
- Medicines with various expiry dates (expired, expiring soon, valid)
- Existing medicines with categories for migration testing
- Multiple households for isolation testing

## Feature 1: Custom Expiry Warning Threshold

### Test Cases

#### TC1.1: User Settings Management
- **Test**: User can access expiry threshold setting in Profile
- **Steps**:
  1. Navigate to Profile page
  2. Locate "Paramètres des médicaments" section
  3. Verify expiry threshold input is visible
  4. Test input validation (1-12 months)
  5. Save new threshold value
- **Expected**: Setting saves successfully, UI updates immediately

#### TC1.2: Threshold Application in Alerts
- **Test**: Custom threshold affects "Expiring Soon" categorization
- **Steps**:
  1. Set user threshold to 2 months
  2. Add medicine expiring in 45 days
  3. Check Alerts page
  4. Verify medicine appears in "Bientôt" tab
  5. Change threshold to 1 month
  6. Verify medicine moves to appropriate category
- **Expected**: Alerts respect user's custom threshold

#### TC1.3: Dashboard Integration
- **Test**: Dashboard statistics use custom threshold
- **Steps**:
  1. Set custom threshold (e.g., 3 months)
  2. View dashboard statistics
  3. Verify "expiring soon" count matches threshold
  4. Compare with database view results
- **Expected**: Dashboard counts match custom threshold logic

#### TC1.4: Medicine Card Badges
- **Test**: Medicine cards show correct expiry status
- **Steps**:
  1. Set threshold to 2 months
  2. View medicine list
  3. Check badge colors and text
  4. Verify consistency across all views
- **Expected**: All medicine cards use custom threshold

### Database Tests

#### TC1.5: Database Functions
- **Test**: Database functions work with custom thresholds
- **SQL**:
  ```sql
  -- Test expiry status function
  SELECT get_medicine_expiry_status('2024-08-15'::DATE, 2);
  
  -- Test expiring soon function
  SELECT is_medicine_expiring_soon('2024-08-15'::DATE, 3);
  ```
- **Expected**: Functions return correct status based on threshold

#### TC1.6: View Updates
- **Test**: Database views use user thresholds
- **SQL**:
  ```sql
  -- Check view includes threshold
  SELECT user_expiry_threshold_months, expiration_status 
  FROM dashboard_medicine_alerts_view 
  WHERE household_id = 'test-household-id';
  ```
- **Expected**: Views include and use custom thresholds

## Feature 2: Tagging System

### Test Cases

#### TC2.1: Tag Management
- **Test**: Users can create, edit, and delete custom tags
- **Steps**:
  1. Navigate to medicine filters
  2. Click "Nouvelle étiquette"
  3. Create tag with custom name and color
  4. Edit existing custom tag
  5. Attempt to delete system tag (should fail)
  6. Delete custom tag with no medicines
- **Expected**: Full CRUD operations work for custom tags

#### TC2.2: Medicine Tagging
- **Test**: Users can assign multiple tags to medicines
- **Steps**:
  1. Edit existing medicine
  2. Add multiple tags using TagSelector
  3. Save changes
  4. Verify tags appear in medicine card
  5. Remove some tags
  6. Verify updates persist
- **Expected**: Multi-tag assignment works correctly

#### TC2.3: Tag Filtering
- **Test**: Tag-based filtering works in medicine lists
- **Steps**:
  1. Apply single tag filter
  2. Verify only medicines with that tag show
  3. Apply multiple tag filters
  4. Test "OR" vs "AND" logic
  5. Clear filters
- **Expected**: Filtering works as expected

#### TC2.4: Migration Verification
- **Test**: Existing categories converted to tags
- **Steps**:
  1. Check medicines that had categories
  2. Verify corresponding system tags exist
  3. Verify medicine_tags relationships created
  4. Test backward compatibility
- **Expected**: All category data preserved as tags

### Database Tests

#### TC2.5: Tag Relationships
- **Test**: Many-to-many relationships work correctly
- **SQL**:
  ```sql
  -- Test tag assignment
  SELECT mt.*, t.name, t.color 
  FROM medicine_tags mt
  JOIN tags t ON mt.tag_id = t.id
  WHERE mt.user_medicine_id = 'test-medicine-id';
  ```
- **Expected**: Relationships maintain data integrity

#### TC2.6: RLS Policies
- **Test**: Row Level Security prevents cross-household access
- **Steps**:
  1. Create tags in different households
  2. Attempt cross-household tag access
  3. Verify isolation maintained
- **Expected**: Users only see their household's tags

## Integration Tests

### TC3.1: Combined Feature Usage
- **Test**: Both features work together without conflicts
- **Steps**:
  1. Set custom expiry threshold
  2. Create custom tags
  3. Add medicine with tags and expiry date
  4. Verify alerts use custom threshold
  5. Verify tag filtering works
  6. Check dashboard statistics
- **Expected**: No conflicts between features

### TC3.2: Performance Testing
- **Test**: New features don't significantly impact performance
- **Steps**:
  1. Load medicine list with 100+ medicines
  2. Apply multiple tag filters
  3. Change expiry threshold
  4. Measure response times
- **Expected**: Performance remains acceptable (<2s load times)

### TC3.3: Mobile Responsiveness
- **Test**: New UI components work on mobile devices
- **Steps**:
  1. Test TagSelector on mobile
  2. Test expiry threshold setting
  3. Verify tag filters are usable
  4. Check touch interactions
- **Expected**: Full functionality on mobile

## Regression Tests

### TC4.1: Existing Functionality
- **Test**: Core medicine management still works
- **Steps**:
  1. Add new medicine
  2. Edit existing medicine
  3. Delete medicine
  4. Search medicines
  5. View medicine details
- **Expected**: No regression in core functionality

### TC4.2: Backward Compatibility
- **Test**: Category system still functional during transition
- **Steps**:
  1. Filter by old categories
  2. Verify category display in medicine cards
  3. Test category-based searches
- **Expected**: Categories work alongside tags

## Error Handling Tests

### TC5.1: Invalid Threshold Values
- **Test**: System handles invalid expiry thresholds
- **Steps**:
  1. Try to set threshold to 0
  2. Try to set threshold to 13
  3. Try negative values
  4. Try non-numeric input
- **Expected**: Proper validation and error messages

### TC5.2: Tag Constraints
- **Test**: System enforces tag constraints
- **Steps**:
  1. Try to create duplicate tag names
  2. Try to delete system tags
  3. Try to create tag with empty name
  4. Test maximum tag name length
- **Expected**: Appropriate constraints enforced

## User Experience Tests

### TC6.1: Onboarding
- **Test**: New users understand the features
- **Steps**:
  1. Create new user account
  2. Navigate through medicine management
  3. Discover tag and threshold features
  4. Test feature discoverability
- **Expected**: Features are intuitive and discoverable

### TC6.2: Migration UX
- **Test**: Existing users adapt to new features
- **Steps**:
  1. Use existing user account
  2. Verify migrated data is accessible
  3. Test transition from categories to tags
  4. Verify no data loss
- **Expected**: Smooth transition for existing users

## Automated Test Scripts

### Database Tests
```sql
-- Test custom expiry threshold
DO $$
BEGIN
  -- Test threshold setting
  UPDATE users SET expiry_warning_days = 3 WHERE id = 'test-user';
  
  -- Verify view updates
  ASSERT (SELECT COUNT(*) FROM dashboard_medicine_alerts_view 
          WHERE expiration_status = 'expiring_soon' 
          AND user_expiry_threshold_months = 3) > 0;
END $$;
```

### API Tests
```javascript
// Test tag creation
const newTag = await createTag('Test Tag', '#FF0000');
assert(newTag.id);
assert(newTag.color === '#FF0000');

// Test medicine tagging
const success = await addTagToMedicine(medicineId, newTag.id);
assert(success === true);
```

## Success Criteria

### Feature 1: Custom Expiry Threshold
- [ ] Users can set threshold (1-12 months)
- [ ] All expiry logic uses custom threshold
- [ ] Database views updated correctly
- [ ] No performance degradation
- [ ] Backward compatibility maintained

### Feature 2: Tagging System
- [ ] Tag CRUD operations work
- [ ] Multi-tag assignment functional
- [ ] Tag filtering works correctly
- [ ] Migration completed successfully
- [ ] RLS policies enforce security

### Integration
- [ ] Features work together seamlessly
- [ ] No conflicts or data corruption
- [ ] Performance remains acceptable
- [ ] Mobile experience maintained
- [ ] User experience is intuitive

## Sign-off

- [ ] Development Team Lead
- [ ] QA Team Lead
- [ ] Product Owner
- [ ] Database Administrator
- [ ] DevOps Engineer

## Post-Release Monitoring

- Monitor database performance
- Track user adoption of new features
- Watch for error rates
- Collect user feedback
- Plan for category system deprecation
