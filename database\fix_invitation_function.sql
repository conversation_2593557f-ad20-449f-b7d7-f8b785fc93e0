-- Quick fix for get_pending_invitation_for_user function
-- This fixes the email column error by using profiles table instead of users table

-- Drop and recreate the function with correct table joins
DROP FUNCTION IF EXISTS get_pending_invitation_for_user(TEXT);

CREATE OR REPLACE FUNCTION get_pending_invitation_for_user(user_email TEXT)
RETURNS TABLE (
  id UUID,
  household_id UUID,
  household_name TEXT,
  invited_by_email TEXT,
  token TEXT,
  permissions JSONB,
  expires_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    hi.id,
    hi.household_id,
    h.name as household_name,
    p.email as invited_by_email,
    hi.token,
    hi.permissions,
    hi.expires_at
  FROM household_invitations hi
  JOIN households h ON hi.household_id = h.id
  JOIN profiles p ON hi.invited_by = p.id  -- Use profiles table for email
  WHERE hi.email = user_email
    AND hi.status = 'pending'
    AND hi.expires_at > NOW()
  ORDER BY hi.created_at DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON> execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_pending_invitation_for_user TO authenticated;
