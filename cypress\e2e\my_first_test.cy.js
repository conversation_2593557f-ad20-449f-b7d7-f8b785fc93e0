// This line tells VS Code to provide Cypress autocompletion
/// <reference types="cypress" />

// 'describe' is a test suite, a group of related tests
describe('Basic Website Navigation', () => {
  // 'it' is an individual test case
  it('successfully loads the home page and finds a welcome message (or other element)',() => {
    // Visit a URL. We'll start with Google for simplicity.
    // Later, you'll change this to your Expo web app's URL.
    cy.visit('/');

    // IMPORTANT: Replace this with actual text or a selector from YOUR APP's home page.
    // For example, if your app's home page has a specific heading:
    cy.contains('Bienvenue dans votre espace MedyTrack').should('be.visible');
  });
});