-- Fix critical onboarding issues
-- This script addresses user creation, RLS policies, and invitation handling

-- 1. <PERSON><PERSON> function to automatically create user entries
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert into users table when a new auth user is created (only id and created_at)
  INSERT INTO public.users (id, created_at)
  VALUES (
    NEW.id,
    NOW()
  ) ON CONFLICT (id) DO NOTHING;

  -- Also ensure profile exists
  INSERT INTO public.profiles (id, email, name, created_at, updated_at)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
    NOW(),
    NOW()
  ) ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    name = COALESCE(EXCLUDED.name, profiles.name),
    updated_at = NOW();

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- 2. Fix locations table RLS policies for onboarding
-- Drop existing problematic policies
DROP POLICY IF EXISTS "Users can view locations in their household" ON locations;
DROP POLICY IF EXISTS "Users can create locations in their household" ON locations;
DROP POLICY IF EXISTS "Users can update locations in their household" ON locations;
DROP POLICY IF EXISTS "Users can delete locations in their household" ON locations;

-- Create new, simpler policies for locations
CREATE POLICY "locations_select_policy" ON locations
  FOR SELECT USING (
    household_id = (SELECT household_id FROM users WHERE id = auth.uid())
  );

CREATE POLICY "locations_insert_policy" ON locations
  FOR INSERT WITH CHECK (
    household_id = (SELECT household_id FROM users WHERE id = auth.uid())
  );

CREATE POLICY "locations_update_policy" ON locations
  FOR UPDATE USING (
    household_id = (SELECT household_id FROM users WHERE id = auth.uid())
  );

CREATE POLICY "locations_delete_policy" ON locations
  FOR DELETE USING (
    household_id = (SELECT household_id FROM users WHERE id = auth.uid())
  );

-- 3. Create function to check for pending invitations
CREATE OR REPLACE FUNCTION get_pending_invitation_for_user(user_email TEXT)
RETURNS TABLE (
  id UUID,
  household_id UUID,
  household_name TEXT,
  invited_by_email TEXT,
  token TEXT,
  permissions JSONB,
  expires_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    hi.id,
    hi.household_id,
    h.name as household_name,
    p.email as invited_by_email,
    hi.token,
    hi.permissions,
    hi.expires_at
  FROM household_invitations hi
  JOIN households h ON hi.household_id = h.id
  JOIN profiles p ON hi.invited_by = p.id
  WHERE hi.email = user_email
    AND hi.status = 'pending'
    AND hi.expires_at > NOW()
  ORDER BY hi.created_at DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_pending_invitation_for_user TO authenticated;

-- 4. Create function to accept invitation during onboarding
CREATE OR REPLACE FUNCTION accept_invitation_during_onboarding(
  p_invitation_id UUID,
  p_user_id UUID
)
RETURNS JSONB AS $$
DECLARE
  v_invitation RECORD;
  v_household_id UUID;
BEGIN
  -- Get invitation details
  SELECT * INTO v_invitation
  FROM household_invitations
  WHERE id = p_invitation_id
    AND status = 'pending'
    AND expires_at > NOW();
  
  IF NOT FOUND THEN
    RETURN jsonb_build_object('success', false, 'error', 'Invitation not found or expired');
  END IF;
  
  -- Update user with household
  UPDATE users 
  SET household_id = v_invitation.household_id
  WHERE id = p_user_id;
  
  -- Add user as household member
  INSERT INTO household_members (
    household_id,
    user_id,
    role,
    permissions
  ) VALUES (
    v_invitation.household_id,
    p_user_id,
    'member',
    v_invitation.permissions
  ) ON CONFLICT (household_id, user_id) DO UPDATE SET
    permissions = EXCLUDED.permissions;
  
  -- Mark invitation as accepted
  UPDATE household_invitations
  SET status = 'accepted', updated_at = NOW()
  WHERE id = p_invitation_id;
  
  -- Mark onboarding as completed
  UPDATE profiles
  SET onboarding_completed = true
  WHERE id = p_user_id;
  
  RETURN jsonb_build_object(
    'success', true, 
    'household_id', v_invitation.household_id,
    'household_name', (SELECT name FROM households WHERE id = v_invitation.household_id)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION accept_invitation_during_onboarding TO authenticated;

-- 5. Ensure existing users have entries in users table
INSERT INTO users (id, created_at)
SELECT
  au.id,
  au.created_at
FROM auth.users au
LEFT JOIN users u ON au.id = u.id
WHERE u.id IS NULL
ON CONFLICT (id) DO NOTHING;

-- 6. Ensure RLS is enabled on all necessary tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE households ENABLE ROW LEVEL SECURITY;

-- 7. Create simple RLS policies for users table
DROP POLICY IF EXISTS "Users can view their own record" ON users;
CREATE POLICY "users_own_record_policy" ON users
  FOR ALL USING (id = auth.uid());

-- 8. Create simple RLS policies for profiles table
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
CREATE POLICY "profiles_own_record_policy" ON profiles
  FOR ALL USING (id = auth.uid());

-- 9. Create simple RLS policies for households table
DROP POLICY IF EXISTS "Users can view their household" ON households;
CREATE POLICY "households_view_policy" ON households
  FOR SELECT USING (
    id = (SELECT household_id FROM users WHERE id = auth.uid())
    OR created_by = auth.uid()
  );

CREATE POLICY "households_insert_policy" ON households
  FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "households_update_policy" ON households
  FOR UPDATE USING (
    id = (SELECT household_id FROM users WHERE id = auth.uid())
  );
