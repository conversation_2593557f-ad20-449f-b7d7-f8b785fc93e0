import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../domain/entities/user.dart';
import '../../../domain/usecases/auth/sign_in_usecase.dart';
import '../../../domain/usecases/auth/sign_up_usecase.dart';
import '../../../domain/usecases/auth/sign_out_usecase.dart';
import '../../../domain/usecases/auth/get_current_user_usecase.dart';
import '../../../core/errors/failures.dart';

part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final SignInUseCase signInUseCase;
  final SignUpUseCase signUpUseCase;
  final SignOutUseCase signOutUseCase;
  final GetCurrentUserUseCase getCurrentUserUseCase;

  AuthBloc({
    required this.signInUseCase,
    required this.signUpUseCase,
    required this.signOutUseCase,
    required this.getCurrentUserUseCase,
  }) : super(AuthInitial()) {
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<AuthSignInRequested>(_onSignInRequested);
    on<AuthSignUpRequested>(_onSignUpRequested);
    on<AuthSignOutRequested>(_onSignOutRequested);
    on<AuthUserUpdated>(_onUserUpdated);
    on<AuthUpdateProfileRequested>(_onUpdateProfileRequested);
  }

  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    final result = await getCurrentUserUseCase();

    result.fold(
      (failure) {
        if (failure is AuthenticationFailure) {
          emit(AuthUnauthenticated());
        } else {
          emit(AuthError(failure.message));
        }
      },
      (user) {
        if (user != null) {
          if (user.hasCompletedOnboarding) {
            emit(AuthAuthenticated(user));
          } else {
            emit(AuthOnboardingRequired(user));
          }
        } else {
          emit(AuthUnauthenticated());
        }
      },
    );
  }

  Future<void> _onSignInRequested(
    AuthSignInRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    final result = await signInUseCase(
      SignInParams(email: event.email, password: event.password),
    );

    result.fold((failure) => emit(AuthError(failure.message)), (user) {
      if (user.hasCompletedOnboarding) {
        emit(AuthAuthenticated(user));
      } else {
        emit(AuthOnboardingRequired(user));
      }
    });
  }

  Future<void> _onSignUpRequested(
    AuthSignUpRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    final result = await signUpUseCase(
      SignUpParams(
        email: event.email,
        password: event.password,
        name: event.name,
      ),
    );

    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (user) => emit(AuthOnboardingRequired(user)),
    );
  }

  Future<void> _onSignOutRequested(
    AuthSignOutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    final result = await signOutUseCase();

    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (_) => emit(AuthUnauthenticated()),
    );
  }

  void _onUserUpdated(AuthUserUpdated event, Emitter<AuthState> emit) {
    if (event.user.hasCompletedOnboarding) {
      emit(AuthAuthenticated(event.user));
    } else {
      emit(AuthOnboardingRequired(event.user));
    }
  }

  Future<void> _onUpdateProfileRequested(
    AuthUpdateProfileRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      // For now, simulate updating the user profile by creating a new user
      // In a real implementation, this would call an UpdateUserUseCase
      final currentState = state;
      if (currentState is AuthOnboardingRequired) {
        final updatedUser = currentState.user.copyWith(
          name: event.name ?? currentState.user.name,
          householdId: event.householdId ?? currentState.user.householdId,
          householdName: event.householdName ?? currentState.user.householdName,
          isOnboardingCompleted:
              event.isOnboardingCompleted ??
              currentState.user.isOnboardingCompleted,
        );

        // Check if profile is now complete
        if (updatedUser.hasCompletedOnboarding) {
          emit(AuthAuthenticated(updatedUser));
        } else {
          emit(AuthOnboardingRequired(updatedUser));
        }
      } else {
        emit(AuthError('Cannot update profile: user not in onboarding state'));
      }
    } catch (e) {
      emit(AuthError('Failed to update profile: $e'));
    }
  }
}
