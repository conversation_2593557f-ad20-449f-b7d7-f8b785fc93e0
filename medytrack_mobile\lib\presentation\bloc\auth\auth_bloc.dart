import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../domain/entities/user.dart';
import '../../../domain/usecases/auth/sign_in_usecase.dart';
import '../../../domain/usecases/auth/sign_up_usecase.dart';
import '../../../domain/usecases/auth/sign_out_usecase.dart';
import '../../../domain/usecases/auth/get_current_user_usecase.dart';
import '../../../domain/usecases/auth/update_profile_usecase.dart';
import '../../../core/errors/failures.dart';

part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final SignInUseCase signInUseCase;
  final SignUpUseCase signUpUseCase;
  final SignOutUseCase signOutUseCase;
  final GetCurrentUserUseCase getCurrentUserUseCase;
  final UpdateProfileUseCase updateProfileUseCase;

  AuthBloc({
    required this.signInUseCase,
    required this.signUpUseCase,
    required this.signOutUseCase,
    required this.getCurrentUserUseCase,
    required this.updateProfileUseCase,
  }) : super(AuthInitial()) {
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<AuthSignInRequested>(_onSignInRequested);
    on<AuthSignUpRequested>(_onSignUpRequested);
    on<AuthSignOutRequested>(_onSignOutRequested);
    on<AuthUserUpdated>(_onUserUpdated);
    on<AuthUpdateProfileRequested>(_onUpdateProfileRequested);
  }

  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    print('🔍 AuthBloc: Starting auth check...');
    emit(AuthLoading());

    final result = await getCurrentUserUseCase();

    result.fold(
      (failure) {
        print('❌ AuthBloc: Auth check failed - ${failure.message}');
        if (failure is AuthenticationFailure) {
          emit(AuthUnauthenticated());
        } else {
          emit(AuthError(failure.message));
        }
      },
      (user) {
        if (user != null) {
          print('✅ AuthBloc: User found - ${user.email}');
          print(
            '📊 AuthBloc: Profile status - ${user.profileCompletenessStatus}',
          );
          print(
            '🏠 AuthBloc: hasCompletedOnboarding - ${user.hasCompletedOnboarding}',
          );

          if (user.hasCompletedOnboarding) {
            print('🎯 AuthBloc: Emitting AuthAuthenticated');
            emit(AuthAuthenticated(user));
          } else {
            print('🎯 AuthBloc: Emitting AuthOnboardingRequired');
            emit(AuthOnboardingRequired(user));
          }
        } else {
          print('❌ AuthBloc: No user found');
          emit(AuthUnauthenticated());
        }
      },
    );
  }

  Future<void> _onSignInRequested(
    AuthSignInRequested event,
    Emitter<AuthState> emit,
  ) async {
    print('🔐 AuthBloc: Starting sign in for ${event.email}...');
    emit(AuthLoading());

    final result = await signInUseCase(
      SignInParams(email: event.email, password: event.password),
    );

    result.fold(
      (failure) {
        print('❌ AuthBloc: Sign in failed - ${failure.message}');
        emit(AuthError(failure.message));
      },
      (user) {
        print('✅ AuthBloc: Sign in successful - ${user.email}');
        print(
          '📊 AuthBloc: Profile status - ${user.profileCompletenessStatus}',
        );
        print(
          '🏠 AuthBloc: hasCompletedOnboarding - ${user.hasCompletedOnboarding}',
        );

        if (user.hasCompletedOnboarding) {
          print('🎯 AuthBloc: Emitting AuthAuthenticated');
          emit(AuthAuthenticated(user));
        } else {
          print('🎯 AuthBloc: Emitting AuthOnboardingRequired');
          emit(AuthOnboardingRequired(user));
        }
      },
    );
  }

  Future<void> _onSignUpRequested(
    AuthSignUpRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    final result = await signUpUseCase(
      SignUpParams(
        email: event.email,
        password: event.password,
        name: event.name,
      ),
    );

    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (user) => emit(AuthOnboardingRequired(user)),
    );
  }

  Future<void> _onSignOutRequested(
    AuthSignOutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    final result = await signOutUseCase();

    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (_) => emit(AuthUnauthenticated()),
    );
  }

  void _onUserUpdated(AuthUserUpdated event, Emitter<AuthState> emit) {
    if (event.user.hasCompletedOnboarding) {
      emit(AuthAuthenticated(event.user));
    } else {
      emit(AuthOnboardingRequired(event.user));
    }
  }

  Future<void> _onUpdateProfileRequested(
    AuthUpdateProfileRequested event,
    Emitter<AuthState> emit,
  ) async {
    print('🔄 AuthBloc: Starting profile update...');
    emit(AuthLoading());

    try {
      final result = await updateProfileUseCase(
        UpdateProfileParams(
          name: event.name,
          householdId: event.householdId,
          householdName: event.householdName,
          isOnboardingCompleted: event.isOnboardingCompleted,
        ),
      );

      result.fold(
        (failure) {
          print('❌ AuthBloc: Profile update failed - ${failure.message}');
          emit(AuthError('Failed to update profile: ${failure.message}'));
        },
        (updatedUser) {
          print('✅ AuthBloc: Profile update successful');
          print(
            '📊 AuthBloc: Updated profile status - ${updatedUser.profileCompletenessStatus}',
          );
          print(
            '🏠 AuthBloc: Updated hasCompletedOnboarding - ${updatedUser.hasCompletedOnboarding}',
          );

          // Check if profile is now complete
          if (updatedUser.hasCompletedOnboarding) {
            print('🎯 AuthBloc: Emitting AuthAuthenticated (profile complete)');
            emit(AuthAuthenticated(updatedUser));
          } else {
            print(
              '🎯 AuthBloc: Emitting AuthOnboardingRequired (profile incomplete)',
            );
            emit(AuthOnboardingRequired(updatedUser));
          }
        },
      );
    } catch (e) {
      print('❌ AuthBloc: Profile update exception - $e');
      emit(AuthError('Failed to update profile: $e'));
    }
  }
}
