
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { DashboardData, MedicineExpiringSoon } from '@/types';
import { toast } from 'sonner';

export const useDashboardData = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    totalMedicines: 0,
    expiringSoon: 0,
    categoryCount: 0,
    locationCount: 0,
    totalLocations: 0,
    totalCategories: 7, // Fixed number of available categories
    locationUsagePercentage: 0,
    categoryUsagePercentage: 0
  });
  const [expiringMedicines, setExpiringMedicines] = useState<MedicineExpiringSoon[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user, profile, householdId } = useAuth();

  const fetchDashboardData = async () => {
    if (!user || !householdId) return;
    setIsLoading(true);
    
    try {
      // Use the user_dashboard_data view to get dashboard counts
      const { data: dashboardDataResult, error: dashboardError } = await supabase
        .from('user_dashboard_data')
        .select('*')
        .eq('household_id', householdId)
        .maybeSingle();
        
      if (dashboardError) throw dashboardError;
      
      // Get total available locations for the household
      const { data: totalLocationsData, error: totalLocationsError } = await supabase
        .from('locations')
        .select('id')
        .eq('household_id', householdId);

      if (totalLocationsError) throw totalLocationsError;

      const totalLocations = totalLocationsData?.length || 0;

      // Create dashboard summary object
      const dashboardSummary: DashboardData = {
        totalMedicines: dashboardDataResult?.total_medicines || 0,
        expiringSoon: dashboardDataResult?.expiring_soon_count || 0,
        categoryCount: 0, // We'll calculate this separately as it may not be in the view
        locationCount: dashboardDataResult?.location_count || 0,
        totalLocations: totalLocations,
        totalCategories: 7, // Fixed number of available categories
        locationUsagePercentage: 0,
        categoryUsagePercentage: 0
      };
      
      // Get unique categories count
      const { data: categoryData, error: categoryError } = await supabase
        .from('user_medicines')
        .select('category')
        .eq('household_id', householdId)
        .not('category', 'is', null);
        
      if (categoryError) throw categoryError;
      
      if (categoryData) {
        dashboardSummary.categoryCount = new Set(categoryData.map(item => item.category)).size;
      }

      // Calculate percentages
      dashboardSummary.locationUsagePercentage = totalLocations > 0
        ? Math.round((dashboardSummary.locationCount / totalLocations) * 100)
        : 0;
      dashboardSummary.categoryUsagePercentage = dashboardSummary.totalCategories > 0
        ? Math.round((dashboardSummary.categoryCount / dashboardSummary.totalCategories) * 100)
        : 0;

      setDashboardData(dashboardSummary);

      // Use dashboard_medicine_alerts_view instead of medicines_expiring_soon
      const { data: expiringData, error: expiringError } = await supabase
        .from('dashboard_medicine_alerts_view')
        .select('*')
        .eq('household_id', householdId)
        .eq('expiration_status', 'expiring_soon')
        .limit(3);

      if (expiringError) throw expiringError;
      
      if (expiringData) {
        const formattedExpiring: MedicineExpiringSoon[] = expiringData.map(item => {
          // Get the appropriate name based on whether it's a custom medicine
          const medicineName = item.is_custom 
            ? item.custom_name 
            : (item.medicine_name || item.official_label || 'Médicament sans nom');
          
          return {
            id: item.user_medicine_id,
            label: medicineName,
            expirationDate: item.expiration,
            quantity: item.quantity || 0,
            familyMemberName: item.family_member_name || "",
            location: item.location_name || ""
          };
        });
        
        setExpiringMedicines(formattedExpiring);
      }
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      toast.error('Erreur lors du chargement des données du tableau de bord');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user && householdId) {
      fetchDashboardData();
    }
  }, [user, householdId]);

  return {
    dashboardData,
    expiringMedicines,
    isLoading,
    refresh: fetchDashboardData
  };
};
