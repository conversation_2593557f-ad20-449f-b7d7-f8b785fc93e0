
import React from "react";
import MedicineCard from "./MedicineCard";
import { Medicine } from "@/types";

interface MedicineListProps {
  medicines: Medicine[];
  onMedicineClick?: (medicine: Medicine) => void;
}

const MedicineList = ({ medicines, onMedicineClick }: MedicineListProps) => {
  if (medicines.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Aucun médicament trouvé</p>
      </div>
    );
  }
  
  return (
    <div className="space-y-4">
      {medicines.map((medicine) => (
        <MedicineCard
          key={medicine.id}
          medicine={medicine}
          onClick={() => onMedicineClick && onMedicineClick(medicine)}
        />
      ))}
    </div>
  );
};

export default MedicineList;
