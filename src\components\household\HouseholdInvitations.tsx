import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { QRCodeSVG } from 'qrcode.react';
import { Mail, QrCode, Copy, UserPlus, Trash2, Clock, CheckCircle, XCircle } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

interface Invitation {
  id: string;
  email: string;
  status: 'pending' | 'accepted' | 'expired' | 'revoked';
  expires_at: string;
  created_at: string;
  invited_by: string;
}

const HouseholdInvitations = () => {
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showQRDialog, setShowQRDialog] = useState(false);
  const [inviteLink, setInviteLink] = useState('');
  const [currentInviteToken, setCurrentInviteToken] = useState('');
  const { user, householdId } = useAuth();

  useEffect(() => {
    if (householdId) {
      fetchInvitations();
    }
  }, [householdId]);

  const fetchInvitations = async () => {
    if (!householdId) return;

    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('household_invitations')
        .select('*')
        .eq('household_id', householdId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Database error fetching invitations:', error);
        throw error;
      }
      setInvitations(data || []);
    } catch (err: any) {
      console.error('Error fetching invitations:', err);
      if (err.code === '42P17') {
        toast.error('Erreur de configuration de la base de données. Veuillez contacter le support.');
      } else {
        toast.error('Erreur lors du chargement des invitations');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const createInvitation = async () => {
    if (!email || !user || !householdId) return;

    setIsLoading(true);
    try {
      const { data, error } = await supabase.rpc('create_household_invitation', {
        p_household_id: householdId,
        p_invited_by: user.id,
        p_email: email
      });

      if (error) throw error;

      // Get the invitation token for the link
      const { data: invitation, error: inviteError } = await supabase
        .from('household_invitations')
        .select('token')
        .eq('id', data)
        .single();

      if (inviteError) throw inviteError;

      const link = `${window.location.origin}/join-household?token=${invitation.token}`;
      setInviteLink(link);
      setCurrentInviteToken(invitation.token);

      // Send email invitation (in a real app, this would be handled by a backend service)
      toast.success('Invitation créée avec succès! Vous pouvez maintenant partager le QR code.');
      setEmail('');
      setShowQRDialog(true); // Automatically show QR code
      fetchInvitations();
    } catch (err) {
      console.error('Error creating invitation:', err);
      toast.error('Erreur lors de la création de l\'invitation');
    } finally {
      setIsLoading(false);
    }
  };

  const revokeInvitation = async (invitationId: string) => {
    if (!user) return;

    try {
      const { data, error } = await supabase.rpc('revoke_household_invitation', {
        p_invitation_id: invitationId,
        p_user_id: user.id
      });

      if (error) throw error;

      if (data) {
        toast.success('Invitation révoquée');
        fetchInvitations();
      } else {
        toast.error('Impossible de révoquer cette invitation');
      }
    } catch (err) {
      console.error('Error revoking invitation:', err);
      toast.error('Erreur lors de la révocation');
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Lien copié dans le presse-papiers');
  };

  const showQRForInvitation = async (invitationId: string) => {
    try {
      const { data: invitation, error } = await supabase
        .from('household_invitations')
        .select('token')
        .eq('id', invitationId)
        .single();

      if (error) throw error;

      const link = `${window.location.origin}/join-household?token=${invitation.token}`;
      setInviteLink(link);
      setCurrentInviteToken(invitation.token);
      setShowQRDialog(true);
    } catch (err) {
      console.error('Error getting invitation token:', err);
      toast.error('Erreur lors de la génération du QR code');
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700"><Clock className="w-3 h-3 mr-1" />En attente</Badge>;
      case 'accepted':
        return <Badge variant="outline" className="bg-green-50 text-green-700"><CheckCircle className="w-3 h-3 mr-1" />Acceptée</Badge>;
      case 'expired':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700"><XCircle className="w-3 h-3 mr-1" />Expirée</Badge>;
      case 'revoked':
        return <Badge variant="outline" className="bg-red-50 text-red-700"><XCircle className="w-3 h-3 mr-1" />Révoquée</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Create Invitation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-navy">
            <UserPlus className="w-5 h-5" />
            Inviter un membre
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Adresse email</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>
          
          <div className="flex gap-2">
            <Button 
              onClick={createInvitation}
              disabled={!email || isLoading}
              className="bg-teal hover:bg-teal-dark text-white"
            >
              <Mail className="w-4 h-4 mr-2" />
              Envoyer invitation
            </Button>
            
            {inviteLink && (
              <Dialog open={showQRDialog} onOpenChange={setShowQRDialog}>
                <DialogTrigger asChild>
                  <Button variant="outline" className="border-teal text-teal">
                    <QrCode className="w-4 h-4 mr-2" />
                    QR Code
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>Code QR d'invitation au foyer</DialogTitle>
                  </DialogHeader>
                  <div className="flex flex-col items-center space-y-4">
                    <div className="p-4 bg-white rounded-lg border-2 border-gray-200">
                      <QRCodeSVG
                        value={inviteLink}
                        size={200}
                        level="M"
                        includeMargin={true}
                      />
                    </div>
                    <div className="text-center space-y-2">
                      <p className="text-sm text-gray-600">
                        Scannez ce code QR pour rejoindre le foyer
                      </p>
                      <p className="text-xs text-gray-500">
                        Token: {currentInviteToken.substring(0, 8)}...
                      </p>
                    </div>
                    <div className="flex items-center gap-2 w-full">
                      <Input value={inviteLink} readOnly className="flex-1 text-xs" />
                      <Button
                        size="icon"
                        variant="outline"
                        onClick={() => copyToClipboard(inviteLink)}
                        className="border-teal text-teal hover:bg-teal/10"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="text-xs text-gray-500 text-center">
                      Partagez ce QR code ou le lien avec la personne que vous souhaitez inviter
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Invitations List */}
      <Card>
        <CardHeader>
          <CardTitle className="text-navy">Invitations envoyées</CardTitle>
        </CardHeader>
        <CardContent>
          {invitations.length === 0 ? (
            <p className="text-gray-500 text-center py-4">Aucune invitation envoyée</p>
          ) : (
            <div className="space-y-3">
              {invitations.map((invitation) => (
                <div key={invitation.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <p className="font-medium text-navy">{invitation.email}</p>
                    <p className="text-sm text-gray-600">
                      Envoyée le {new Date(invitation.created_at).toLocaleDateString('fr-FR')}
                    </p>
                    {invitation.status === 'pending' && (
                      <p className="text-xs text-gray-500">
                        Expire le {new Date(invitation.expires_at).toLocaleDateString('fr-FR')}
                      </p>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {getStatusBadge(invitation.status)}
                    {invitation.status === 'pending' && (
                      <>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => showQRForInvitation(invitation.id)}
                          className="text-teal border-teal hover:bg-teal/10"
                        >
                          <QrCode className="w-3 h-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => revokeInvitation(invitation.id)}
                          className="text-red-600 border-red-200 hover:bg-red-50"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default HouseholdInvitations;
