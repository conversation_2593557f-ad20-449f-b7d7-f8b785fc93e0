import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import '../../domain/entities/user.dart';

class UserModel extends User {
  const UserModel({
    required super.id,
    required super.email,
    super.name,
    super.avatarUrl,
    super.householdId,
    super.householdName,
    super.expiryWarningMonths,
    super.isOnboardingCompleted,
    required super.createdAt,
    required super.updatedAt,
    super.lastLoginAt,
  });

  factory UserModel.fromSupabaseUser(supabase.User user) {
    final name = user.userMetadata?['name'] as String?;
    final householdId = user.userMetadata?['household_id'] as String?;
    final householdName = user.userMetadata?['household_name'] as String?;
    final isOnboardingCompleted =
        (user.userMetadata?['onboarding_completed'] as bool?) ?? false;

    return UserModel(
      id: user.id,
      email: user.email ?? '',
      name: name,
      avatarUrl: user.userMetadata?['avatar_url'] as String?,
      householdId: householdId,
      householdName: householdName,
      expiryWarningMonths:
          (user.userMetadata?['expiry_warning_months'] as int?) ?? 1,
      isOnboardingCompleted: isOnboardingCompleted,
      createdAt: DateTime.parse(user.createdAt),
      updatedAt: user.updatedAt != null
          ? DateTime.parse(user.updatedAt!)
          : DateTime.parse(user.createdAt),
      lastLoginAt: user.lastSignInAt != null
          ? DateTime.parse(user.lastSignInAt!)
          : null,
    );
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['name'] as String?,
      avatarUrl: json['avatar_url'] as String?,
      householdId: json['household_id'] as String?,
      householdName: json['household_name'] as String?,
      expiryWarningMonths: (json['expiry_warning_months'] as int?) ?? 1,
      isOnboardingCompleted: (json['onboarding_completed'] as bool?) ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      lastLoginAt: json['last_login_at'] != null
          ? DateTime.parse(json['last_login_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'avatar_url': avatarUrl,
      'household_id': householdId,
      'household_name': householdName,
      'expiry_warning_months': expiryWarningMonths,
      'onboarding_completed': isOnboardingCompleted,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_login_at': lastLoginAt?.toIso8601String(),
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    String? avatarUrl,
    String? householdId,
    String? householdName,
    int? expiryWarningMonths,
    bool? isOnboardingCompleted,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      householdId: householdId ?? this.householdId,
      householdName: householdName ?? this.householdName,
      expiryWarningMonths: expiryWarningMonths ?? this.expiryWarningMonths,
      isOnboardingCompleted:
          isOnboardingCompleted ?? this.isOnboardingCompleted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }
}
