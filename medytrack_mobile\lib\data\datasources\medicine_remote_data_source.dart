import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:dio/dio.dart';
import '../models/medicine_model.dart';

abstract class MedicineRemoteDataSource {
  Future<List<MedicineModel>> getMedicines();
  Future<MedicineModel> addMedicine(MedicineModel medicine);
  Future<MedicineModel> updateMedicine(MedicineModel medicine);
  Future<void> deleteMedicine(String id);
}

class MedicineRemoteDataSourceImpl implements MedicineRemoteDataSource {
  final SupabaseClient supabaseClient;
  final Dio dio;

  MedicineRemoteDataSourceImpl({
    required this.supabaseClient,
    required this.dio,
  });

  @override
  Future<List<MedicineModel>> getMedicines() async {
    try {
      final response = await supabaseClient
          .from('user_medicines')
          .select()
          .order('created_at', ascending: false);

      return (response as List)
          .map((json) => MedicineModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to get medicines: ${e.toString()}');
    }
  }

  @override
  Future<MedicineModel> addMedicine(MedicineModel medicine) async {
    try {
      final response = await supabaseClient
          .from('user_medicines')
          .insert(medicine.toJson())
          .select()
          .single();

      return MedicineModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to add medicine: ${e.toString()}');
    }
  }

  @override
  Future<MedicineModel> updateMedicine(MedicineModel medicine) async {
    try {
      final response = await supabaseClient
          .from('user_medicines')
          .update(medicine.toJson())
          .eq('id', medicine.id)
          .select()
          .single();

      return MedicineModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update medicine: ${e.toString()}');
    }
  }

  @override
  Future<void> deleteMedicine(String id) async {
    try {
      await supabaseClient
          .from('user_medicines')
          .delete()
          .eq('id', id);
    } catch (e) {
      throw Exception('Failed to delete medicine: ${e.toString()}');
    }
  }
}
