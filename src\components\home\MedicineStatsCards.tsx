import React, { useEffect, useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { AlertTriangle, Clock, Pill, Package } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { isExpired, isNearExpiry } from "@/utils/helpers";

interface MedicineStats {
  expired: number;
  expiringSoon: number;
  lowStock: number;
  total: number;
}

const MedicineStatsCards = () => {
  const [stats, setStats] = useState<MedicineStats>({
    expired: 0,
    expiringSoon: 0,
    lowStock: 0,
    total: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const { householdId } = useAuth();

  const fetchMedicineStats = useCallback(async () => {
    if (!householdId) return;

    try {
      setIsLoading(true);
      // Get medicine data with thresholds from user_medicines table
      const { data, error } = await supabase
        .from('user_medicines')
        .select('expiration, quantity, low_stock_threshold')
        .eq('household_id', householdId);

      if (error) throw error;

      if (data) {
        const counts = {
          expired: 0,
          expiringSoon: 0,
          lowStock: 0,
          total: data.length
        };

        // Calculate expiry status and low stock using client-side logic for accuracy
        data.forEach((item) => {
          if (item.expiration) {
            if (isExpired(item.expiration)) {
              counts.expired++;
            } else if (isNearExpiry(item.expiration)) {
              counts.expiringSoon++;
            }
          }

          // Check for low stock
          const threshold = item.low_stock_threshold || 0;
          if (item.quantity <= threshold && item.quantity > 0) {
            counts.lowStock++;
          }
        });

        setStats(counts);
      }
    } catch (err) {
      console.error('Error fetching medicine stats:', err);
    } finally {
      setIsLoading(false);
    }
  }, [householdId]);

  useEffect(() => {
    fetchMedicineStats();
  }, [fetchMedicineStats]);
  
  const navigateToFilteredMedicines = (filter: string) => {
    // Navigate to My Medicines page with filter parameter
    navigate(`/my-medicines?filter=${filter}`);
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4 text-center">
              <div className="flex flex-col items-center space-y-3">
                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                <div className="space-y-2">
                  <div className="h-4 w-24 bg-gray-200 rounded"></div>
                  <div className="h-8 w-16 bg-gray-200 rounded"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {/* Total Medicines Card */}
      <Card
        className="cursor-pointer hover:shadow-md transition-shadow border-l-4 border-r-4 border-l-teal border-r-teal"
        onClick={() => navigate('/my-medicines')}
      >
        <CardContent className="p-4 text-center min-h-[120px] flex items-center justify-center">
          <div className="flex flex-col items-center space-y-3">
            <div className="p-3 bg-teal/10 rounded-full">
              <Pill className="h-6 w-6 text-teal" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Total Médicaments</p>
              <p className="text-2xl font-bold text-navy">{stats.total}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Expired Medicines Card */}
      <Card
        className={`cursor-pointer hover:shadow-md transition-shadow border-l-4 border-r-4 border-l-red-500 border-r-red-500 ${
          stats.expired > 0 ? 'bg-red-50' : ''
        }`}
        onClick={() => navigateToFilteredMedicines('expired')}
      >
        <CardContent className="p-4 text-center min-h-[120px] flex items-center justify-center">
          <div className="flex flex-col items-center space-y-3">
            <div className="p-3 bg-red-100 rounded-full">
              <AlertTriangle className="h-6 w-6 text-red-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Médicaments Expirés</p>
              <p className="text-2xl font-bold text-red-600">{stats.expired}</p>
              {stats.expired > 0 && (
                <p className="text-xs text-red-500 mt-1">Action requise</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Low Stock Card */}
      <Card
        className={`cursor-pointer hover:shadow-md transition-shadow border-l-4 border-r-4 border-l-orange-500 border-r-orange-500 ${
          stats.lowStock > 0 ? 'bg-orange-50' : ''
        }`}
        onClick={() => navigateToFilteredMedicines('low-stock')}
      >
        <CardContent className="p-4 text-center min-h-[120px] flex items-center justify-center">
          <div className="flex flex-col items-center space-y-3">
            <div className="p-3 bg-orange-100 rounded-full">
              <Package className="h-6 w-6 text-orange-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Stock Faible</p>
              <p className="text-2xl font-bold text-orange-600">{stats.lowStock}</p>
              {stats.lowStock > 0 && (
                <p className="text-xs text-orange-600 mt-1">À réapprovisionner</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Expiring Soon Card */}
      <Card
        className={`cursor-pointer hover:shadow-md transition-shadow border-l-4 border-r-4 border-l-amber-500 border-r-amber-500 ${
          stats.expiringSoon > 0 ? 'bg-amber-50' : ''
        }`}
        onClick={() => navigateToFilteredMedicines('expiring-soon')}
      >
        <CardContent className="p-4 text-center min-h-[120px] flex items-center justify-center">
          <div className="flex flex-col items-center space-y-3">
            <div className="p-3 bg-amber-100 rounded-full">
              <Clock className="h-6 w-6 text-amber-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Expirent Bientôt</p>
              <p className="text-2xl font-bold text-amber-600">{stats.expiringSoon}</p>
              {stats.expiringSoon > 0 && (
                <p className="text-xs text-amber-600 mt-1">Dans 30 jours</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default React.memo(MedicineStatsCards);
