
import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { User } from "@supabase/supabase-js";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { CircleUser, Loader2, Trash, Upload } from "lucide-react";

interface ProfileAvatarProps {
  user: User;
}

const ProfileAvatar = ({ user }: ProfileAvatarProps) => {
  const [isUploading, setIsUploading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const avatarUrl = user.user_metadata?.avatar_url;
  
  const uploadAvatar = async (file: File) => {
    setIsUploading(true);
    
    try {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        toast.error("Veuillez sélectionner une image valide");
        return;
      }
      
      // Maximum file size: 2MB
      if (file.size > 2 * 1024 * 1024) {
        toast.error("L'image ne doit pas dépasser 2MB");
        return;
      }

      // Upload the file to Supabase Storage
      const filePath = `${user.id}/${Date.now()}.png`;
      const { error: uploadError } = await supabase
        .storage
        .from('avatars')
        .upload(filePath, file);
        
      if (uploadError) {
        toast.error("Erreur lors de l'upload", {
          description: uploadError.message
        });
        return;
      }
      
      // Get public URL
      const { data } = supabase
        .storage
        .from('avatars')
        .getPublicUrl(filePath);
      
      // Update user metadata
      const { error: updateError } = await supabase.auth.updateUser({
        data: { avatar_url: data.publicUrl }
      });
      
      if (updateError) {
        toast.error("Erreur lors de la mise à jour du profil", {
          description: updateError.message
        });
        return;
      }
      
      toast.success("Avatar mis à jour avec succès");
    } catch (error) {
      toast.error("Une erreur est survenue");
    } finally {
      setIsUploading(false);
    }
  };
  
  const deleteAvatar = async () => {
    if (!avatarUrl) return;
    
    setIsDeleting(true);
    try {
      // Extract file path from public URL
      const urlPath = new URL(avatarUrl).pathname;
      const filePath = urlPath.split("/").slice(-2).join("/");
      
      // Delete from storage if it's stored in our bucket
      if (avatarUrl.includes("avatars")) {
        await supabase
          .storage
          .from('avatars')
          .remove([filePath]);
      }
      
      // Update user metadata
      const { error } = await supabase.auth.updateUser({
        data: { avatar_url: null }
      });
      
      if (error) {
        toast.error("Erreur lors de la suppression de l'avatar", {
          description: error.message
        });
        return;
      }
      
      toast.success("Avatar supprimé avec succès");
    } catch (error) {
      toast.error("Une erreur est survenue");
    } finally {
      setIsDeleting(false);
    }
  };
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      uploadAvatar(e.target.files[0]);
    }
  };
  
  return (
    <div className="flex flex-col items-center gap-2">
      <Avatar className="w-24 h-24 border-2 border-primary/20">
        {avatarUrl ? (
          <AvatarImage src={avatarUrl} alt="Avatar" />
        ) : null}
        <AvatarFallback className="bg-primary/10">
          <CircleUser className="h-12 w-12 text-muted-foreground" />
        </AvatarFallback>
      </Avatar>
      
      <div className="flex flex-wrap gap-2 justify-center mt-2">
        <Button 
          variant="outline" 
          size="sm" 
          className="flex gap-1"
          disabled={isUploading || isDeleting}
          asChild
        >
          <label htmlFor="avatar-upload" className="cursor-pointer">
            {isUploading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Upload className="h-4 w-4" />
            )}
            {isUploading ? 'Téléchargement...' : 'Changer'}
            <input 
              id="avatar-upload"
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              className="sr-only"
              disabled={isUploading || isDeleting}
            />
          </label>
        </Button>
        
        {avatarUrl && (
          <Button 
            variant="outline"
            size="sm" 
            className="flex gap-1"
            onClick={deleteAvatar}
            disabled={isDeleting || isUploading}
          >
            {isDeleting ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Trash className="h-4 w-4" />
            )}
            {isDeleting ? 'Suppression...' : 'Supprimer'}
          </Button>
        )}
      </div>
    </div>
  );
};

export default ProfileAvatar;
