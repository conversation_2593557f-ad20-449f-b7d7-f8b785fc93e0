
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Medicine, Category, Location } from "@/types";

interface MedicineFormProps {
  formData: Partial<Medicine>;
  onFormChange: (field: keyof Medicine, value: any) => void;
  onSubmit: (e: React.FormEvent) => void;
  locations?: Location[];
}

const MedicineForm = ({ formData, onFormChange, onSubmit, locations = [] }: MedicineFormProps) => {
  const categories = [
    { id: "pain", name: "<PERSON>ule<PERSON>" },
    { id: "cold", name: "Rhume" },
    { id: "allergy", name: "Allergie" },
    { id: "digestion", name: "Digestion" },
    { id: "first-aid", name: "Premiers soins" },
    { id: "prescription", name: "Ordonnance" },
    { id: "other", name: "<PERSON><PERSON>" },
  ];

  return (
    <form onSubmit={onSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Nom*</Label>
        <Input
          id="name"
          value={formData.name || ''}
          onChange={(e) => onFormChange('name', e.target.value)}
          readOnly={!!formData.name}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="dosage">Dosage</Label>
        <Input
          id="dosage"
          value={formData.dosage || ''}
          onChange={(e) => onFormChange('dosage', e.target.value)}
          placeholder="Ex: 500mg, 10ml..."
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="quantity">Quantité*</Label>
          <Input
            id="quantity"
            type="number"
            min={0}
            value={formData.quantity || 0}
            onChange={(e) => onFormChange('quantity', parseInt(e.target.value))}
            required
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="expiryDate">Date d'expiration</Label>
          <Input
            id="expiryDate"
            type="date"
            value={formData.expiryDate || ''}
            onChange={(e) => onFormChange('expiryDate', e.target.value)}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="category">Catégorie</Label>
        <Select 
          value={formData.category as string} 
          onValueChange={(value) => onFormChange('category', value as Category)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Choisir une catégorie" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="location">Emplacement</Label>
        <Select 
          value={formData.location as string} 
          onValueChange={(value) => onFormChange('location', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Choisir un emplacement" />
          </SelectTrigger>
          <SelectContent>
            {locations.map((location) => (
              <SelectItem key={location.id} value={location.id}>
                {location.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="notes">Notes</Label>
        <Textarea
          id="notes"
          value={formData.notes || ''}
          onChange={(e) => onFormChange('notes', e.target.value)}
          placeholder="Ajoutez des notes ou instructions..."
        />
      </div>

      <Button type="submit" className="w-full">Ajouter à mon armoire</Button>
    </form>
  );
};

export default MedicineForm;
