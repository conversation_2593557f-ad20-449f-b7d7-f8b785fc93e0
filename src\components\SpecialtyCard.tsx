
import React from 'react';
import { TunisiaMedicine } from '@/hooks/useSpecialtySearch';
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface SpecialtyCardProps {
  specialty: TunisiaMedicine;
  onClick: () => void;
}

const SpecialtyCard = ({ specialty, onClick }: SpecialtyCardProps) => {
  return (
    <Card 
      className="cursor-pointer hover:bg-accent/50 transition-colors"
      onClick={onClick}
    >
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-medium">{specialty.nom || 'Sans nom'}</h3>
            <p className="text-sm text-muted-foreground">AMM: {specialty.amm || 'Non spécifié'}</p>
          </div>
          {specialty.classe && (
            <Badge variant="outline" className="ml-2">
              {specialty.classe}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-2 text-sm">
          {specialty.forme && (
            <span className="text-muted-foreground">
              {specialty.forme}
            </span>
          )}
          {specialty.dosage && (
            <span className="text-muted-foreground">
              • {specialty.dosage}
            </span>
          )}
          {specialty.laboratoire && (
            <span className="text-muted-foreground">
              • {specialty.laboratoire}
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SpecialtyCard;
