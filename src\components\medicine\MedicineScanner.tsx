
import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Camera, Barcode, Search, CheckCircle, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

interface MedicineScannerProps {
  onMedicineFound?: (medicine: any) => void;
}

const MedicineScanner = ({ onMedicineFound }: MedicineScannerProps) => {
  const [isScanning, setIsScanning] = useState(false);
  const [manualBarcode, setManualBarcode] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [foundMedicine, setFoundMedicine] = useState<any>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // Cleanup camera stream on unmount
  useEffect(() => {
    return () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  const searchMedicineByBarcode = async (barcode: string) => {
    if (!barcode.trim()) {
      toast.error("Veuillez entrer un code-barres valide");
      return;
    }

    setIsSearching(true);
    try {
      const { data, error } = await supabase
        .from('tunisia_medicines')
        .select('*')
        .eq('amm', barcode.trim())
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      if (data) {
        setFoundMedicine(data);
        toast.success(`Médicament trouvé: ${data.nom}`);
        if (onMedicineFound) {
          onMedicineFound(data);
        }
      } else {
        setFoundMedicine(null);
        toast.error("Aucun médicament trouvé avec ce code-barres");
      }
    } catch (err) {
      toast.error("Erreur lors de la recherche du médicament");
    } finally {
      setIsSearching(false);
    }
  };

  const startCamera = async () => {
    try {
      setIsScanning(true);
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // Use back camera if available
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });

      streamRef.current = stream;
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (err) {
      console.error('Error accessing camera:', err);
      toast.error("Impossible d'accéder à la caméra");
      setIsScanning(false);
    }
  };

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setIsScanning(false);
  };

  const handleManualSearch = () => {
    searchMedicineByBarcode(manualBarcode);
  };

  return (
    <div className="space-y-6">
      {/* Manual Barcode Entry */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-navy">
            <Search className="w-5 h-5" />
            Recherche par code-barres
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="barcode">Code-barres (AMM)</Label>
            <Input
              id="barcode"
              value={manualBarcode}
              onChange={(e) => setManualBarcode(e.target.value)}
              placeholder="Entrez le code-barres du médicament"
              onKeyPress={(e) => e.key === 'Enter' && handleManualSearch()}
            />
          </div>
          <Button
            onClick={handleManualSearch}
            disabled={!manualBarcode.trim() || isSearching}
            className="w-full bg-teal hover:bg-teal-dark text-white"
          >
            {isSearching ? (
              <>
                <Search className="w-4 h-4 mr-2 animate-spin" />
                Recherche...
              </>
            ) : (
              <>
                <Search className="w-4 h-4 mr-2" />
                Rechercher
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Camera Scanner */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-navy">
            <Camera className="w-5 h-5" />
            Scanner avec la caméra
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {!isScanning ? (
            <Button
              variant="outline"
              className="w-full py-8 flex flex-col gap-2 border-teal text-teal hover:bg-teal/10"
              onClick={startCamera}
            >
              <Camera size={36} />
              <span>Démarrer la caméra</span>
            </Button>
          ) : (
            <div className="space-y-4">
              <video
                ref={videoRef}
                autoPlay
                playsInline
                className="w-full rounded-lg border-2 border-teal"
                style={{ maxHeight: '300px' }}
              />
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={stopCamera}
                  className="flex-1"
                >
                  Arrêter
                </Button>
                <Input
                  placeholder="Code-barres détecté apparaîtra ici"
                  className="flex-1"
                  readOnly
                />
              </div>
              <p className="text-sm text-gray-600 text-center">
                Pointez la caméra vers le code-barres du médicament
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Found Medicine Display */}
      {foundMedicine && (
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-700">
              <CheckCircle className="w-5 h-5" />
              Médicament trouvé
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <strong>Nom:</strong> {foundMedicine.nom}
            </div>
            {foundMedicine.dosage && (
              <div>
                <strong>Dosage:</strong> {foundMedicine.dosage}
              </div>
            )}
            {foundMedicine.forme && (
              <div>
                <strong>Forme:</strong> {foundMedicine.forme}
              </div>
            )}
            {foundMedicine.laboratoire && (
              <div>
                <strong>Laboratoire:</strong> {foundMedicine.laboratoire}
              </div>
            )}
            <div className="text-sm text-green-600 mt-2">
              Les informations ont été automatiquement remplies. Vous pouvez maintenant ajouter la quantité et la date d'expiration.
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default MedicineScanner;
