
import React, { createContext, useState, useContext, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Session, User } from '@supabase/supabase-js';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';

type AuthContextType = {
  session: Session | null;
  user: User | null;
  profile: any | null;
  householdId: string | null;
  householdName: string | null;
  isOnboardingCompleted: boolean;
  signUp: (email: string, password: string, name: string) => Promise<{ error: any }>;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  loading: boolean;
  refreshProfile: () => Promise<void>;
  createHousehold: (name: string) => Promise<{ error: any, data: any }>;
  updateHouseholdName: (name: string) => Promise<{ error: any }>;
  supabase: typeof supabase;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<any | null>(null);
  const [householdId, setHouseholdId] = useState<string | null>(null);
  const [householdName, setHouseholdName] = useState<string | null>(null);
  const [isOnboardingCompleted, setIsOnboardingCompleted] = useState(false);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email);
        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          // Defer Supabase calls with setTimeout to avoid potential deadlocks
          setTimeout(() => {
            fetchProfile(session.user.id);
          }, 0);
        } else {
          setProfile(null);
          setHouseholdId(null);
          setHouseholdName(null);
          setIsOnboardingCompleted(false);
        }
      }
    );

    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      console.log('Initial session check:', session?.user?.email);
      setSession(session);
      setUser(session?.user ?? null);
      
      if (session?.user) {
        fetchProfile(session.user.id);
      }
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const fetchProfile = async (userId: string) => {
    try {
      // Fetch profile data
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      // Always check localStorage fallback for onboarding completion
      const localOnboardingCompleted = localStorage.getItem(`onboarding_completed_${userId}`) === 'true';

      if (profileError) {
        console.error('Error fetching profile:', profileError);
        console.log('Using localStorage fallback for onboarding:', localOnboardingCompleted);
        setIsOnboardingCompleted(localOnboardingCompleted);
      } else {
        setProfile(profileData);
        // Check database first, then localStorage fallback
        const dbOnboardingCompleted = profileData?.onboarding_completed || false;
        let finalOnboardingStatus = dbOnboardingCompleted || localOnboardingCompleted;

        // Note: We'll check for existing household after fetching user data

        console.log('Onboarding status (before household check):', {
          db: dbOnboardingCompleted,
          local: localOnboardingCompleted,
          final: finalOnboardingStatus,
          userId
        });
        setIsOnboardingCompleted(finalOnboardingStatus);
      }

      // Fetch household information - handle case where user doesn't exist yet
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('household_id')
        .eq('id', userId)
        .single();

      if (userError) {
        console.error('Error fetching household ID:', userError);

        // If user doesn't exist in users table (PGRST116), create them
        if (userError.code === 'PGRST116') {
          console.log('User not found in users table, creating entry...');
          try {
            const { error: createError } = await supabase
              .from('users')
              .insert({
                id: userId,
                created_at: new Date().toISOString()
              });

            if (createError) {
              console.error('Error creating user entry:', createError);
            } else {
              console.log('User entry created successfully');
              // User created but has no household - will trigger onboarding
              setHouseholdId(null);
              setHouseholdName(null);
            }
          } catch (err) {
            console.error('Unexpected error creating user:', err);
          }
        }
      } else if (userData?.household_id) {
        setHouseholdId(userData.household_id);
        
        // Fetch household name
        const { data: householdData, error: householdError } = await supabase
          .from('households')
          .select('name')
          .eq('id', userData.household_id)
          .single();
        
        if (householdError) {
          console.error('Error fetching household name:', householdError);
        } else {
          setHouseholdName(householdData?.name || null);
        }

        // For existing users with households: mark onboarding as complete
        // This ensures existing users bypass onboarding
        if (!isOnboardingCompleted) {
          console.log('Existing user with household detected, marking onboarding as complete');
          const updatedStatus = true;
          setIsOnboardingCompleted(updatedStatus);
          // Store in localStorage for future sessions
          localStorage.setItem(`onboarding_completed_${userId}`, 'true');
          console.log('Updated onboarding status for existing user:', updatedStatus);
        }
      } else {
        // No household - this is an edge case that needs handling
        setHouseholdId(null);
        setHouseholdName(null);
        
        // Redirect to onboarding if logged in but no household
        if (userId) {
          toast.warning("Configuration du foyer requise.", {
            duration: 5000
          });
          setTimeout(() => navigate('/onboarding'), 1000);
        }
      }
    } catch (err) {
      console.error('Unexpected error fetching user data:', err);
    }
  };
  
  const refreshProfile = async () => {
    if (user) {
      await fetchProfile(user.id);
    }
  };

  const createHousehold = async (name: string) => {
    if (!user) return { error: new Error('Utilisateur non connecté'), data: null };
    
    try {
      // Create a new household
      const { data: householdData, error: householdError } = await supabase
        .from('households')
        .insert({ 
          name, 
          created_by: user.id 
        })
        .select()
        .single();
      
      if (householdError) {
        toast.error("Erreur lors de la création du foyer");
        return { error: householdError, data: null };
      }
      
      // Associate user with the new household
      const { error: userUpdateError } = await supabase
        .from('users')
        .update({ household_id: householdData.id })
        .eq('id', user.id);
      
      if (userUpdateError) {
        toast.error("Erreur lors de l'association du foyer à l'utilisateur");
        return { error: userUpdateError, data: null };
      }
      
      // Update local state
      setHouseholdId(householdData.id);
      setHouseholdName(householdData.name);
      
      toast.success("Foyer créé avec succès");
      return { error: null, data: householdData };
    } catch (err) {
      console.error('Error creating household:', err);
      toast.error("Une erreur est survenue");
      return { error: err, data: null };
    }
  };

  const updateHouseholdName = async (name: string) => {
    if (!householdId) return { error: new Error('Aucun foyer associé') };
    
    try {
      const { error } = await supabase
        .from('households')
        .update({ name })
        .eq('id', householdId);
      
      if (error) {
        toast.error("Erreur lors de la mise à jour du nom du foyer");
        return { error };
      }
      
      // Update local state
      setHouseholdName(name);
      
      toast.success("Nom du foyer mis à jour");
      return { error: null };
    } catch (err) {
      console.error('Error updating household name:', err);
      toast.error("Une erreur est survenue");
      return { error: err };
    }
  };

  const signUp = async (email: string, password: string, name: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name
          }
        }
      });
      
      if (error) {
        toast.error(error.message);
        return { error };
      }
      
      // After successful signup, user will go through onboarding
      if (data.user) {
        toast.success('Inscription réussie. Vous allez être guidé pour configurer votre espace.');
      } else {
        toast.success('Inscription réussie. Vérifiez votre email pour confirmer votre compte.');
      }
      
      return { error: null };
    } catch (err) {
      console.error('Unexpected error during signup:', err);
      const error = err instanceof Error ? err : new Error('Une erreur est survenue lors de l\'inscription');
      toast.error(error.message);
      return { error };
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) {
        toast.error(error.message);
        return { error };
      }
      
      toast.success('Connexion réussie');
      return { error: null };
    } catch (err) {
      console.error('Unexpected error during sign in:', err);
      const error = err instanceof Error ? err : new Error('Une erreur est survenue lors de la connexion');
      toast.error(error.message);
      return { error };
    }
  };

  const signOut = async () => {
    try {
      await supabase.auth.signOut();
      toast.info('Déconnexion réussie');
      navigate('/auth');
    } catch (err) {
      console.error('Unexpected error during sign out:', err);
      toast.error('Erreur lors de la déconnexion');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="animate-spin h-8 w-8 text-primary" />
      </div>
    );
  }

  return (
    <AuthContext.Provider
      value={{
        session,
        user,
        profile,
        householdId,
        householdName,
        isOnboardingCompleted,
        signUp,
        signIn,
        signOut,
        loading,
        refreshProfile,
        createHousehold,
        updateHouseholdName,
        supabase
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
