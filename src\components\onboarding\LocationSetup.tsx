import React, { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useLocations } from "@/hooks/useLocations";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { MapPin, Plus, CheckCircle, Home, Heart, Luggage } from "lucide-react";
import { toast } from "sonner";

interface LocationSetupProps {
  onComplete: () => void;
}

const LocationSetup = ({ onComplete }: LocationSetupProps) => {
  const { householdId } = useAuth();
  const { locations, addLocation, isLoading } = useLocations();
  const [locationName, setLocationName] = useState('');
  const [locationDescription, setLocationDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Debug logging
  console.log('LocationSetup render:', {
    householdId,
    locationsCount: locations.length,
    isLoading,
    locationName,
    isSubmitting
  });

  // Effect to monitor household ID changes
  useEffect(() => {
    console.log('LocationSetup: householdId changed:', householdId);
  }, [householdId]);

  // Effect to monitor locations changes
  useEffect(() => {
    console.log('LocationSetup: locations changed:', locations);
  }, [locations]);

  // Suggested locations for quick setup
  const suggestedLocations = [
    { name: 'Salle de bain', description: 'Armoire à pharmacie principale', icon: Home },
    { name: 'Cuisine', description: 'Placard ou réfrigérateur', icon: Heart },
    { name: 'Chambre', description: 'Table de chevet', icon: Luggage },
  ];

  useEffect(() => {
    // If user already has locations, they can proceed
    // But don't auto-complete, let them click the finish button
  }, [locations.length, onComplete]);

  const handleAddLocation = async () => {
    console.log('Adding location:', { locationName, householdId });

    if (!locationName.trim()) {
      toast.error('Veuillez saisir un nom pour l\'emplacement');
      return;
    }

    if (!householdId) {
      console.error('No household ID available');
      toast.error('Aucun foyer configuré. Veuillez d\'abord créer un foyer.');
      return;
    }

    setIsSubmitting(true);
    try {
      console.log('Calling addLocation with:', {
        name: locationName.trim(),
        description: locationDescription.trim() || undefined,
        householdId
      });

      const newLocation = await addLocation(
        locationName.trim(),
        locationDescription.trim() || undefined
      );

      if (newLocation) {
        console.log('Location added successfully:', newLocation);
        toast.success('Emplacement ajouté avec succès!');
        setLocationName('');
        setLocationDescription('');

        // Location added successfully, user can now complete onboarding
        // Don't auto-complete, let them click the finish button
      } else {
        console.error('addLocation returned null');
        toast.error('Erreur lors de l\'ajout de l\'emplacement');
      }
    } catch (err) {
      console.error('Error adding location:', err);
      toast.error('Erreur lors de l\'ajout de l\'emplacement');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSuggestedLocation = async (suggested: typeof suggestedLocations[0]) => {
    console.log('Adding suggested location:', { suggested, householdId });

    if (!householdId) {
      console.error('No household ID available for suggested location');
      toast.error('Aucun foyer configuré');
      return;
    }

    setIsSubmitting(true);
    try {
      const newLocation = await addLocation(suggested.name, suggested.description);

      if (newLocation) {
        console.log('Suggested location added successfully:', newLocation);
        toast.success(`Emplacement "${suggested.name}" ajouté!`);

        // Location added successfully, user can now complete onboarding
        // Don't auto-complete, let them click the finish button
      } else {
        console.error('addLocation returned null for suggested location');
        toast.error('Erreur lors de l\'ajout de l\'emplacement');
      }
    } catch (err) {
      console.error('Error adding suggested location:', err);
      toast.error('Erreur lors de l\'ajout de l\'emplacement');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (locations.length > 0) {
    return (
      <div className="text-center space-y-6">
        <div className="w-16 h-16 bg-teal/10 rounded-full flex items-center justify-center mx-auto">
          <CheckCircle className="w-8 h-8 text-teal" />
        </div>
        <div>
          <h2 className="text-xl font-bold text-navy mb-2">
            Emplacements configurés!
          </h2>
          <p className="text-gray-600">
            Vous avez {locations.length} emplacement{locations.length > 1 ? 's' : ''} configuré{locations.length > 1 ? 's' : ''}.
            Vous pouvez en ajouter d'autres plus tard.
          </p>
        </div>

        <div className="bg-teal/10 p-4 rounded-lg">
          <h3 className="font-medium text-teal-dark mb-2">Emplacements existants :</h3>
          <ul className="text-sm text-gray-600 space-y-1">
            {locations.map((location) => (
              <li key={location.id} className="flex items-center space-x-2">
                <MapPin className="w-4 h-4 text-teal" />
                <span>{location.name}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    );
  }

  // Show loading state if household ID is not available yet
  if (!householdId) {
    return (
      <div className="text-center space-y-6">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto">
          <MapPin className="w-8 h-8 text-gray-400" />
        </div>
        <div>
          <h2 className="text-xl font-bold text-navy mb-2">
            Préparation...
          </h2>
          <p className="text-gray-600">
            Veuillez patienter pendant que nous préparons votre espace.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-navy/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <MapPin className="w-8 h-8 text-navy" />
        </div>
        <h2 className="text-xl font-bold text-navy mb-2">
          Ajoutez votre premier emplacement
        </h2>
        <p className="text-gray-600">
          Les emplacements vous aident à organiser et retrouver facilement vos médicaments.
        </p>
      </div>

      {/* Suggested Locations */}
      <div>
        <h3 className="font-medium text-navy mb-3">Emplacements suggérés</h3>
        <div className="grid gap-3">
          {suggestedLocations.map((suggested, index) => (
            <Card 
              key={index}
              className="cursor-pointer hover:border-teal/50 transition-colors"
              onClick={() => handleSuggestedLocation(suggested)}
            >
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-teal/10 rounded-full flex items-center justify-center">
                    <suggested.icon className="w-5 h-5 text-teal" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-navy">{suggested.name}</h4>
                    <p className="text-sm text-gray-600">{suggested.description}</p>
                  </div>
                  <Plus className="w-5 h-5 text-gray-400" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Custom Location Form */}
      <div>
        <h3 className="font-medium text-navy mb-3">Ou créez un emplacement personnalisé</h3>
        <Card className="border-teal/20">
          <CardContent className="p-6 space-y-4">
            <div>
              <Label htmlFor="location-name">Nom de l'emplacement *</Label>
              <Input
                id="location-name"
                value={locationName}
                onChange={(e) => setLocationName(e.target.value)}
                placeholder="Ex: Armoire à pharmacie, Réfrigérateur..."
                disabled={isSubmitting}
              />
            </div>
            
            <div>
              <Label htmlFor="location-description">Description (optionnel)</Label>
              <Textarea
                id="location-description"
                value={locationDescription}
                onChange={(e) => setLocationDescription(e.target.value)}
                placeholder="Détails supplémentaires sur cet emplacement..."
                rows={3}
                disabled={isSubmitting}
              />
            </div>
            
            <Button
              onClick={handleAddLocation}
              disabled={!locationName.trim() || isSubmitting}
              className="w-full bg-teal hover:bg-teal-dark text-white"
            >
              {isSubmitting ? 'Ajout...' : 'Ajouter cet emplacement'}
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="bg-blue-50 p-4 rounded-lg">
        <p className="text-sm text-blue-800">
          <strong>Conseil :</strong> Commencez par ajouter l'emplacement principal où vous stockez 
          la plupart de vos médicaments. Vous pourrez ajouter d'autres emplacements plus tard.
        </p>
      </div>
    </div>
  );
};

export default LocationSetup;
