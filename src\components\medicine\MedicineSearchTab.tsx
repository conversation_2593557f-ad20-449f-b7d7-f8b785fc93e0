
import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Search, Plus, Database } from "lucide-react";
import { useMedicineSearch, MedicineVariant } from "@/hooks/useMedicineSearch";
import MedicineSearchResults from "@/components/medicine/MedicineSearchResults";
import AddMedicineForm from "@/components/medicine/AddMedicineForm";
import MedicineVariantSelector from "@/components/medicine/MedicineVariantSelector";
import { Location, FamilyMember } from "@/types";

interface MedicineSearchTabProps {
  locations: Location[];
  familyMembers: FamilyMember[];
  formData: any;
  onFormChange: (field: string, value: any) => void;
  onSubmit: (e: React.FormEvent) => void;
}

const MedicineSearchTab = ({ 
  locations, 
  familyMembers,
  formData, 
  onFormChange,
  onSubmit
}: MedicineSearchTabProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [showResults, setShowResults] = useState(false);
  const [selectedMedicine, setSelectedMedicine] = useState<any | null>(null);
  const [selectedMedicineName, setSelectedMedicineName] = useState<string>("");
  const [showVariantSelector, setShowVariantSelector] = useState(false);
  const [isCustom, setIsCustom] = useState(false);

  const {
    medicines,
    searchMedicines,
    isLoading,
    medicineVariants,
    getMedicineVariants,
    isLoadingVariants
  } = useMedicineSearch();

  // Check if medicine was found via barcode scanning
  const isFromBarcode = formData.medicine_id && formData.barcode && !isCustom && !selectedMedicine;

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    
    if (query.length >= 2) {
      searchMedicines(query);
      setShowResults(true);
    } else {
      setShowResults(false);
    }
    
    // If user clears the search, reset the form
    if (!query) {
      setSelectedMedicine(null);
      setSelectedMedicineName("");
      setShowVariantSelector(false);
      setIsCustom(false);
      onFormChange('custom_name', '');
      onFormChange('is_custom', false);
      onFormChange('barcode', null);
      onFormChange('medicine_id', null);
    }
  };

  const handleMedicineSelect = (medicine: any) => {
    setSelectedMedicineName(medicine.name);
    setShowResults(false);
    setShowVariantSelector(true);
    setSearchQuery(medicine.name);

    // Get variants for this medicine name
    getMedicineVariants(medicine.name);
  };

  const handleVariantSelect = (variant: MedicineVariant) => {
    setSelectedMedicine(variant);
    setShowVariantSelector(false);
    setIsCustom(false);

    // Set form data with the selected variant
    onFormChange('is_custom', false);
    onFormChange('custom_name', '');
    onFormChange('barcode', variant.amm || null);
    onFormChange('medicine_id', variant.id);
    onFormChange('presentation', variant.presentation || null);
    onFormChange('notes', `${variant.dci ? 'DCI: ' + variant.dci : ''}${variant.classe ? ', Classe: ' + variant.classe : ''}${variant.sous_classe ? ', Sous-classe: ' + variant.sous_classe : ''}${variant.presentation ? ', Présentation: ' + variant.presentation : ''}`);
  };

  const handleVariantCancel = () => {
    setShowVariantSelector(false);
    setSelectedMedicineName("");
    setSearchQuery("");
  };

  const handleManualEntry = () => {
    setIsCustom(true);
    setSelectedMedicine(null);
    setSelectedMedicineName("");
    setShowVariantSelector(false);
    onFormChange('is_custom', true);
    onFormChange('custom_name', searchQuery);
    onFormChange('barcode', null);
    onFormChange('medicine_id', null);
    setShowResults(false);
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Rechercher un médicament*</Label>
        <div className="relative">
          <div className="relative flex items-center">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              type="text"
              placeholder="Rechercher un médicament..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="pl-10"
            />
          </div>

          {showResults && searchQuery.trim().length >= 2 && (
            <div className="absolute z-50 w-full mt-1 bg-white border rounded-md shadow-lg">
              <MedicineSearchResults
                isSearching={isLoading}
                searchResults={medicines}
                onSelect={handleMedicineSelect}
              />
              {medicines.length === 0 && searchQuery.trim().length >= 2 && (
                <div className="p-2 text-center border-t">
                  <p className="text-sm text-muted-foreground mb-2">
                    Aucun médicament trouvé dans la base
                  </p>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={handleManualEntry}
                    className="w-full"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Créer une entrée manuelle
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {showVariantSelector && (
        <MedicineVariantSelector
          medicineName={selectedMedicineName}
          variants={medicineVariants}
          isLoading={isLoadingVariants}
          onVariantSelect={handleVariantSelect}
          onCancel={handleVariantCancel}
        />
      )}

      {/* Show form if medicine selected, custom entry, or found via barcode */}
      {(selectedMedicine || isCustom || isFromBarcode) && !showVariantSelector && (
        <>
          {isFromBarcode && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg mb-4">
              <div className="flex items-center gap-2 text-green-700 mb-2">
                <Database className="w-5 h-5" />
                <span className="font-medium">Médicament trouvé par code-barres</span>
              </div>
              <div className="text-sm text-green-600">
                <strong>{formData.custom_name}</strong> a été trouvé dans la base de données.
                Complétez les informations ci-dessous pour l'ajouter à votre armoire.
              </div>
            </div>
          )}
          <AddMedicineForm
            formData={formData}
            onFormChange={onFormChange}
            onSubmit={onSubmit}
            locations={locations}
            familyMembers={familyMembers}
            isCustom={isCustom}
            selectedMedicine={selectedMedicine}
          />
        </>
      )}

      {!selectedMedicine && !isCustom && !showVariantSelector && searchQuery.trim().length >= 2 && (
        <div className="flex flex-col items-center py-8 text-center text-muted-foreground space-y-4">
          <Database className="h-12 w-12 opacity-30" />
          <div>
            <p className="font-medium">Recherchez un médicament officiel ou créez une entrée manuelle</p>
            <p className="text-sm">Vous pouvez saisir le nom d'un médicament pour le rechercher dans la base de données</p>
          </div>
          <Button
            variant="outline"
            onClick={() => {
              setIsCustom(true);
              onFormChange('is_custom', true);
              onFormChange('custom_name', searchQuery);
              onFormChange('barcode', null);
              onFormChange('medicine_id', null);
            }}
          >
            <Plus className="mr-2 h-4 w-4" />
            Créer une entrée manuelle
          </Button>
        </div>
      )}
    </div>
  );
};

export default MedicineSearchTab;
