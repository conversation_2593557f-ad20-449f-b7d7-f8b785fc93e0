
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://wzzykbnebhyvdoagpwvk.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind6enlrYm5lYmh5dmRvYWdwd3ZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUwMTMzNjMsImV4cCI6MjA2MDU4OTM2M30.LYLDFRLZB4qtuzxTjRuZqnepRwrg3BNGLwdnwU4D0Es";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
