
import React, { useEffect } from "react";
import ModernHeader from "@/components/ModernHeader";
import BottomNavigation from "@/components/BottomNavigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useDashboardData } from "@/hooks/useDashboardData";
import { formatDate } from "@/utils/helpers";
import { Pill, MapPin, Users, AlertCircle, LayoutGrid, Tags } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { Loader2 } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import FloatingActionButton from "@/components/FloatingActionButton";
import featureFlags from "@/config/featureFlags";

const Dashboard = () => {
  const { dashboardData, expiringMedicines, isLoading, refresh } = useDashboardData();
  const navigate = useNavigate();
  const { user, profile } = useAuth();

  useEffect(() => {
    if (user) {
      refresh();
    }
  }, [user]);

  const handleMedicineClick = (id: string) => {
    navigate(`/medicine/${id}`);
  };

  return (
    <div className="medicinet-container animate-fade-in pb-20">
      <ModernHeader
        title="Tableau de bord"
        subtitle="Suivez l'état de votre armoire à pharmacie"
        userName={profile?.name}
        variant="navy"
      />

      {isLoading ? (
        <div className="flex justify-center py-12">
          <Loader2 className="animate-spin h-8 w-8 text-teal" />
        </div>
      ) : (
        <>

          {/* Stats Cards */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <Card className="border-teal/20 hover:border-teal/40 transition-colors">
              <CardContent className="pt-4">
                <div className="flex flex-col items-center text-center space-y-2">
                  <div className="bg-teal/10 p-3 rounded-full">
                    <Pill size={24} className="text-teal" />
                  </div>
                  <div>
                    <span className="block text-2xl font-bold text-navy">{dashboardData.totalMedicines}</span>
                    <span className="text-sm text-gray-600">Médicaments</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-amber-200 hover:border-amber-300 transition-colors">
              <CardContent className="pt-4">
                <div className="flex flex-col items-center text-center space-y-2">
                  <div className="bg-amber-100 p-3 rounded-full">
                    <AlertCircle size={24} className="text-amber-600" />
                  </div>
                  <div>
                    <span className="block text-2xl font-bold text-navy">{dashboardData.expiringSoon}</span>
                    <span className="text-sm text-gray-600">Expirent bientôt</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-navy/20 hover:border-navy/40 transition-colors cursor-pointer" onClick={() => navigate('/settings')}>
              <CardContent className="pt-4">
                <div className="flex flex-col items-center text-center space-y-2">
                  <div className="bg-navy/10 p-3 rounded-full">
                    <Tags size={24} className="text-navy" />
                  </div>
                  <div>
                    <span className="block text-2xl font-bold text-navy">
                      {dashboardData.tagCount} <span className="text-xl text-navy font-medium">({dashboardData.tagUsagePercentage}% utilisées)</span>
                    </span>
                    <span className="text-sm text-gray-600">Étiquettes</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-teal/20 hover:border-teal/40 transition-colors">
              <CardContent className="pt-4">
                <div className="flex flex-col items-center text-center space-y-2">
                  <div className="bg-teal/10 p-3 rounded-full">
                    <MapPin size={24} className="text-teal-dark" />
                  </div>
                  <div>
                    <span className="block text-2xl font-bold text-navy">
                      {dashboardData.locationCount} <span className="text-xl text-teal font-medium">({dashboardData.locationUsagePercentage}% utilisés)</span>
                    </span>
                    <span className="text-sm text-gray-600">Emplacements</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Expiring Medicines Section */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg text-navy">Médicaments expirant bientôt</CardTitle>
            </CardHeader>
            <CardContent>
              {expiringMedicines.length > 0 ? (
                <div className="space-y-4">
                  {expiringMedicines.map((med) => (
                    <div 
                      key={med.id} 
                      className="p-3 border rounded-lg flex justify-between items-start cursor-pointer hover:bg-muted/50 transition-colors"
                      onClick={() => handleMedicineClick(med.id)}
                    >
                      <div className="flex-1">
                        <p className="font-medium">{med.label || "Médicament sans nom"}</p>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <span>Exp: {formatDate(med.expirationDate || '')}</span>
                          <span className="inline-block h-1 w-1 bg-muted-foreground rounded-full"></span>
                          <span>Qté: {med.quantity}</span>
                        </div>
                        {(med.familyMemberName || med.location) && (
                          <div className="mt-1 flex items-center gap-2 text-xs text-muted-foreground">
                            {med.location && (
                              <div className="flex items-center">
                                <MapPin size={12} className="mr-1" />
                                <span>{med.location}</span>
                              </div>
                            )}
                            {med.familyMemberName && (
                              <div className="flex items-center">
                                <Users size={12} className="mr-1" />
                                <span>{med.familyMemberName}</span>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                      <AlertCircle size={18} className="text-amber-500 flex-shrink-0" />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  <p>Aucun médicament n'expire bientôt</p>
                </div>
              )}
              
              <Button
                className="w-full mt-4 bg-teal hover:bg-teal-dark text-white"
                onClick={() => navigate("/alerts")}
              >
                Voir toutes les alertes
              </Button>
            </CardContent>
          </Card>

          {/* Navigation buttons - conditionally render based on feature flags */}
          {featureFlags.showDemoFeatures && (
            <div className="grid grid-cols-2 gap-4 mb-24">
              <Button 
                variant="outline"
                className="py-6 flex flex-col items-center"
                onClick={() => navigate("/family")}
              >
                <Users size={24} className="mb-2" />
                <span>Famille</span>
              </Button>
              
              <Button 
                variant="outline"
                className="py-6 flex flex-col items-center"
                onClick={() => navigate("/locations")}
              >
                <MapPin size={24} className="mb-2" />
                <span>Emplacements</span>
              </Button>
            </div>
          )}
        </>
      )}

      <FloatingActionButton />
      <BottomNavigation />
    </div>
  );
};

export default Dashboard;
