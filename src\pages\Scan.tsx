
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import ModernHeader from "@/components/ModernHeader";
import BottomNavigation from "@/components/BottomNavigation";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Barcode, Camera, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const Scan = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [scanning, setScanning] = useState(false);
  
  const handleScanStart = () => {
    setScanning(true);
    
    // Simulate a scan after 2 seconds
    setTimeout(() => {
      setScanning(false);
      
      toast({
        title: "Fonction démo",
        description: "Le scan sera disponible dans une future version. Vous allez être redirigé vers le formulaire d'ajout.",
      });
      
      navigate("/add-medicine");
    }, 2000);
  };
  
  return (
    <div className="medicinet-container animate-fade-in">
      <ModernHeader
        title="Scanner"
        subtitle="Scannez le code-barres de votre médicament"
        showBackButton={true}
        variant="navy"
      />
      
      <div className="bg-gray-100 rounded-2xl aspect-[3/4] mb-6 relative flex flex-col items-center justify-center">
        {scanning ? (
          <div className="text-center">
            <div className="h-1 w-full max-w-xs bg-primary animate-pulse mb-4"></div>
            <p>Recherche de code-barres...</p>
          </div>
        ) : (
          <div className="text-center p-6">
            <Barcode size={64} className="mx-auto mb-4 text-muted-foreground" />
            <p className="text-lg mb-2">Scannez un code-barres</p>
            <p className="text-sm text-muted-foreground mb-6">
              Positionnez le code-barres dans le cadre pour l'analyser automatiquement
            </p>
            <Button onClick={handleScanStart}>
              Commencer le scan
            </Button>
          </div>
        )}
      </div>
      
      <div className="bg-muted p-4 rounded-lg flex items-start gap-3 mb-6">
        <AlertCircle size={20} className="text-muted-foreground shrink-0 mt-0.5" />
        <p className="text-sm text-muted-foreground">
          Les informations récupérées par scan seront automatiquement comparées 
          avec la base nationale des médicaments.
        </p>
      </div>
      
      <div>
        <p className="text-center mb-4 font-medium">Autres options d'ajout</p>
        
        <div className="flex gap-4">
          <Button 
            variant="outline" 
            className="flex-1 py-6 flex flex-col gap-2"
            onClick={() => navigate("/add-medicine")}
          >
            <Camera size={24} />
            <span>Prendre une photo</span>
          </Button>
          
          <Button 
            variant="outline" 
            className="flex-1 py-6 flex flex-col gap-2"
            onClick={() => navigate("/add-medicine")}
          >
            <Barcode size={24} />
            <span>Saisie manuelle</span>
          </Button>
        </div>
      </div>
      
      <BottomNavigation />
    </div>
  );
};

export default Scan;
