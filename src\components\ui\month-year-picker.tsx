import React from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";

interface MonthYearPickerProps {
  value: string; // Date string in YYYY-MM-DD format
  onChange: (value: string) => void; // Returns date string in YYYY-MM-DD format
  label?: string;
  required?: boolean;
  className?: string;
}

const MonthYearPicker = ({ value, onChange, label, required = false, className }: MonthYearPickerProps) => {
  // Parse the current value - handle the date string properly
  const currentDate = value ? new Date(value + 'T00:00:00') : new Date(); // Add time to avoid timezone issues
  const currentMonth = currentDate.getMonth() + 1; // getMonth() returns 0-11
  const currentYear = currentDate.getFullYear();

  // Generate years (1 year before current year to 10 years in the future)
  const currentYearNum = new Date().getFullYear();
  const years = Array.from({ length: 12 }, (_, i) => currentYearNum - 1 + i);

  // Month names in French
  const months = [
    { value: 1, label: "Janvier" },
    { value: 2, label: "Février" },
    { value: 3, label: "Mars" },
    { value: 4, label: "Avril" },
    { value: 5, label: "Mai" },
    { value: 6, label: "Juin" },
    { value: 7, label: "Juillet" },
    { value: 8, label: "Août" },
    { value: 9, label: "Septembre" },
    { value: 10, label: "Octobre" },
    { value: 11, label: "Novembre" },
    { value: 12, label: "Décembre" }
  ];

  const handleMonthChange = (month: string) => {
    const monthNum = parseInt(month);
    // Set to last day of the month for expiration
    const lastDay = new Date(currentYear, monthNum, 0).getDate();
    // Create date string directly to avoid timezone issues
    const dateString = `${currentYear}-${monthNum.toString().padStart(2, '0')}-${lastDay.toString().padStart(2, '0')}`;
    onChange(dateString);
  };

  const handleYearChange = (year: string) => {
    const yearNum = parseInt(year);
    // Set to last day of the current month for expiration
    const lastDay = new Date(yearNum, currentMonth, 0).getDate();
    // Create date string directly to avoid timezone issues
    const dateString = `${yearNum}-${currentMonth.toString().padStart(2, '0')}-${lastDay.toString().padStart(2, '0')}`;
    onChange(dateString);
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <Label>
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}
      <div className="grid grid-cols-2 gap-3">
        {/* Month Selector */}
        <div className="space-y-1">
          <Label className="text-sm text-gray-600">Mois</Label>
          <Select 
            value={currentMonth.toString()} 
            onValueChange={handleMonthChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="Mois" />
            </SelectTrigger>
            <SelectContent>
              {months.map((month) => (
                <SelectItem key={month.value} value={month.value.toString()}>
                  {month.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Year Selector */}
        <div className="space-y-1">
          <Label className="text-sm text-gray-600">Année</Label>
          <Select 
            value={currentYear.toString()} 
            onValueChange={handleYearChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="Année" />
            </SelectTrigger>
            <SelectContent>
              {years.map((year) => (
                <SelectItem key={year} value={year.toString()}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Display selected date in MM/YY format */}
      <div className="text-sm text-gray-600">
        Date sélectionnée: {currentMonth.toString().padStart(2, '0')}/{currentYear.toString().slice(-2)}
      </div>
    </div>
  );
};

export default MonthYearPicker;
