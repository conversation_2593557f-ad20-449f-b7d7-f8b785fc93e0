-- Household Invitations System
-- This script creates the necessary tables and functions for household sharing

-- Create household_invitations table
CREATE TABLE IF NOT EXISTS household_invitations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  household_id UUID NOT NULL REFERENCES households(id) ON DELETE CASCADE,
  invited_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  token TEXT NOT NULL UNIQUE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired', 'revoked')),
  permissions JSONB DEFAULT '{"can_add_medicines": true, "can_edit_medicines": true, "can_delete_medicines": false, "can_manage_family": false, "can_invite_others": false}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_household_invitations_household_id ON household_invitations(household_id);
CREATE INDEX IF NOT EXISTS idx_household_invitations_email ON household_invitations(email);
CREATE INDEX IF NOT EXISTS idx_household_invitations_token ON household_invitations(token);
CREATE INDEX IF NOT EXISTS idx_household_invitations_status ON household_invitations(status);

-- Create household_members table to track who belongs to which household
CREATE TABLE IF NOT EXISTS household_members (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  household_id UUID NOT NULL REFERENCES households(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
  permissions JSONB DEFAULT '{"can_add_medicines": true, "can_edit_medicines": true, "can_delete_medicines": false, "can_manage_family": false, "can_invite_others": false}'::jsonb,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(household_id, user_id)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_household_members_household_id ON household_members(household_id);
CREATE INDEX IF NOT EXISTS idx_household_members_user_id ON household_members(user_id);

-- Function to generate secure invitation tokens
CREATE OR REPLACE FUNCTION generate_invitation_token()
RETURNS TEXT AS $$
BEGIN
  RETURN encode(gen_random_bytes(32), 'base64');
END;
$$ LANGUAGE plpgsql;

-- Function to create a household invitation
CREATE OR REPLACE FUNCTION create_household_invitation(
  p_household_id UUID,
  p_invited_by UUID,
  p_email TEXT,
  p_permissions JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_token TEXT;
  v_invitation_id UUID;
  v_expires_at TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Generate unique token
  v_token := generate_invitation_token();
  
  -- Set expiration to 7 days from now
  v_expires_at := NOW() + INTERVAL '7 days';
  
  -- Insert invitation
  INSERT INTO household_invitations (
    household_id,
    invited_by,
    email,
    token,
    expires_at,
    permissions
  ) VALUES (
    p_household_id,
    p_invited_by,
    p_email,
    v_token,
    v_expires_at,
    COALESCE(p_permissions, '{"can_add_medicines": true, "can_edit_medicines": true, "can_delete_medicines": false, "can_manage_family": false, "can_invite_others": false}'::jsonb)
  ) RETURNING id INTO v_invitation_id;
  
  RETURN v_invitation_id;
END;
$$ LANGUAGE plpgsql;

-- Function to accept a household invitation
CREATE OR REPLACE FUNCTION accept_household_invitation(
  p_token TEXT,
  p_user_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  v_invitation RECORD;
BEGIN
  -- Get invitation details
  SELECT * INTO v_invitation
  FROM household_invitations
  WHERE token = p_token
    AND status = 'pending'
    AND expires_at > NOW();
  
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- Add user to household_members
  INSERT INTO household_members (
    household_id,
    user_id,
    role,
    permissions
  ) VALUES (
    v_invitation.household_id,
    p_user_id,
    'member',
    v_invitation.permissions
  ) ON CONFLICT (household_id, user_id) DO NOTHING;
  
  -- Update user's household_id
  UPDATE users 
  SET household_id = v_invitation.household_id
  WHERE id = p_user_id;
  
  -- Mark invitation as accepted
  UPDATE household_invitations
  SET status = 'accepted', updated_at = NOW()
  WHERE id = v_invitation.id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to revoke an invitation
CREATE OR REPLACE FUNCTION revoke_household_invitation(
  p_invitation_id UUID,
  p_user_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  v_household_id UUID;
BEGIN
  -- Get household_id and verify user has permission
  SELECT household_id INTO v_household_id
  FROM household_invitations
  WHERE id = p_invitation_id;
  
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- Check if user has permission to revoke (owner or admin)
  IF NOT EXISTS (
    SELECT 1 FROM household_members
    WHERE household_id = v_household_id
      AND user_id = p_user_id
      AND role IN ('owner', 'admin')
  ) THEN
    RETURN FALSE;
  END IF;
  
  -- Revoke invitation
  UPDATE household_invitations
  SET status = 'revoked', updated_at = NOW()
  WHERE id = p_invitation_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up expired invitations
CREATE OR REPLACE FUNCTION cleanup_expired_invitations()
RETURNS INTEGER AS $$
DECLARE
  v_count INTEGER;
BEGIN
  UPDATE household_invitations
  SET status = 'expired', updated_at = NOW()
  WHERE status = 'pending'
    AND expires_at <= NOW();
  
  GET DIAGNOSTICS v_count = ROW_COUNT;
  RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically add household owner as member
CREATE OR REPLACE FUNCTION add_household_owner_as_member()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO household_members (
    household_id,
    user_id,
    role,
    permissions
  ) VALUES (
    NEW.id,
    NEW.created_by,
    'owner',
    '{"can_add_medicines": true, "can_edit_medicines": true, "can_delete_medicines": true, "can_manage_family": true, "can_invite_others": true}'::jsonb
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
DROP TRIGGER IF EXISTS trigger_add_household_owner ON households;
CREATE TRIGGER trigger_add_household_owner
  AFTER INSERT ON households
  FOR EACH ROW
  EXECUTE FUNCTION add_household_owner_as_member();

-- Enable RLS
ALTER TABLE household_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE household_members ENABLE ROW LEVEL SECURITY;

-- RLS Policies for household_invitations
CREATE POLICY "Users can view invitations for their household" ON household_invitations
  FOR SELECT USING (
    household_id IN (
      SELECT household_id FROM household_members WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create invitations for their household" ON household_invitations
  FOR INSERT WITH CHECK (
    household_id IN (
      SELECT household_id FROM household_members 
      WHERE user_id = auth.uid() 
        AND (permissions->>'can_invite_others')::boolean = true
    )
  );

-- RLS Policies for household_members
CREATE POLICY "Users can view members of their household" ON household_members
  FOR SELECT USING (
    household_id IN (
      SELECT household_id FROM household_members WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Only owners and admins can manage members" ON household_members
  FOR ALL USING (
    household_id IN (
      SELECT household_id FROM household_members 
      WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
    )
  );
