#Mon Jul 07 14:36:18 GMT+01:00 2025
com.medytrack.medytrack_mobile.app-main-38\:/drawable-v21/launch_background.xml=C\:\\Users\\Asma Tebourbi\\Documents\\GitHub\\medy-track (Lovable)\\medytrack_mobile\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-v21_launch_background.xml.flat
com.medytrack.medytrack_mobile.app-main-38\:/mipmap-hdpi/ic_launcher.png=C\:\\Users\\Asma Tebourbi\\Documents\\GitHub\\medy-track (Lovable)\\medytrack_mobile\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.png.flat
com.medytrack.medytrack_mobile.app-main-38\:/mipmap-mdpi/ic_launcher.png=C\:\\Users\\Asma Tebourbi\\Documents\\GitHub\\medy-track (Lovable)\\medytrack_mobile\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.png.flat
com.medytrack.medytrack_mobile.app-main-38\:/mipmap-xhdpi/ic_launcher.png=C\:\\Users\\Asma Tebourbi\\Documents\\GitHub\\medy-track (Lovable)\\medytrack_mobile\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.png.flat
com.medytrack.medytrack_mobile.app-main-38\:/mipmap-xxhdpi/ic_launcher.png=C\:\\Users\\Asma Tebourbi\\Documents\\GitHub\\medy-track (Lovable)\\medytrack_mobile\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.png.flat
com.medytrack.medytrack_mobile.app-main-38\:/mipmap-xxxhdpi/ic_launcher.png=C\:\\Users\\Asma Tebourbi\\Documents\\GitHub\\medy-track (Lovable)\\medytrack_mobile\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.png.flat
