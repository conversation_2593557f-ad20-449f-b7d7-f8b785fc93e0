import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import MedicineCard from '@/components/medicines/MedicineCard';
import { Medicine } from '@/types';

// Mock the helpers
vi.mock('@/utils/helpers', () => ({
  formatDateShort: (date: string) => date.substring(0, 7), // MM/YY format
  isExpired: (date: string) => new Date(date) < new Date('2025-01-01'),
  isNearExpiry: (date: string) => {
    const expiry = new Date(date);
    const today = new Date('2025-01-01');
    const thirtyDays = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);
    return expiry >= today && expiry <= thirtyDays;
  }
}));

const createMockMedicine = (overrides: Partial<Medicine> = {}): Medicine => ({
  id: 'test-id',
  name: 'Paracétamol',
  dosage: '500mg',
  form: 'Comprimé pelliculé',
  presentation: 'Boîte de 30',
  quantity: 10,
  expiryDate: '2025-12-31',
  category: 'pain',
  location: 'loc-1',
  locationName: 'Armoire à pharmacie',
  createdAt: '2025-01-01',
  updatedAt: '2025-01-01',
  is_custom: false,
  ...overrides
});

describe('Improved MedicineCard UI', () => {
  const mockOnClick = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should display medicine name in bold 18px navy color', () => {
    const medicine = createMockMedicine();
    
    render(
      <MedicineCard 
        medicine={medicine}
        onClick={mockOnClick}
      />
    );

    const nameElement = screen.getByText('Paracétamol');
    expect(nameElement).toBeInTheDocument();
    expect(nameElement).toHaveClass('font-bold', 'text-navy', 'text-lg', 'font-ubuntu');
  });

  it('should display dosage and form in bold 14px black color', () => {
    const medicine = createMockMedicine();
    
    render(
      <MedicineCard 
        medicine={medicine}
        onClick={mockOnClick}
      />
    );

    const dosageFormElement = screen.getByText('500mg - Comprimé pelliculé');
    expect(dosageFormElement).toBeInTheDocument();
    expect(dosageFormElement).toHaveClass('font-bold', 'text-black', 'text-sm', 'font-ubuntu');
  });

  it('should display status badge with increased font size and bold weight', () => {
    const expiredMedicine = createMockMedicine({
      expiryDate: '2024-01-01' // Expired
    });
    
    render(
      <MedicineCard 
        medicine={expiredMedicine}
        onClick={mockOnClick}
      />
    );

    const statusBadge = screen.getByText('Expiré');
    expect(statusBadge).toBeInTheDocument();
    expect(statusBadge).toHaveClass('text-sm', 'font-bold');
  });

  it('should display expiration date in MM/YY format', () => {
    const medicine = createMockMedicine({
      expiryDate: '2025-12-31'
    });
    
    render(
      <MedicineCard 
        medicine={medicine}
        onClick={mockOnClick}
      />
    );

    // Should show formatted date
    expect(screen.getByText('2025-12')).toBeInTheDocument();
  });

  it('should display quantity in 14px font with medium weight', () => {
    const medicine = createMockMedicine({ quantity: 5 });
    
    render(
      <MedicineCard 
        medicine={medicine}
        onClick={mockOnClick}
      />
    );

    const quantityElement = screen.getByText('5 unités');
    expect(quantityElement).toBeInTheDocument();
    expect(quantityElement).toHaveClass('text-sm', 'font-medium', 'font-ubuntu');
  });

  it('should display location in 14px black text', () => {
    const medicine = createMockMedicine();
    
    render(
      <MedicineCard 
        medicine={medicine}
        onClick={mockOnClick}
      />
    );

    const locationElement = screen.getByText('Armoire à pharmacie');
    expect(locationElement).toBeInTheDocument();
    expect(locationElement).toHaveClass('text-sm', 'text-black', 'font-ubuntu');
  });

  it('should use only left border for status indication', () => {
    const expiredMedicine = createMockMedicine({
      expiryDate: '2024-01-01'
    });
    
    const { container } = render(
      <MedicineCard 
        medicine={expiredMedicine}
        onClick={mockOnClick}
      />
    );

    const card = container.querySelector('[class*="border-l-4"]');
    expect(card).toBeInTheDocument();
    expect(card).toHaveClass('border-l-4', 'border-red-500');
    // Should not have right border
    expect(card).not.toHaveClass('border-r-4');
  });

  it('should handle multiple expiry dates indicator', () => {
    const medicine = createMockMedicine();
    const multipleExpiryDates = ['2025-06-01', '2025-12-01', '2026-01-01'];
    
    render(
      <MedicineCard 
        medicine={medicine}
        onClick={mockOnClick}
        showMultipleExpiryIndicator={true}
        multipleExpiryDates={multipleExpiryDates}
      />
    );

    expect(screen.getByText('Dates multiples')).toBeInTheDocument();
  });

  it('should expand to show all expiry dates when multiple expiry indicator is clicked', () => {
    const medicine = createMockMedicine();
    const multipleExpiryDates = ['2025-06-01', '2025-12-01', '2026-01-01'];
    
    render(
      <MedicineCard 
        medicine={medicine}
        onClick={mockOnClick}
        showMultipleExpiryIndicator={true}
        multipleExpiryDates={multipleExpiryDates}
      />
    );

    const expandButton = screen.getByText('Dates multiples');
    fireEvent.click(expandButton);

    expect(screen.getByText('Toutes les dates d\'expiration:')).toBeInTheDocument();
    expect(screen.getByText('• 2025-06')).toBeInTheDocument();
    expect(screen.getByText('• 2025-12')).toBeInTheDocument();
    expect(screen.getByText('• 2026-01')).toBeInTheDocument();
  });

  it('should handle custom medicines correctly', () => {
    const customMedicine = createMockMedicine({
      is_custom: true,
      custom_name: 'Mon médicament personnel',
      name: 'ignored'
    });
    
    render(
      <MedicineCard 
        medicine={customMedicine}
        onClick={mockOnClick}
      />
    );

    expect(screen.getByText('Mon médicament personnel')).toBeInTheDocument();
    expect(screen.getByText('Manuel')).toBeInTheDocument();
  });

  it('should maintain clickable functionality', () => {
    const medicine = createMockMedicine();
    
    render(
      <MedicineCard 
        medicine={medicine}
        onClick={mockOnClick}
      />
    );

    const card = screen.getByText('Paracétamol').closest('[class*="cursor-pointer"]');
    fireEvent.click(card!);

    expect(mockOnClick).toHaveBeenCalledWith(medicine);
  });

  it('should show proper status colors for different expiry states', () => {
    // Test expired medicine
    const expiredMedicine = createMockMedicine({
      expiryDate: '2024-01-01'
    });
    
    const { rerender } = render(
      <MedicineCard 
        medicine={expiredMedicine}
        onClick={mockOnClick}
      />
    );

    expect(screen.getByText('Expiré')).toHaveClass('bg-red-100', 'text-red-800');

    // Test expiring soon medicine
    const expiringSoonMedicine = createMockMedicine({
      expiryDate: '2025-01-15' // Within 30 days
    });
    
    rerender(
      <MedicineCard 
        medicine={expiringSoonMedicine}
        onClick={mockOnClick}
      />
    );

    expect(screen.getByText('Expire bientôt')).toHaveClass('bg-amber-100', 'text-amber-800');

    // Test valid medicine
    const validMedicine = createMockMedicine({
      expiryDate: '2026-01-01'
    });
    
    rerender(
      <MedicineCard 
        medicine={validMedicine}
        onClick={mockOnClick}
      />
    );

    expect(screen.getByText('Valide')).toHaveClass('bg-green-100', 'text-green-800');
  });
});
