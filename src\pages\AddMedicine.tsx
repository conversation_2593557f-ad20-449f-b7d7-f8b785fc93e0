
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { toast } from "sonner";
import { Medicine } from "@/types";
import { useUserMedicines } from "@/hooks/useUserMedicines";
import MedicineScanner from "@/components/medicine/MedicineScanner";
import { useLocations } from "@/hooks/useLocations";
import { useFamilyMembers } from "@/hooks/useFamilyMembers";
import BottomNavigation from "@/components/BottomNavigation";
import MedicineAddTabs from "@/components/medicine/MedicineAddTabs";
import MedicineSearchTab from "@/components/medicine/MedicineSearchTab";
import ModernHeader from "@/components/ModernHeader";
import { useAuth } from "@/contexts/AuthContext";

const AddMedicine = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'scan' | 'manual'>('manual');

  const { addMedicine } = useUserMedicines();
  const { locations } = useLocations();
  const { familyMembers } = useFamilyMembers();
  const { user, profile } = useAuth();
  
  const [formData, setFormData] = useState({
    custom_name: '',
    quantity: 1,
    category: 'other',
    location: '',
    familyMember: '', // Will be updated to current user when family members load
    notes: '',
    lowStockThreshold: 2, // Default threshold
    expiryDate: (() => {
      // Default to next year, June (month 6) for medicine expiry
      const nextYear = new Date().getFullYear() + 1;
      const lastDayOfJune = new Date(nextYear, 6, 0).getDate(); // June is month 5 (0-indexed), so 6 gives us last day of June
      return `${nextYear}-06-${lastDayOfJune.toString().padStart(2, '0')}`;
    })(),
    is_custom: false,
    barcode: null as string | null, // For storing AMM
    medicine_id: null as string | null, // For storing medicine ID
    presentation: null as string | null // For storing presentation information
  });

  // Set current user as default family member when family members are loaded
  useEffect(() => {
    if (familyMembers.length > 0 && user) {
      // Find the current user in family members by matching name or email
      const currentUserMember = familyMembers.find(member => {
        const matches = [
          profile?.name && member.name === profile.name,
          user.email && member.name === user.email.split('@')[0],
          member.relation === 'Moi',
          member.relation === 'Utilisateur principal',
          // Also try matching by email directly
          user.email && member.name.toLowerCase().includes(user.email.split('@')[0].toLowerCase())
        ].some(Boolean);

        return matches;
      });

      if (currentUserMember) {
        setFormData(prev => ({
          ...prev,
          familyMember: currentUserMember.id
        }));
      } else if (familyMembers.length > 0) {
        // If no exact match found, use the first family member as fallback
        setFormData(prev => ({
          ...prev,
          familyMember: familyMembers[0].id
        }));
      }
    }
  }, [familyMembers, user, profile]);

  // Set default location to first available location
  useEffect(() => {
    if (locations.length > 0 && !formData.location) {
      setFormData(prev => ({
        ...prev,
        location: locations[0].id
      }));
    }
  }, [locations, formData.location]);

  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.is_custom && !formData.custom_name) {
      toast.error("Le nom du médicament est requis");
      return;
    }
    
    try {
      const medicineToAdd = {
        name: formData.is_custom ? formData.custom_name : '',
        barcode: formData.is_custom ? null : formData.barcode,
        medicine_id: formData.is_custom ? null : formData.medicine_id,
        quantity: formData.quantity,
        expiryDate: formData.expiryDate,
        category: formData.category as any,
        location: formData.location || '',
        notes: formData.notes,
        lowStockThreshold: formData.lowStockThreshold,
        custom_name: formData.is_custom ? formData.custom_name : null,
        is_custom: formData.is_custom,
        presentation: formData.is_custom ? null : formData.presentation,
        familyMember: formData.familyMember ?
          { id: formData.familyMember, name: '' } : null
      };
      
      await addMedicine(medicineToAdd);
      // Toast is now handled in useUserMedicines.addMedicine
      navigate("/");
    } catch (error) {
      console.error('Error adding medicine:', error);
      // Error toast is already handled in useUserMedicines.addMedicine
    }
  };

  const handleTabChange = (tab: 'scan' | 'manual') => {
    setActiveTab(tab);
  };

  const handleMedicineFound = (medicine: any) => {
    // Populate form with found medicine data
    setFormData(prev => ({
      ...prev,
      custom_name: medicine.nom,
      is_custom: false,
      medicine_id: medicine.id,
      barcode: medicine.amm,
      // Keep existing form data for quantity, location, etc.
    }));

    // Switch to manual tab to show the populated form
    setActiveTab('manual');
    toast.success(`Médicament "${medicine.nom}" ajouté au formulaire`);
  };

  return (
    <div className="medicinet-container pb-24 animate-fade-in">
      <ModernHeader
        title="Ajouter un médicament"
        subtitle="Scannez ou ajoutez manuellement un médicament"
        showBackButton={true}
        variant="navy"
      />

      <div className="bg-white rounded-lg p-4 mb-6 border border-teal/20">
        <MedicineAddTabs activeTab={activeTab} onTabChange={handleTabChange} />

        {activeTab === 'scan' ? (
          <MedicineScanner onMedicineFound={handleMedicineFound} />
        ) : (
          <MedicineSearchTab
            locations={locations}
            familyMembers={familyMembers}
            formData={formData}
            onFormChange={handleFormChange}
            onSubmit={handleSubmit}
          />
        )}
      </div>

      <BottomNavigation />
    </div>
  );
};

export default AddMedicine;
