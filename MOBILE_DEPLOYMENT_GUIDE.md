# MedyTrack Mobile - Complete Deployment Guide

## 🎯 **Deployment Overview**

This guide provides step-by-step instructions for deploying the MedyTrack mobile application to both Google Play Store and Apple App Store, including all necessary configurations, testing procedures, and production readiness checks.

---

## 📋 **Pre-Deployment Checklist**

### **✅ Code Quality & Testing**
- [ ] All unit tests passing (80%+ coverage)
- [ ] Integration tests completed successfully
- [ ] Widget tests validated
- [ ] Performance benchmarks met
- [ ] Memory leak testing completed
- [ ] Security audit passed
- [ ] Accessibility compliance verified

### **✅ Configuration Verification**
- [ ] Production Supabase credentials configured
- [ ] Firebase project setup completed
- [ ] App signing certificates generated
- [ ] Environment-specific configurations validated
- [ ] API endpoints verified for production
- [ ] Push notification setup tested

### **✅ Store Assets Prepared**
- [ ] App icons (all required sizes)
- [ ] Screenshots (all device types)
- [ ] App descriptions (French, English, Arabic)
- [ ] Privacy policy updated
- [ ] Terms of service finalized
- [ ] App Store metadata completed

---

## 🤖 **Android Deployment (Google Play Store)**

### **Step 1: Prepare Release Build**

#### **1.1 Configure App Signing**
```bash
# Generate upload keystore
keytool -genkey -v -keystore upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload

# Create key.properties file
echo "storePassword=YOUR_STORE_PASSWORD" > android/key.properties
echo "keyPassword=YOUR_KEY_PASSWORD" >> android/key.properties
echo "keyAlias=upload" >> android/key.properties
echo "storeFile=../upload-keystore.jks" >> android/key.properties
```

#### **1.2 Build Production AAB**
```bash
# Clean previous builds
flutter clean
flutter pub get

# Build production AAB
flutter build appbundle --flavor production --release --verbose

# Verify build
ls -la build/app/outputs/bundle/productionRelease/
```

#### **1.3 Test Release Build**
```bash
# Install on device for testing
flutter install --flavor production --release

# Run automated tests on release build
flutter test integration_test/ --flavor production
```

### **Step 2: Google Play Console Setup**

#### **2.1 Create App Listing**
1. **Go to Google Play Console**
   - Navigate to [Google Play Console](https://play.google.com/console)
   - Create new application
   - Select "App" and choose default language (French)

2. **App Details**
   ```
   App Name: MedyTrack
   Short Description: Gestion professionnelle de médicaments
   Full Description: [See store_assets/google_play_description.md]
   ```

3. **Store Listing Assets**
   - **App Icon**: 512x512 PNG
   - **Feature Graphic**: 1024x500 PNG
   - **Screenshots**: 
     - Phone: 16:9 or 9:16 ratio (minimum 320px)
     - Tablet: 16:10 or 10:16 ratio (minimum 1080px)

#### **2.2 Content Rating**
```
Category: Medical
Target Audience: Adults (18+)
Content Descriptors: Medical/Health Information
Interactive Elements: Users Interact Online, Shares Location
```

#### **2.3 App Content**
- **Privacy Policy**: https://medytrack.com/privacy
- **Target Audience**: Adults
- **Content Rating**: Everyone
- **App Category**: Medical
- **Tags**: medicine, health, pharmacy, medication

### **Step 3: Release Management**

#### **3.1 Internal Testing**
```bash
# Upload AAB to Internal Testing track
# Add internal testers (development team)
# Test for 24-48 hours
```

#### **3.2 Closed Testing (Alpha)**
```bash
# Promote to Closed Testing
# Add 20-50 beta testers
# Collect feedback for 1 week
# Fix critical issues if any
```

#### **3.3 Open Testing (Beta)**
```bash
# Promote to Open Testing
# Allow public beta testing
# Monitor crash reports and feedback
# Performance optimization based on real usage
```

#### **3.4 Production Release**
```bash
# Final review and approval
# Staged rollout: 1% → 5% → 20% → 50% → 100%
# Monitor key metrics during rollout
```

---

## 🍎 **iOS Deployment (Apple App Store)**

### **Step 1: Xcode Configuration**

#### **1.1 Open iOS Project**
```bash
# Open iOS project in Xcode
open ios/Runner.xcworkspace
```

#### **1.2 Configure Signing & Capabilities**
1. **Select Runner target**
2. **Signing & Capabilities tab**
   - Team: Select your Apple Developer Team
   - Bundle Identifier: com.medytrack.medytrack-mobile
   - Provisioning Profile: Automatic

3. **Add Capabilities**
   - Push Notifications
   - Background Modes (Background fetch, Remote notifications)
   - App Groups (if needed for sharing)
   - Associated Domains (for deep linking)

#### **1.3 Build Configuration**
```bash
# Set build configuration to Release
# Verify deployment target: iOS 14.0
# Check architecture: arm64
```

### **Step 2: App Store Connect Setup**

#### **2.1 Create App Record**
1. **Go to App Store Connect**
   - Navigate to [App Store Connect](https://appstoreconnect.apple.com)
   - Create new app
   - Platform: iOS
   - Bundle ID: com.medytrack.medytrack-mobile

2. **App Information**
   ```
   Name: MedyTrack
   Subtitle: Gestion de médicaments
   Category: Medical
   Content Rights: No, it does not contain, show, or access third-party content
   ```

#### **2.2 Pricing and Availability**
```
Price: Free
Availability: All territories
App Store Distribution: Available on the App Store
```

#### **2.3 App Privacy**
- **Data Collection**: Health & Fitness, Contact Info, User Content
- **Data Usage**: App Functionality, Analytics, Product Personalization
- **Data Sharing**: No data shared with third parties
- **Data Retention**: User can request deletion

### **Step 3: Build and Upload**

#### **3.1 Archive Build**
```bash
# Clean and build
flutter clean
flutter pub get

# Build iOS release
flutter build ios --flavor production --release

# Open Xcode for archiving
open ios/Runner.xcworkspace
```

#### **3.2 Create Archive**
1. **In Xcode**:
   - Select "Any iOS Device" as destination
   - Product → Archive
   - Wait for archive to complete

2. **Organizer Window**:
   - Select the archive
   - Click "Distribute App"
   - Choose "App Store Connect"
   - Upload to App Store Connect

#### **3.3 TestFlight Testing**
```bash
# Internal Testing (Apple Developer Team)
# External Testing (Beta testers)
# Collect feedback and crash reports
# Fix issues and upload new builds if needed
```

### **Step 4: App Review Submission**

#### **4.1 App Store Review Information**
```
Contact Information:
- First Name: [Your Name]
- Last Name: [Your Last Name]
- Phone Number: [Your Phone]
- Email: [Your Email]

Demo Account (if required):
- Username: <EMAIL>
- Password: DemoPassword123!

Notes:
This app helps users manage their medications with features like:
- Medicine tracking and organization
- Expiry date monitoring
- Barcode scanning for easy entry
- Secure data storage with biometric authentication
```

#### **4.2 Version Information**
```
Version: 1.4.2
Copyright: 2024 MedyTrack
Trade Representative Contact: [Your Contact Info]
App Review Information: [Demo account details]
Version Release: Automatically release this version
```

---

## 🔧 **CI/CD Pipeline Setup**

### **GitHub Actions Configuration**

#### **Android CI/CD**
```yaml
# .github/workflows/android.yml
name: Android CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.24.0'
      - run: flutter pub get
      - run: flutter test
      - run: flutter analyze

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      - run: flutter pub get
      - run: flutter build appbundle --flavor production --release
      - name: Upload to Play Store
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
          packageName: com.medytrack.medytrack_mobile
          releaseFiles: build/app/outputs/bundle/productionRelease/app-production-release.aab
          track: internal
```

#### **iOS CI/CD**
```yaml
# .github/workflows/ios.yml
name: iOS CI/CD

on:
  push:
    branches: [ main ]

jobs:
  build:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.24.0'
      - run: flutter pub get
      - run: flutter build ios --flavor production --release --no-codesign
      - name: Build and upload to TestFlight
        uses: apple-actions/import-codesign-certs@v1
        with:
          p12-file-base64: ${{ secrets.IOS_CERTIFICATE }}
          p12-password: ${{ secrets.IOS_CERTIFICATE_PASSWORD }}
```

---

## 📊 **Post-Deployment Monitoring**

### **Key Metrics to Monitor**
- **Crash Rate**: <0.1% target
- **ANR Rate**: <0.1% target (Android)
- **App Launch Time**: <3 seconds
- **User Retention**: >80% after 30 days
- **App Store Rating**: >4.5 stars

### **Monitoring Tools**
- **Firebase Crashlytics**: Crash reporting
- **Firebase Performance**: Performance monitoring
- **Firebase Analytics**: User behavior
- **App Store Connect**: iOS metrics
- **Google Play Console**: Android metrics

### **Alert Configuration**
```yaml
Crash Rate > 0.5%: Immediate alert
ANR Rate > 0.5%: Immediate alert
App Rating < 4.0: Daily alert
Performance degradation > 20%: Hourly alert
```

---

## 🚨 **Rollback Procedures**

### **Emergency Rollback**
1. **Google Play Store**:
   - Go to Play Console → Release Management
   - Halt current rollout
   - Promote previous stable version

2. **Apple App Store**:
   - Contact Apple Developer Support
   - Request expedited review for rollback version
   - Submit critical bug fix update

### **Hotfix Deployment**
```bash
# Critical bug fix process
1. Create hotfix branch from main
2. Implement minimal fix
3. Test thoroughly
4. Build and deploy to internal testing
5. Fast-track through testing phases
6. Deploy to production with staged rollout
```

---

## ✅ **Final Deployment Checklist**

### **Pre-Launch (T-1 Week)**
- [ ] All testing completed and passed
- [ ] Store assets uploaded and approved
- [ ] App descriptions translated and reviewed
- [ ] Privacy policy and terms updated
- [ ] Support documentation prepared
- [ ] Monitoring and alerting configured

### **Launch Day (T-0)**
- [ ] Final build uploaded and approved
- [ ] Release notes published
- [ ] Social media announcements prepared
- [ ] Support team briefed
- [ ] Monitoring dashboards active
- [ ] Rollback procedures tested

### **Post-Launch (T+1 Week)**
- [ ] User feedback monitored and addressed
- [ ] Crash reports analyzed and fixed
- [ ] Performance metrics reviewed
- [ ] App store reviews responded to
- [ ] Next iteration planning initiated

---

**🎉 Congratulations! MedyTrack Mobile is now live and helping users manage their medications professionally! 🏥📱**
