import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, XCircle, Users, Home, Loader2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

const JoinHousehold = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [invitation, setInvitation] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [isJoining, setIsJoining] = useState(false);

  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      setError('Token d\'invitation manquant');
      setIsLoading(false);
      return;
    }

    if (!user) {
      // Redirect to auth with return URL
      navigate(`/auth?returnTo=${encodeURIComponent(window.location.pathname + window.location.search)}`);
      return;
    }

    fetchInvitation();
  }, [token, user]);

  const fetchInvitation = async () => {
    if (!token) return;

    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('household_invitations')
        .select(`
          *,
          households (
            name,
            created_by
          ),
          invited_by_user:users!household_invitations_invited_by_fkey (
            email
          )
        `)
        .eq('token', token)
        .eq('status', 'pending')
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          setError('Invitation non trouvée ou expirée');
        } else {
          throw error;
        }
        return;
      }

      // Check if invitation is expired
      if (new Date(data.expires_at) < new Date()) {
        setError('Cette invitation a expiré');
        return;
      }

      setInvitation(data);
    } catch (err) {
      console.error('Error fetching invitation:', err);
      setError('Erreur lors du chargement de l\'invitation');
    } finally {
      setIsLoading(false);
    }
  };

  const acceptInvitation = async () => {
    if (!user || !token) return;

    setIsJoining(true);
    try {
      const { data, error } = await supabase.rpc('accept_household_invitation', {
        p_token: token,
        p_user_id: user.id
      });

      if (error) throw error;

      if (data) {
        toast.success('Vous avez rejoint le foyer avec succès!');
        navigate('/');
      } else {
        toast.error('Impossible de rejoindre le foyer');
      }
    } catch (err) {
      console.error('Error accepting invitation:', err);
      toast.error('Erreur lors de l\'acceptation de l\'invitation');
    } finally {
      setIsJoining(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center space-y-4">
              <Loader2 className="w-8 h-8 animate-spin text-teal" />
              <p className="text-center text-gray-600">
                Chargement de l'invitation...
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <XCircle className="w-6 h-6" />
              Invitation invalide
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600">{error}</p>
            <Button 
              onClick={() => navigate('/')}
              className="w-full bg-navy hover:bg-navy-dark text-white"
            >
              Retour à l'accueil
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!invitation) {
    return null;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-navy">
            <Users className="w-6 h-6" />
            Invitation au foyer
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center space-y-2">
            <div className="w-16 h-16 bg-teal/10 rounded-full flex items-center justify-center mx-auto">
              <Home className="w-8 h-8 text-teal" />
            </div>
            <h3 className="text-lg font-semibold text-navy">
              {invitation.households?.name || 'Foyer'}
            </h3>
            <p className="text-gray-600">
              Vous êtes invité(e) à rejoindre ce foyer par{' '}
              <span className="font-medium">
                {invitation.invited_by_user?.email}
              </span>
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4 space-y-2">
            <h4 className="font-medium text-navy">Permissions accordées:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              {invitation.permissions?.can_add_medicines && (
                <li>• Ajouter des médicaments</li>
              )}
              {invitation.permissions?.can_edit_medicines && (
                <li>• Modifier des médicaments</li>
              )}
              {invitation.permissions?.can_delete_medicines && (
                <li>• Supprimer des médicaments</li>
              )}
              {invitation.permissions?.can_manage_family && (
                <li>• Gérer les membres de la famille</li>
              )}
              {invitation.permissions?.can_invite_others && (
                <li>• Inviter d'autres membres</li>
              )}
            </ul>
          </div>

          <div className="text-xs text-gray-500 text-center">
            Cette invitation expire le{' '}
            {new Date(invitation.expires_at).toLocaleDateString('fr-FR', {
              day: 'numeric',
              month: 'long',
              year: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </div>

          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => navigate('/')}
              className="flex-1"
            >
              Refuser
            </Button>
            <Button
              onClick={acceptInvitation}
              disabled={isJoining}
              className="flex-1 bg-teal hover:bg-teal-dark text-white"
            >
              {isJoining ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Rejoindre...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Rejoindre
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default JoinHousehold;
