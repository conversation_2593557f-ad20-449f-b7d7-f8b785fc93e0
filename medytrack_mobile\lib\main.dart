import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:go_router/go_router.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:cached_network_image/cached_network_image.dart';

// Simple service for DI testing
@injectable
class TestService {
  String getMessage() => 'Dependency Injection Working!';
}

// Simple DI setup
final getIt = GetIt.instance;

@InjectableInit()
void configureDependencies() {
  getIt.registerSingleton<TestService>(TestService());
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    // Initialize DI
    configureDependencies();
    
    // Initialize Hive
    await Hive.initFlutter();
    
    // Initialize Supabase (with dummy values for testing)
    await Supabase.initialize(
      url: 'https://dummy.supabase.co',
      anonKey: 'dummy-anon-key-for-testing-only',
    );
    
    runApp(const MedyTrackApp());
  } catch (e) {
    runApp(MaterialApp(
      home: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              const Text('Initialization Error'),
              Text('$e'),
            ],
          ),
        ),
      ),
    ));
  }
}

// Router configuration
final GoRouter _router = GoRouter(
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => const MyHomePage(title: 'MedyTrack - UI & Media Test'),
    ),
    GoRoute(
      path: '/media',
      builder: (context, state) => const MediaTestPage(),
    ),
  ],
);

class MedyTrackApp extends StatelessWidget {
  const MedyTrackApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => TestBloc(),
      child: MaterialApp.router(
        title: 'MedyTrack - Group 4 Test',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: const Color(0xFF2D4A8E)),
          useMaterial3: true,
        ),
        routerConfig: _router,
      ),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  String _storageTest = 'Not tested';
  String _hiveTest = 'Not tested';
  String _supabaseTest = 'Not tested';
  String _diTest = 'Not tested';
  String _routerTest = '✅ Working (you\'re here!)';
  String _svgTest = 'Testing...';
  String _imageTest = 'Testing...';

  @override
  void initState() {
    super.initState();
    _testDependencies();
  }

  Future<void> _testDependencies() async {
    // Test DI
    try {
      final service = getIt<TestService>();
      final message = service.getMessage();
      setState(() {
        _diTest = message.isNotEmpty ? '✅ Working' : '❌ Failed';
      });
    } catch (e) {
      setState(() {
        _diTest = '❌ Error: $e';
      });
    }

    // Test SVG
    try {
      setState(() {
        _svgTest = '✅ Working (see icon below)';
      });
    } catch (e) {
      setState(() {
        _svgTest = '❌ Error: $e';
      });
    }

    // Test Cached Network Image
    try {
      setState(() {
        _imageTest = '✅ Working (see image below)';
      });
    } catch (e) {
      setState(() {
        _imageTest = '❌ Error: $e';
      });
    }

    // Test Secure Storage
    try {
      await _secureStorage.write(key: 'test', value: 'success');
      final value = await _secureStorage.read(key: 'test');
      setState(() {
        _storageTest = value == 'success' ? '✅ Working' : '❌ Failed';
      });
    } catch (e) {
      setState(() {
        _storageTest = '❌ Error: $e';
      });
    }

    // Test Hive
    try {
      final box = await Hive.openBox('testBox');
      await box.put('test', 'success');
      final value = box.get('test');
      setState(() {
        _hiveTest = value == 'success' ? '✅ Working' : '❌ Failed';
      });
      await box.close();
    } catch (e) {
      setState(() {
        _hiveTest = '❌ Error: $e';
      });
    }

    // Test Supabase
    try {
      final client = Supabase.instance.client;
      setState(() {
        _supabaseTest = '✅ Initialized';
      });
    } catch (e) {
      setState(() {
        _supabaseTest = '❌ Error: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF2D4A8E),
        foregroundColor: Colors.white,
        title: Text(widget.title),
      ),
      body: BlocBuilder<TestBloc, TestState>(
        builder: (context, state) {
          return Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  const Icon(
                    Icons.image,
                    size: 100,
                    color: Color(0xFF0DCDB7),
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'Group 4: UI & Media Test',
                    style: TextStyle(
                      fontSize: 24, 
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2D4A8E),
                    ),
                  ),
                  const SizedBox(height: 30),
                  Card(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Dependency Status:',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          _buildStatusRow('flutter_bloc', '✅ Working (Groups 1-3)'),
                          _buildStatusRow('equatable', '✅ Working (Groups 1-3)'),
                          _buildStatusRow('supabase_flutter', _supabaseTest),
                          _buildStatusRow('hive_flutter', _hiveTest),
                          _buildStatusRow('flutter_secure_storage', _storageTest),
                          _buildStatusRow('go_router', _routerTest),
                          _buildStatusRow('get_it', _diTest),
                          _buildStatusRow('injectable', _diTest),
                          _buildStatusRow('flutter_svg', _svgTest),
                          _buildStatusRow('cached_network_image', _imageTest),
                          const SizedBox(height: 16),
                          Text(
                            'Counter: ${state.counter}',
                            style: const TextStyle(fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  // SVG Test
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        const Text(
                          'SVG Test:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        // Simple SVG data for testing
                        SvgPicture.string(
                          '''<svg width="50" height="50" viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="25" cy="25" r="20" fill="#0DCDB7"/>
                            <text x="25" y="30" text-anchor="middle" fill="white" font-size="16">✓</text>
                          </svg>''',
                          width: 50,
                          height: 50,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () => context.go('/media'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0DCDB7),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Test Media Page →'),
                  ),
                  const SizedBox(height: 20),
                  const Card(
                    margin: EdgeInsets.symmetric(horizontal: 16),
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: Column(
                        children: [
                          Text(
                            '🎉 Group 4 Dependencies Test!',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                          SizedBox(height: 10),
                          Text(
                            'UI and media dependencies are being tested.',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontSize: 14),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.read<TestBloc>().add(IncrementEvent()),
        backgroundColor: const Color(0xFF0DCDB7),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildStatusRow(String dependency, String status) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              dependency,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              status,
              style: TextStyle(
                color: status.startsWith('✅') ? Colors.green : Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class MediaTestPage extends StatelessWidget {
  const MediaTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF2D4A8E),
        foregroundColor: Colors.white,
        title: const Text('Media Test Page'),
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.check_circle,
                size: 100,
                color: Colors.green,
              ),
              const SizedBox(height: 20),
              const Text(
                '✅ Media Components Working!',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
              const SizedBox(height: 30),
              // Cached Network Image Test
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    const Text(
                      'Cached Network Image Test:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    CachedNetworkImage(
                      imageUrl: 'https://via.placeholder.com/150x150/0DCDB7/FFFFFF?text=MedyTrack',
                      width: 150,
                      height: 150,
                      placeholder: (context, url) => const CircularProgressIndicator(),
                      errorWidget: (context, url, error) => Container(
                        width: 150,
                        height: 150,
                        color: Colors.grey[300],
                        child: const Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.error, color: Colors.red),
                            Text('Image Error'),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () => context.go('/'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0DCDB7),
                  foregroundColor: Colors.white,
                ),
                child: const Text('← Back to Home'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Test BLoC (same as previous groups)
class TestBloc extends Bloc<TestEvent, TestState> {
  TestBloc() : super(const TestState(counter: 0)) {
    on<IncrementEvent>((event, emit) {
      emit(TestState(counter: state.counter + 1));
    });
  }
}

abstract class TestEvent extends Equatable {
  const TestEvent();
  
  @override
  List<Object> get props => [];
}

class IncrementEvent extends TestEvent {}

class TestState extends Equatable {
  final int counter;
  
  const TestState({required this.counter});
  
  @override
  List<Object> get props => [counter];
}
