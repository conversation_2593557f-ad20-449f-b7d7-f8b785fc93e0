-- Fix function type mismatches for MedyTrack v1.4.2

-- 1. Drop and recreate get_household_tags function with correct types
DROP FUNCTION IF EXISTS public.get_household_tags(UUID);

CREATE OR REPLACE FUNCTION public.get_household_tags(p_household_id UUID)
RETURNS TABLE (
  id UUID,
  name VARCHAR(50),  -- Match the actual column type
  color VARCHAR(7),  -- Match the actual column type
  is_system_tag BOOLEAN,
  medicine_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.id,
    t.name,
    t.color,
    t.is_system_tag,
    COUNT(mt.id) as medicine_count
  FROM public.tags t
  LEFT JOIN public.medicine_tags mt ON t.id = mt.tag_id
  WHERE t.household_id = p_household_id
  GROUP BY t.id, t.name, t.color, t.is_system_tag
  ORDER BY t.is_system_tag DESC, t.name ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. <PERSON> execute permissions
GRANT EXECUTE ON FUNCTION public.get_household_tags(UUID) TO authenticated;

-- 3. Add comment
COMMENT ON FUNCTION public.get_household_tags(UUID) IS 'Get all tags for a household with medicine counts - fixed types';
