import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide AuthState;

import '../../bloc/auth/auth_bloc.dart';

/// Widget that listens to Supabase auth state changes and updates AuthBloc accordingly
/// This mirrors the web app's onAuthStateChange functionality
class AuthStateListener extends StatefulWidget {
  final Widget child;

  const AuthStateListener({super.key, required this.child});

  @override
  State<AuthStateListener> createState() => _AuthStateListenerState();
}

class _AuthStateListenerState extends State<AuthStateListener> {
  late final StreamSubscription _authSubscription;

  @override
  void initState() {
    super.initState();
    _setupAuthStateListener();
  }

  void _setupAuthStateListener() {
    // Listen to Supabase auth state changes (mirrors web app's onAuthStateChange)
    _authSubscription = Supabase.instance.client.auth.onAuthStateChange.listen((
      data,
    ) {
      final event = data.event;
      final session = data.session;

      debugPrint(
        '🔐 Auth state changed: $event, user: ${session?.user?.email}',
      );

      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Mirror web app's auth state handling
      if (session?.user != null) {
        // User signed in - trigger auth check to fetch complete profile
        context.read<AuthBloc>().add(AuthCheckRequested());
      } else {
        // User signed out - emit unauthenticated state
        if (event == AuthChangeEvent.signedOut) {
          context.read<AuthBloc>().add(AuthSignOutRequested());
        }
      }
    });
  }

  @override
  void dispose() {
    _authSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
