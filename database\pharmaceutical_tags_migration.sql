-- Pharmaceutical Tags Migration for MedyTrack
-- Replaces customizable tagging with standardized pharmaceutical tags
-- Optimized for Tunisia and French-speaking regions

-- 1. First, ensure the tags and medicine_tags tables exist
CREATE TABLE IF NOT EXISTS public.tags (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(50) NOT NULL,
  color VARCHAR(7) NOT NULL DEFAULT '#0DCDB7',
  household_id UUID NOT NULL,
  is_system_tag BOOLEAN DEFAULT TRUE, -- All pharmaceutical tags are system tags
  category VARCHAR(50) NOT NULL DEFAULT 'therapeutic', -- 'therapeutic' or 'usage'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(name, household_id)
);

CREATE TABLE IF NOT EXISTS public.medicine_tags (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_medicine_id UUID NOT NULL,
  tag_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_medicine_id, tag_id)
);

-- 2. Add foreign key constraints if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'tags_household_id_fkey'
  ) THEN
    ALTER TABLE public.tags 
    ADD CONSTRAINT tags_household_id_fkey 
    FOREIGN KEY (household_id) REFERENCES households(id) ON DELETE CASCADE;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'medicine_tags_user_medicine_id_fkey'
  ) THEN
    ALTER TABLE public.medicine_tags 
    ADD CONSTRAINT medicine_tags_user_medicine_id_fkey 
    FOREIGN KEY (user_medicine_id) REFERENCES user_medicines(id) ON DELETE CASCADE;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'medicine_tags_tag_id_fkey'
  ) THEN
    ALTER TABLE public.medicine_tags 
    ADD CONSTRAINT medicine_tags_tag_id_fkey 
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE;
  END IF;
END $$;

-- 3. Add category column if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'tags' AND column_name = 'category'
  ) THEN
    ALTER TABLE public.tags ADD COLUMN category VARCHAR(50) NOT NULL DEFAULT 'therapeutic';
  END IF;
END $$;

-- 4. Backup existing medicine-tag relationships before clearing
CREATE TEMP TABLE temp_medicine_categories AS
SELECT
  um.id as user_medicine_id,
  um.category as old_category,
  array_agg(t.name) as existing_tag_names
FROM user_medicines um
LEFT JOIN medicine_tags mt ON um.id = mt.user_medicine_id
LEFT JOIN tags t ON mt.tag_id = t.id
GROUP BY um.id, um.category;

-- Clear existing tags to replace with pharmaceutical tags
DELETE FROM public.medicine_tags;
DELETE FROM public.tags;

-- 5. Insert Therapeutic Classes (💊 Classes Thérapeutiques)
INSERT INTO public.tags (name, color, household_id, is_system_tag, category)
SELECT 
  tag_name,
  tag_color,
  h.id as household_id,
  true as is_system_tag,
  'therapeutic' as category
FROM households h
CROSS JOIN (
  VALUES 
    ('antibiotique', '#E53E3E'),      -- Red for antibiotics
    ('antalgique', '#3182CE'),        -- Blue for pain relief
    ('anti-inflammatoire', '#D69E2E'), -- Orange for anti-inflammatory
    ('antipyrétique', '#38A169'),     -- Green for fever reduction
    ('antiallergique', '#805AD5'),    -- Purple for allergies
    ('antispasmodique', '#DD6B20'),   -- Orange-red for antispasmodic
    ('corticoïde', '#C53030'),        -- Dark red for corticoids
    ('antifongique', '#2B6CB0'),      -- Dark blue for antifungal
    ('antivirale', '#2C7A7B'),        -- Teal for antiviral
    ('antihypertenseur', '#1A365D'),  -- Navy for hypertension
    ('antidiabétique', '#553C9A'),    -- Dark purple for diabetes
    ('psychotrope', '#744210')        -- Brown for psychotropic
) AS therapeutic_tags(tag_name, tag_color)
ON CONFLICT (name, household_id) DO NOTHING;

-- 6. Insert Usage/Domain Tags (🩺 Domaines d'Usage)
INSERT INTO public.tags (name, color, household_id, is_system_tag, category)
SELECT 
  tag_name,
  tag_color,
  h.id as household_id,
  true as is_system_tag,
  'usage' as category
FROM households h
CROSS JOIN (
  VALUES 
    ('parapharmacie', '#0DCDB7'),     -- Teal for parapharmacy
    ('premiers_soins', '#E53E3E'),    -- Red for first aid
    ('complément_alimentaire', '#38A169'), -- Green for supplements
    ('soins_peau', '#ED8936'),        -- Orange for skin care
    ('soins_yeux', '#3182CE'),        -- Blue for eye care
    ('soins_oreilles', '#805AD5'),    -- Purple for ear care
    ('soins_bouche', '#D69E2E'),      -- Yellow for oral care
    ('digestif', '#48BB78'),          -- Light green for digestive
    ('respiratoire', '#4299E1'),      -- Light blue for respiratory
    ('pédiatrique', '#F56565'),       -- Light red for pediatric
    ('gynécologie', '#ED64A6'),       -- Pink for gynecology
    ('dermatologie', '#F6AD55')       -- Light orange for dermatology
) AS usage_tags(tag_name, tag_color)
ON CONFLICT (name, household_id) DO NOTHING;

-- 7. Enable RLS
ALTER TABLE public.tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.medicine_tags ENABLE ROW LEVEL SECURITY;

-- 8. Create RLS policies
DROP POLICY IF EXISTS "tags_household_policy" ON public.tags;
CREATE POLICY "tags_household_policy" ON public.tags
  FOR ALL USING (
    household_id = (SELECT household_id FROM users WHERE id = auth.uid())
  );

DROP POLICY IF EXISTS "medicine_tags_household_policy" ON public.medicine_tags;
CREATE POLICY "medicine_tags_household_policy" ON public.medicine_tags
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_medicines um 
      WHERE um.id = medicine_tags.user_medicine_id 
      AND um.household_id = (SELECT household_id FROM users WHERE id = auth.uid())
    )
  );

-- 9. Grant permissions
GRANT SELECT, INSERT, DELETE ON public.tags TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.medicine_tags TO authenticated;

-- 10. Update get_household_tags function for pharmaceutical tags
DROP FUNCTION IF EXISTS public.get_household_tags(UUID);

CREATE OR REPLACE FUNCTION public.get_household_tags(p_household_id UUID)
RETURNS TABLE (
  id UUID,
  name VARCHAR(50),
  color VARCHAR(7),
  is_system_tag BOOLEAN,
  category VARCHAR(50),
  medicine_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.id,
    t.name,
    t.color,
    t.is_system_tag,
    t.category,
    COUNT(mt.id) as medicine_count
  FROM public.tags t
  LEFT JOIN public.medicine_tags mt ON t.id = mt.tag_id
  WHERE t.household_id = p_household_id
  GROUP BY t.id, t.name, t.color, t.is_system_tag, t.category
  ORDER BY t.category ASC, t.name ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 11. Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_household_tags(UUID) TO authenticated;

-- 12. Add comments for documentation
COMMENT ON TABLE public.tags IS 'Standardized pharmaceutical tags for therapeutic classes and usage domains';
COMMENT ON COLUMN public.tags.category IS 'Tag category: therapeutic (classes thérapeutiques) or usage (domaines d''usage)';
COMMENT ON FUNCTION public.get_household_tags(UUID) IS 'Get all pharmaceutical tags for a household with medicine counts and categories';

-- 13. Migrate existing medicine categorizations to pharmaceutical tags
-- Map old categories to appropriate pharmaceutical tags
INSERT INTO public.medicine_tags (user_medicine_id, tag_id)
SELECT DISTINCT
  tmc.user_medicine_id,
  t.id as tag_id
FROM temp_medicine_categories tmc
JOIN user_medicines um ON tmc.user_medicine_id = um.id
JOIN tags t ON t.household_id = um.household_id
WHERE
  -- Map old categories to new pharmaceutical tags
  (tmc.old_category = 'pain' AND t.name = 'antalgique') OR
  (tmc.old_category = 'cold' AND t.name = 'respiratoire') OR
  (tmc.old_category = 'allergy' AND t.name = 'antiallergique') OR
  (tmc.old_category = 'digestion' AND t.name = 'digestif') OR
  (tmc.old_category = 'first-aid' AND t.name = 'premiers_soins') OR
  (tmc.old_category = 'prescription' AND t.name = 'antalgique') OR -- Default mapping
  (tmc.old_category = 'other' AND t.name = 'parapharmacie') -- Default for uncategorized
ON CONFLICT (user_medicine_id, tag_id) DO NOTHING;

-- 14. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_tags_household_category ON public.tags(household_id, category);
CREATE INDEX IF NOT EXISTS idx_tags_category ON public.tags(category);

-- 15. Clean up temporary table
DROP TABLE IF EXISTS temp_medicine_categories;

-- Verification queries (uncomment to run)
-- SELECT 'Pharmaceutical tags migration completed!' as status;
-- SELECT category, COUNT(*) as tag_count FROM tags GROUP BY category ORDER BY category;
-- SELECT COUNT(*) as migrated_medicine_tags FROM medicine_tags;
