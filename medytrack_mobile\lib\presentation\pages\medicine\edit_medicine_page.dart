import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';

class EditMedicinePage extends StatelessWidget {
  final String medicineId;
  
  const EditMedicinePage({
    super.key,
    required this.medicineId,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Modifier le médicament'),
        backgroundColor: AppColors.navy,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.edit_outlined,
              size: 64,
              color: AppColors.grey400,
            ),
            const SizedBox(height: 16),
            Text(
              'Modifier médicament ID: $medicineId',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: AppColors.grey600,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Formulaire de modification à implémenter',
              style: TextStyle(
                color: AppColors.grey500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
