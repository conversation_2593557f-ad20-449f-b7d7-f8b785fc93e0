
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useUserSettings } from "@/hooks/useUserSettings";
import { useTags } from "@/hooks/useTags";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "sonner";
import { ArrowLeft, Clock, Tags, Plus, Edit2, Trash2, Users, MapPin, User } from "lucide-react";
import ModernHeader from "@/components/ModernHeader";
import BottomNavigation from "@/components/BottomNavigation";
import { useAuth } from "@/contexts/AuthContext";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import HouseholdManager from "@/components/household/HouseholdManager";

const Settings = () => {
  const navigate = useNavigate();
  const { user, householdName } = useAuth();
  const { settings, updateExpiryWarningMonths, isLoading: settingsLoading } = useUserSettings();
  const { tags, createTag, updateTag, deleteTag, isLoading: tagsLoading } = useTags();

  // Expiry threshold state
  const [expiryThreshold, setExpiryThreshold] = useState(settings.expiryWarningMonths);
  const [isUpdatingThreshold, setIsUpdatingThreshold] = useState(false);

  // Tag management state
  const [isCreateTagDialogOpen, setIsCreateTagDialogOpen] = useState(false);
  const [isEditTagDialogOpen, setIsEditTagDialogOpen] = useState(false);
  const [editingTag, setEditingTag] = useState<any>(null);
  const [newTagName, setNewTagName] = useState("");
  const [newTagColor, setNewTagColor] = useState("#0DCDB7");
  const [isCreatingTag, setIsCreatingTag] = useState(false);
  const [isUpdatingTag, setIsUpdatingTag] = useState(false);

  // Sync expiry threshold with settings
  React.useEffect(() => {
    setExpiryThreshold(settings.expiryWarningMonths);
  }, [settings.expiryWarningMonths]);

  const handleExpiryThresholdUpdate = async () => {
    if (expiryThreshold < 1 || expiryThreshold > 12) {
      toast.error("Le seuil d'expiration doit être entre 1 et 12 mois");
      return;
    }

    setIsUpdatingThreshold(true);
    const success = await updateExpiryWarningMonths(expiryThreshold);
    if (success) {
      toast.success("Seuil d'expiration mis à jour avec succès");
    }
    setIsUpdatingThreshold(false);
  };

  const handleCreateTag = async () => {
    if (!newTagName.trim()) {
      toast.error("Le nom de l'étiquette est requis");
      return;
    }

    setIsCreatingTag(true);
    const success = await createTag(newTagName.trim(), newTagColor);
    if (success) {
      setNewTagName("");
      setNewTagColor("#0DCDB7");
      setIsCreateTagDialogOpen(false);
    }
    setIsCreatingTag(false);
  };

  const handleEditTag = (tag: any) => {
    setEditingTag(tag);
    setNewTagName(tag.name);
    setNewTagColor(tag.color);
    setIsEditTagDialogOpen(true);
  };

  const handleUpdateTag = async () => {
    if (!editingTag || !newTagName.trim()) {
      toast.error("Le nom de l'étiquette est requis");
      return;
    }

    setIsUpdatingTag(true);
    const success = await updateTag(editingTag.id, newTagName.trim(), newTagColor);
    if (success) {
      setIsEditTagDialogOpen(false);
      setEditingTag(null);
      setNewTagName("");
      setNewTagColor("#0DCDB7");
    }
    setIsUpdatingTag(false);
  };

  const handleDeleteTag = async (tag: any) => {
    if (tag.isSystemTag) {
      toast.error("Impossible de supprimer une étiquette système");
      return;
    }

    if (tag.medicineCount && tag.medicineCount > 0) {
      toast.error("Impossible de supprimer une étiquette utilisée par des médicaments");
      return;
    }

    const success = await deleteTag(tag.id);
    if (!success) {
      toast.error("Erreur lors de la suppression de l'étiquette");
    }
  };

  const systemTags = tags.filter(tag => tag.isSystemTag);
  const customTags = tags.filter(tag => !tag.isSystemTag);

  const settingsGroups = [
    {
      title: "Compte",
      items: [
        {
          label: "Mon profil",
          icon: User,
          description: "Gérer mon compte et mes préférences",
          action: () => navigate("/profile")
        }
      ]
    },
    {
      title: "Gestion",
      items: [
        {
          label: "Membres de la famille",
          icon: Users,
          description: "Gérer les membres de ma famille",
          action: () => navigate("/family")
        },
        {
          label: "Emplacements",
          icon: MapPin,
          description: "Gérer mes emplacements de stockage",
          action: () => navigate("/locations")
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <ModernHeader
        title="Paramètres"
        showBackButton={true}
        onBackClick={() => navigate(-1)}
      />

      <div className="container mx-auto px-4 py-6 pb-24 max-w-4xl">
        <div className="space-y-6">

          {/* Profile Card */}
          <Card className="p-6 cursor-pointer border-teal/20 hover:border-teal/40 transition-colors" onClick={() => navigate("/profile")}>
            <div className="flex items-center">
              <Avatar className="h-14 w-14">
                <AvatarImage src={user?.user_metadata?.avatar_url} />
                <AvatarFallback className="bg-teal text-white text-lg">
                  {user?.user_metadata?.name?.charAt(0) || user?.email?.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="ml-4">
                <h3 className="font-medium text-lg text-navy">{user?.user_metadata?.name || user?.email}</h3>
                <p className="text-sm text-gray-600">
                  {user?.email}
                </p>
              </div>
            </div>
          </Card>

          {/* Household Section */}
          <div>
            <h2 className="text-lg font-medium mb-3 text-navy">Foyer</h2>
            <HouseholdManager />
          </div>

          {/* Medicine Settings */}
          <Card className="border-teal/20">
            <CardHeader>
              <CardTitle className="flex items-center text-navy">
                <Clock className="mr-2 h-5 w-5 text-teal" />
                Paramètres des médicaments
              </CardTitle>
              <CardDescription>
                Personnalisez les alertes et notifications pour vos médicaments
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <Label htmlFor="expiry-threshold">Seuil d'alerte d'expiration</Label>
                  <p className="text-sm text-muted-foreground">
                    Recevoir une alerte quand un médicament expire dans ce délai (en mois)
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Input
                    id="expiry-threshold"
                    type="number"
                    min="1"
                    max="12"
                    value={expiryThreshold}
                    onChange={(e) => setExpiryThreshold(parseInt(e.target.value) || 1)}
                    disabled={isUpdatingThreshold || settingsLoading}
                    className="w-20 text-center"
                  />
                  <span className="text-sm text-muted-foreground">mois</span>
                  <Button
                    onClick={handleExpiryThresholdUpdate}
                    disabled={isUpdatingThreshold || settingsLoading || expiryThreshold === settings.expiryWarningMonths}
                    size="sm"
                    className="bg-teal hover:bg-teal-dark text-white"
                  >
                    {isUpdatingThreshold ? 'Mise à jour...' : 'Enregistrer'}
                  </Button>
                </div>
              </div>

              <div className="text-xs text-muted-foreground bg-teal/5 p-3 rounded-lg">
                <strong>Note:</strong> Ce paramètre affecte toutes les alertes d'expiration dans l'application,
                y compris les notifications, les badges "Expire bientôt" et les statistiques du tableau de bord.
              </div>
            </CardContent>
          </Card>

          {/* Tag Management */}
          <Card className="border-teal/20">
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-navy">
                <div className="flex items-center">
                  <Tags className="mr-2 h-5 w-5 text-teal" />
                  Gestion des étiquettes
                </div>
                <Dialog open={isCreateTagDialogOpen} onOpenChange={setIsCreateTagDialogOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Plus size={16} className="mr-1" />
                      Nouvelle étiquette
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Créer une nouvelle étiquette</DialogTitle>
                      <DialogDescription>
                        Ajoutez une étiquette personnalisée pour organiser vos médicaments.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="new-tag-name">Nom de l'étiquette</Label>
                        <Input
                          id="new-tag-name"
                          value={newTagName}
                          onChange={(e) => setNewTagName(e.target.value)}
                          placeholder="Ex: Vitamines, Urgence..."
                          maxLength={50}
                        />
                      </div>
                      <div>
                        <Label htmlFor="new-tag-color">Couleur</Label>
                        <div className="flex items-center gap-2">
                          <Input
                            id="new-tag-color"
                            type="color"
                            value={newTagColor}
                            onChange={(e) => setNewTagColor(e.target.value)}
                            className="w-16 h-10"
                          />
                          <Badge style={{ backgroundColor: newTagColor, color: 'white' }}>
                            {newTagName || "Aperçu"}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setIsCreateTagDialogOpen(false)}>
                        Annuler
                      </Button>
                      <Button onClick={handleCreateTag} disabled={isCreatingTag}>
                        {isCreatingTag ? "Création..." : "Créer"}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardTitle>
              <CardDescription>
                Organisez vos médicaments avec des étiquettes personnalisées
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">

              {/* System Tags */}
              <div>
                <h3 className="text-sm font-medium text-navy mb-3">Étiquettes système</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {systemTags.map((tag) => (
                    <div key={tag.id} className="flex items-center justify-between p-2 border rounded-lg">
                      <div className="flex items-center gap-2">
                        <div
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: tag.color }}
                        />
                        <span className="text-sm font-medium">{tag.name}</span>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {tag.medicineCount || 0}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>

              {/* Custom Tags */}
              <div>
                <h3 className="text-sm font-medium text-navy mb-3">Étiquettes personnalisées</h3>
                {customTags.length === 0 ? (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    Aucune étiquette personnalisée. Créez-en une pour mieux organiser vos médicaments.
                  </p>
                ) : (
                  <div className="space-y-2">
                    {customTags.map((tag) => (
                      <div key={tag.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div
                            className="w-4 h-4 rounded-full"
                            style={{ backgroundColor: tag.color }}
                          />
                          <span className="font-medium">{tag.name}</span>
                          <Badge variant="secondary" className="text-xs">
                            {tag.medicineCount || 0} médicament(s)
                          </Badge>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => handleEditTag(tag)}
                          >
                            <Edit2 size={14} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                            onClick={() => handleDeleteTag(tag)}
                            disabled={tag.medicineCount && tag.medicineCount > 0}
                          >
                            <Trash2 size={14} />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Other Settings Groups */}
          {settingsGroups.map((group, groupIndex) => (
            <div key={groupIndex}>
              <h2 className="text-lg font-medium mb-3 text-navy">{group.title}</h2>
              <div className="space-y-3">
                {group.items.map((item, itemIndex) => (
                  <Card
                    key={itemIndex}
                    className="p-4 cursor-pointer border-gray-200 hover:border-teal/30 hover:bg-teal/5 transition-colors"
                    onClick={item.action}
                  >
                    <div className="flex items-center">
                      <div className="bg-teal/10 p-2 rounded-full">
                        <item.icon size={20} className="text-teal" />
                      </div>
                      <div className="ml-4">
                        <h3 className="font-medium text-navy">{item.label}</h3>
                        <p className="text-sm text-gray-600">{item.description}</p>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Edit Tag Dialog */}
      <Dialog open={isEditTagDialogOpen} onOpenChange={setIsEditTagDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Modifier l'étiquette</DialogTitle>
            <DialogDescription>
              Modifiez le nom et la couleur de votre étiquette personnalisée.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-tag-name">Nom de l'étiquette</Label>
              <Input
                id="edit-tag-name"
                value={newTagName}
                onChange={(e) => setNewTagName(e.target.value)}
                placeholder="Ex: Vitamines, Urgence..."
                maxLength={50}
              />
            </div>
            <div>
              <Label htmlFor="edit-tag-color">Couleur</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="edit-tag-color"
                  type="color"
                  value={newTagColor}
                  onChange={(e) => setNewTagColor(e.target.value)}
                  className="w-16 h-10"
                />
                <Badge style={{ backgroundColor: newTagColor, color: 'white' }}>
                  {newTagName || "Aperçu"}
                </Badge>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditTagDialogOpen(false)}>
              Annuler
            </Button>
            <Button onClick={handleUpdateTag} disabled={isUpdatingTag}>
              {isUpdatingTag ? "Mise à jour..." : "Mettre à jour"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <BottomNavigation />
    </div>
  );
};

export default Settings;
