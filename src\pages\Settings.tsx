
import React from "react";
import { useNavigate } from "react-router-dom";
import ModernHeader from "@/components/ModernHeader";
import BottomNavigation from "@/components/BottomNavigation";
import { Button } from "@/components/ui/button";
import { Users, MapPin, ArrowLeft, User, Home } from "lucide-react";
import { Card } from "@/components/ui/card";
import { useAuth } from "@/contexts/AuthContext";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import HouseholdManager from "@/components/household/HouseholdManager";
import FloatingActionButton from "@/components/FloatingActionButton";

const Settings = () => {
  const navigate = useNavigate();
  const { user, householdName } = useAuth();

  // Configuration to hide demo features
  const showDemoFeatures = false;

  const settingsGroups = [
    {
      title: "Compte",
      items: [
        {
          label: "Mon profil",
          icon: User,
          description: "Gérer mon compte et mes préférences",
          action: () => navigate("/profile")
        }
      ]
    },
    {
      title: "Gestion",
      items: [
        { 
          label: "Membres de la famille", 
          icon: Users, 
          description: "G<PERSON>rer les membres de ma famille",
          action: () => navigate("/family") 
        },
        { 
          label: "Emplacements", 
          icon: MapPin, 
          description: "Gérer mes emplacements de stockage",
          action: () => navigate("/locations") 
        }
      ]
    }
  ];

  return (
    <div className="medicinet-container animate-fade-in pb-20">
      <ModernHeader
        title="Paramètres"
        subtitle="Gérez votre compte et vos préférences"
        showBackButton={true}
        variant="navy"
      />

      {/* Profile Card */}
      <Card className="p-6 mb-6 cursor-pointer border-teal/20 hover:border-teal/40 transition-colors" onClick={() => navigate("/profile")}>
        <div className="flex items-center">
          <Avatar className="h-14 w-14">
            <AvatarImage src={user?.user_metadata?.avatar_url} />
            <AvatarFallback className="bg-teal text-white text-lg">
              {user?.user_metadata?.name?.charAt(0) || user?.email?.charAt(0)}
            </AvatarFallback>
          </Avatar>
          <div className="ml-4">
            <h3 className="font-medium text-lg text-navy">{user?.user_metadata?.name || user?.email}</h3>
            <p className="text-sm text-gray-600">
              {user?.email}
            </p>
          </div>
        </div>
      </Card>

      {/* Household Section */}
      <div className="mb-6">
        <h2 className="text-lg font-medium mb-3 text-navy">Foyer</h2>
        <HouseholdManager />
      </div>

      {settingsGroups.map((group, groupIndex) => (
        <div key={groupIndex} className="mb-6">
          <h2 className="text-lg font-medium mb-3 text-navy">{group.title}</h2>
          <div className="space-y-3">
            {group.items.map((item, itemIndex) => (
              <Card
                key={itemIndex}
                className="p-4 cursor-pointer border-gray-200 hover:border-teal/30 hover:bg-teal/5 transition-colors"
                onClick={item.action}
              >
                <div className="flex items-center">
                  <div className="bg-teal/10 p-2 rounded-full">
                    <item.icon size={20} className="text-teal" />
                  </div>
                  <div className="ml-4">
                    <h3 className="font-medium text-navy">{item.label}</h3>
                    <p className="text-sm text-gray-600">{item.description}</p>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      ))}
      
      <FloatingActionButton />
      <BottomNavigation />
    </div>
  );
};

export default Settings;
