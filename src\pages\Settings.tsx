
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useUserSettings } from "@/hooks/useUserSettings";
import { useTags } from "@/hooks/useTags";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { ArrowLeft, Clock, Tags, Users, MapPin, User } from "lucide-react";
import ModernHeader from "@/components/ModernHeader";
import BottomNavigation from "@/components/BottomNavigation";
import { useAuth } from "@/contexts/AuthContext";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import HouseholdManager from "@/components/household/HouseholdManager";

const Settings = () => {
  const navigate = useNavigate();
  const { user, householdName } = useAuth();
  const { settings, updateExpiryWarningMonths, isLoading: settingsLoading } = useUserSettings();
  const { tags, createTag, updateTag, deleteTag, isLoading: tagsLoading } = useTags();

  // Expiry threshold state
  const [expiryThreshold, setExpiryThreshold] = useState(settings.expiryWarningMonths);
  const [isUpdatingThreshold, setIsUpdatingThreshold] = useState(false);



  // Sync expiry threshold with settings
  React.useEffect(() => {
    setExpiryThreshold(settings.expiryWarningMonths);
  }, [settings.expiryWarningMonths]);

  const handleExpiryThresholdUpdate = async () => {
    if (expiryThreshold < 1 || expiryThreshold > 12) {
      toast.error("Le seuil d'expiration doit être entre 1 et 12 mois");
      return;
    }

    setIsUpdatingThreshold(true);
    const success = await updateExpiryWarningMonths(expiryThreshold);
    if (success) {
      toast.success("Seuil d'expiration mis à jour avec succès");
    }
    setIsUpdatingThreshold(false);
  };



  const settingsGroups = [
    {
      title: "Compte",
      items: [
        {
          label: "Mon profil",
          icon: User,
          description: "Gérer mon compte et mes préférences",
          action: () => navigate("/profile")
        }
      ]
    },
    {
      title: "Gestion",
      items: [
        {
          label: "Membres de la famille",
          icon: Users,
          description: "Gérer les membres de ma famille",
          action: () => navigate("/family")
        },
        {
          label: "Emplacements",
          icon: MapPin,
          description: "Gérer mes emplacements de stockage",
          action: () => navigate("/locations")
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <ModernHeader
        title="Paramètres"
        showBackButton={true}
        onBackClick={() => navigate(-1)}
      />

      <div className="container mx-auto px-4 py-6 pb-24 max-w-4xl">
        <div className="space-y-6">

          {/* Profile Card */}
          <Card className="p-6 cursor-pointer border-teal/20 hover:border-teal/40 transition-colors" onClick={() => navigate("/profile")}>
            <div className="flex items-center">
              <Avatar className="h-14 w-14">
                <AvatarImage src={user?.user_metadata?.avatar_url} />
                <AvatarFallback className="bg-teal text-white text-lg">
                  {user?.user_metadata?.name?.charAt(0) || user?.email?.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="ml-4">
                <h3 className="font-medium text-lg text-navy">{user?.user_metadata?.name || user?.email}</h3>
                <p className="text-sm text-gray-600">
                  {user?.email}
                </p>
              </div>
            </div>
          </Card>

          {/* Household Section */}
          <div>
            <h2 className="text-lg font-medium mb-3 text-navy">Foyer</h2>
            <HouseholdManager />
          </div>

          {/* Medicine Settings */}
          <Card className="border-teal/20">
            <CardHeader>
              <CardTitle className="flex items-center text-navy">
                <Clock className="mr-2 h-5 w-5 text-teal" />
                Paramètres des médicaments
              </CardTitle>
              <CardDescription>
                Personnalisez les alertes et notifications pour vos médicaments
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <Label htmlFor="expiry-threshold">Seuil d'alerte d'expiration</Label>
                  <p className="text-sm text-muted-foreground">
                    Recevoir une alerte quand un médicament expire dans ce délai (en mois)
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Input
                    id="expiry-threshold"
                    type="number"
                    min="1"
                    max="12"
                    value={expiryThreshold}
                    onChange={(e) => setExpiryThreshold(parseInt(e.target.value) || 1)}
                    disabled={isUpdatingThreshold || settingsLoading}
                    className="w-20 text-center"
                  />
                  <span className="text-sm text-muted-foreground">mois</span>
                  <Button
                    onClick={handleExpiryThresholdUpdate}
                    disabled={isUpdatingThreshold || settingsLoading || expiryThreshold === settings.expiryWarningMonths}
                    size="sm"
                    className="bg-teal hover:bg-teal-dark text-white"
                  >
                    {isUpdatingThreshold ? 'Mise à jour...' : 'Enregistrer'}
                  </Button>
                </div>
              </div>

              <div className="text-xs text-muted-foreground bg-teal/5 p-3 rounded-lg">
                <strong>Note:</strong> Ce paramètre affecte toutes les alertes d'expiration dans l'application,
                y compris les notifications, les badges "Expire bientôt" et les statistiques du tableau de bord.
              </div>
            </CardContent>
          </Card>

          {/* Pharmaceutical Tags Reference */}
          <Card className="border-teal/20">
            <CardHeader>
              <CardTitle className="flex items-center text-navy">
                <Tags className="mr-2 h-5 w-5 text-teal" />
                Étiquettes pharmaceutiques
              </CardTitle>
              <CardDescription>
                Système d'étiquetage standardisé pour une organisation professionnelle de vos médicaments
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">

              {/* Therapeutic Classes */}
              <div>
                <h3 className="text-sm font-medium text-navy mb-3 flex items-center">
                  💊 Classes Thérapeutiques
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {tags.filter(tag => tag.category === 'therapeutic').map((tag) => (
                    <div key={tag.id} className="flex items-center justify-between p-2 border rounded-lg">
                      <div className="flex items-center gap-2">
                        <div
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: tag.color }}
                        />
                        <span className="text-sm font-medium">{tag.name}</span>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {tag.medicineCount || 0}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>

              {/* Usage/Domain Tags */}
              <div>
                <h3 className="text-sm font-medium text-navy mb-3 flex items-center">
                  🩺 Domaines d'Usage
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {tags.filter(tag => tag.category === 'usage').map((tag) => (
                    <div key={tag.id} className="flex items-center justify-between p-2 border rounded-lg">
                      <div className="flex items-center gap-2">
                        <div
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: tag.color }}
                        />
                        <span className="text-sm font-medium">{tag.name}</span>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {tag.medicineCount || 0}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>

              <div className="text-xs text-muted-foreground bg-teal/5 p-3 rounded-lg">
                <strong>Note:</strong> Ce système d'étiquetage standardisé est optimisé pour l'usage pharmaceutique
                en Tunisie et dans les régions francophones. Les étiquettes sont prédéfinies pour assurer
                une organisation cohérente et professionnelle.
              </div>
            </CardContent>
          </Card>

          {/* Other Settings Groups */}
          {settingsGroups.map((group, groupIndex) => (
            <div key={groupIndex}>
              <h2 className="text-lg font-medium mb-3 text-navy">{group.title}</h2>
              <div className="space-y-3">
                {group.items.map((item, itemIndex) => (
                  <Card
                    key={itemIndex}
                    className="p-4 cursor-pointer border-gray-200 hover:border-teal/30 hover:bg-teal/5 transition-colors"
                    onClick={item.action}
                  >
                    <div className="flex items-center">
                      <div className="bg-teal/10 p-2 rounded-full">
                        <item.icon size={20} className="text-teal" />
                      </div>
                      <div className="ml-4">
                        <h3 className="font-medium text-navy">{item.label}</h3>
                        <p className="text-sm text-gray-600">{item.description}</p>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>



      <BottomNavigation />
    </div>
  );
};

export default Settings;
