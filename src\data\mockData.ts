
import { Medicine, Location, User } from "@/types";

export const mockMedicines: Medicine[] = [
  {
    id: "med-1",
    name: "Paracétamol",
    dosage: "500mg",
    quantity: 24,
    expiryDate: "2025-10-15",
    category: "pain",
    location: "loc-1",
    notes: "For headaches and fever",
    createdAt: "2023-05-20T14:30:00Z",
    updatedAt: "2023-05-20T14:30:00Z"
  },
  {
    id: "med-2",
    name: "Ibuprofène",
    dosage: "200mg",
    quantity: 10,
    expiryDate: "2023-12-30",
    category: "pain",
    location: "loc-1",
    notes: "Anti-inflammatory",
    createdAt: "2023-06-15T09:20:00Z",
    updatedAt: "2023-06-15T09:20:00Z"
  },
  {
    id: "med-3",
    name: "Doliprane",
    dosage: "1000mg",
    quantity: 8,
    expiryDate: "2025-08-22",
    category: "pain",
    location: "loc-1",
    notes: "For high fever",
    createdAt: "2023-07-03T16:45:00Z",
    updatedAt: "2023-07-03T16:45:00Z"
  },
  {
    id: "med-4",
    name: "<PERSON><PERSON><PERSON>ine",
    dosage: "10mg",
    quantity: 2,
    expiryDate: "2024-04-10",
    category: "allergy",
    location: "loc-2",
    notes: "For allergies",
    createdAt: "2023-08-12T11:15:00Z",
    updatedAt: "2023-08-12T11:15:00Z"
  },
  {
    id: "med-5",
    name: "Smecta",
    dosage: "3g",
    quantity: 12,
    expiryDate: "2025-01-20",
    category: "digestion",
    location: "loc-1",
    notes: "For diarrhea",
    createdAt: "2023-09-08T13:50:00Z",
    updatedAt: "2023-09-08T13:50:00Z"
  },
  {
    id: "med-6",
    name: "Amoxicilline",
    dosage: "500mg",
    quantity: 0,
    expiryDate: "2023-07-15",
    category: "prescription",
    location: "loc-3",
    notes: "Need renewal",
    createdAt: "2023-01-25T10:30:00Z",
    updatedAt: "2023-07-25T10:30:00Z"
  }
];

export const mockLocations: Location[] = [
  {
    id: "loc-1",
    name: "Armoire à pharmacie",
    icon: "home"
  },
  {
    id: "loc-2",
    name: "Trousse de voyage",
    icon: "luggage"
  },
  {
    id: "loc-3",
    name: "Chez Mamie",
    icon: "heart"
  }
];

export const mockUsers: User[] = [
  {
    id: "user-1",
    name: "Sophie",
    avatar: "",
    isAdmin: true
  },
  {
    id: "user-2",
    name: "Marc",
    avatar: "",
    isAdmin: false
  },
  {
    id: "user-3",
    name: "Mamie",
    avatar: "",
    isAdmin: false
  }
];
