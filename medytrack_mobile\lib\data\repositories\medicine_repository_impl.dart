import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/network/network_info.dart';
import '../../domain/entities/medicine.dart';
import '../../domain/repositories/medicine_repository.dart';
import '../datasources/medicine_remote_data_source.dart';
import '../datasources/medicine_local_data_source.dart';
import '../models/medicine_model.dart';

class MedicineRepositoryImpl implements MedicineRepository {
  final MedicineRemoteDataSource remoteDataSource;
  final MedicineLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  MedicineRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<Medicine>>> getMedicines() async {
    if (await networkInfo.isConnected) {
      try {
        final medicines = await remoteDataSource.getMedicines();
        await localDataSource.cacheMedicines(medicines);
        return Right(medicines);
      } catch (e) {
        // Try to get cached data if remote fails
        try {
          final cachedMedicines = await localDataSource.getCachedMedicines();
          return Right(cachedMedicines);
        } catch (cacheError) {
          return Left(ServerFailure(e.toString()));
        }
      }
    } else {
      try {
        final cachedMedicines = await localDataSource.getCachedMedicines();
        return Right(cachedMedicines);
      } catch (e) {
        return const Left(NetworkFailure('No internet connection and no cached data'));
      }
    }
  }

  @override
  Future<Either<Failure, Medicine>> addMedicine(Medicine medicine) async {
    if (await networkInfo.isConnected) {
      try {
        final medicineModel = MedicineModel(
          id: medicine.id,
          name: medicine.name,
          dosage: medicine.dosage,
          form: medicine.form,
          presentation: medicine.presentation,
          quantity: medicine.quantity,
          expiryDate: medicine.expiryDate,
          tags: medicine.tags,
          locationId: medicine.locationId,
          locationName: medicine.locationName,
          imageUrl: medicine.imageUrl,
          notes: medicine.notes,
          barcode: medicine.barcode,
          medicineId: medicine.medicineId,
          customName: medicine.customName,
          isCustom: medicine.isCustom,
          lowStockThreshold: medicine.lowStockThreshold,
          familyMember: medicine.familyMember,
          createdAt: medicine.createdAt,
          updatedAt: medicine.updatedAt,
          laboratoire: medicine.laboratoire,
          dci: medicine.dci,
          classe: medicine.classe,
          sousClasse: medicine.sousClasse,
          indications: medicine.indications,
        );
        
        final result = await remoteDataSource.addMedicine(medicineModel);
        return Right(result);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Medicine>> updateMedicine(Medicine medicine) async {
    if (await networkInfo.isConnected) {
      try {
        final medicineModel = MedicineModel(
          id: medicine.id,
          name: medicine.name,
          dosage: medicine.dosage,
          form: medicine.form,
          presentation: medicine.presentation,
          quantity: medicine.quantity,
          expiryDate: medicine.expiryDate,
          tags: medicine.tags,
          locationId: medicine.locationId,
          locationName: medicine.locationName,
          imageUrl: medicine.imageUrl,
          notes: medicine.notes,
          barcode: medicine.barcode,
          medicineId: medicine.medicineId,
          customName: medicine.customName,
          isCustom: medicine.isCustom,
          lowStockThreshold: medicine.lowStockThreshold,
          familyMember: medicine.familyMember,
          createdAt: medicine.createdAt,
          updatedAt: medicine.updatedAt,
          laboratoire: medicine.laboratoire,
          dci: medicine.dci,
          classe: medicine.classe,
          sousClasse: medicine.sousClasse,
          indications: medicine.indications,
        );
        
        final result = await remoteDataSource.updateMedicine(medicineModel);
        return Right(result);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteMedicine(String id) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.deleteMedicine(id);
        return const Right(null);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }
}
