import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Search, Filter, X, ChevronDown, ChevronUp } from "lucide-react";
import { Location } from "@/types";
import { useTags } from "@/hooks/useTags";

export interface FilterState {
  search: string;
  categories: string[]; // Keep for backward compatibility during transition
  tags: string[]; // New tag-based filtering
  locations: string[];
  expiryStatus: 'all' | 'expired' | 'expiring' | 'valid';
  stockStatus: 'all' | 'low' | 'out' | 'in-stock';
}

interface MedicineFiltersProps {
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  locations: Location[];
  onClearFilters: () => void;
}

const CATEGORIES = [
  { value: 'pain', label: 'Douleur' },
  { value: 'cold', label: 'Rhume' },
  { value: 'allergy', label: 'Allergie' },
  { value: 'digestion', label: 'Digestion' },
  { value: 'first-aid', label: 'Premiers soins' },
  { value: 'prescription', label: 'Ordonnance' },
  { value: 'other', label: 'Autre' }
];

const EXPIRY_OPTIONS = [
  { value: 'all', label: 'Tous' },
  { value: 'expired', label: 'Expirés' },
  { value: 'expiring', label: 'Expirent bientôt' },
  { value: 'valid', label: 'Valides' }
];

const STOCK_OPTIONS = [
  { value: 'all', label: 'Tous' },
  { value: 'out', label: 'Rupture' },
  { value: 'low', label: 'Stock bas' },
  { value: 'in-stock', label: 'En stock' }
];

const MedicineFilters = ({
  filters,
  onFiltersChange,
  locations,
  onClearFilters
}: MedicineFiltersProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { tags, isLoading: tagsLoading } = useTags();

  const updateFilters = (updates: Partial<FilterState>) => {
    onFiltersChange({ ...filters, ...updates });
  };

  const toggleCategory = (category: string) => {
    const newCategories = filters.categories.includes(category)
      ? filters.categories.filter(c => c !== category)
      : [...filters.categories, category];
    updateFilters({ categories: newCategories });
  };

  const toggleTag = (tagId: string) => {
    const newTags = filters.tags.includes(tagId)
      ? filters.tags.filter(t => t !== tagId)
      : [...filters.tags, tagId];
    updateFilters({ tags: newTags });
  };

  const toggleLocation = (locationId: string) => {
    const newLocations = filters.locations.includes(locationId)
      ? filters.locations.filter(l => l !== locationId)
      : [...filters.locations, locationId];
    updateFilters({ locations: newLocations });
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.categories.length > 0) count++;
    if (filters.tags.length > 0) count++;
    if (filters.locations.length > 0) count++;
    if (filters.expiryStatus !== 'all') count++;
    if (filters.stockStatus !== 'all') count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <Card className="border-teal/20">
      <CardContent className="p-4">
        {/* Search Bar */}
        <div className="space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4" />
            <Input
              type="text"
              placeholder="Rechercher par nom, notes, dosage..."
              value={filters.search}
              onChange={(e) => updateFilters({ search: e.target.value })}
              className="pl-10 border-teal/20 focus:border-teal focus:ring-teal"
            />
          </div>

          {/* Filter Toggle Button */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={() => setIsExpanded(!isExpanded)}
              className="border-teal/30 text-teal hover:bg-teal/10"
            >
              <Filter size={16} className="mr-2" />
              Filtres
              {activeFilterCount > 0 && (
                <Badge className="ml-2 bg-teal text-white">
                  {activeFilterCount}
                </Badge>
              )}
              {isExpanded ? <ChevronUp size={16} className="ml-2" /> : <ChevronDown size={16} className="ml-2" />}
            </Button>

            {activeFilterCount > 0 && (
              <Button
                variant="ghost"
                onClick={onClearFilters}
                className="text-gray-500 hover:text-red-500"
              >
                <X size={16} className="mr-1" />
                Effacer
              </Button>
            )}
          </div>

          {/* Expanded Filters */}
          {isExpanded && (
            <div className="space-y-4 pt-4 border-t border-gray-200">
              {/* Quick Status Filters */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-navy">Statut d'expiration</Label>
                  <Select 
                    value={filters.expiryStatus} 
                    onValueChange={(value: any) => updateFilters({ expiryStatus: value })}
                  >
                    <SelectTrigger className="mt-1 border-teal/20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {EXPIRY_OPTIONS.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-sm font-medium text-navy">Statut du stock</Label>
                  <Select 
                    value={filters.stockStatus} 
                    onValueChange={(value: any) => updateFilters({ stockStatus: value })}
                  >
                    <SelectTrigger className="mt-1 border-teal/20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {STOCK_OPTIONS.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Tags */}
              <div>
                <Label className="text-sm font-medium text-navy">Étiquettes</Label>
                {tagsLoading ? (
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin h-4 w-4 border-2 border-teal border-t-transparent rounded-full" />
                  </div>
                ) : (
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    {tags.map(tag => (
                      <div key={tag.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`tag-${tag.id}`}
                          checked={filters.tags.includes(tag.id)}
                          onCheckedChange={() => toggleTag(tag.id)}
                          className="border-teal/30 data-[state=checked]:bg-teal data-[state=checked]:border-teal"
                        />
                        <Label
                          htmlFor={`tag-${tag.id}`}
                          className="text-sm cursor-pointer flex items-center gap-2"
                        >
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: tag.color }}
                          />
                          {tag.name}
                          {tag.medicineCount !== undefined && tag.medicineCount > 0 && (
                            <Badge variant="secondary" className="text-xs">
                              {tag.medicineCount}
                            </Badge>
                          )}
                        </Label>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Locations */}
              {locations.length > 0 && (
                <div>
                  <Label className="text-sm font-medium text-navy">Emplacements</Label>
                  <div className="grid grid-cols-1 gap-2 mt-2">
                    {locations.map(location => (
                      <div key={location.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`location-${location.id}`}
                          checked={filters.locations.includes(location.id)}
                          onCheckedChange={() => toggleLocation(location.id)}
                          className="border-teal/30 data-[state=checked]:bg-teal data-[state=checked]:border-teal"
                        />
                        <Label 
                          htmlFor={`location-${location.id}`}
                          className="text-sm cursor-pointer"
                        >
                          {location.name}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default MedicineFilters;
