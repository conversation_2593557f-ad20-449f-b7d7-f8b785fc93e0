-- MedyTrack v1.4.2 Production Migration Script
-- This script contains all necessary changes for the v1.4.2 release
-- Execute this script to upgrade from v1.4.1 to v1.4.2

-- =============================================================================
-- FEATURE 1: Custom Expiry Warning Thresholds
-- =============================================================================

-- Add expiry_warning_days column to users table (stores months, not days)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'expiry_warning_days'
  ) THEN
    ALTER TABLE public.users ADD COLUMN expiry_warning_days INTEGER DEFAULT 1;
    COMMENT ON COLUMN public.users.expiry_warning_days IS 'Custom expiry warning threshold in MONTHS (not days despite column name). Default is 1 month.';
  END IF;
END $$;

-- Update existing users to have default threshold
UPDATE public.users 
SET expiry_warning_days = 1 
WHERE expiry_warning_days IS NULL;

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_users_expiry_warning 
ON public.users(id, expiry_warning_days);

-- =============================================================================
-- FEATURE 2: Standardized Pharmaceutical Tagging System
-- =============================================================================

-- Create tags table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.tags (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(50) NOT NULL,
  color VARCHAR(7) NOT NULL DEFAULT '#0DCDB7',
  household_id UUID NOT NULL,
  is_system_tag BOOLEAN DEFAULT TRUE,
  category VARCHAR(50) NOT NULL DEFAULT 'therapeutic',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(name, household_id)
);

-- Create medicine_tags table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.medicine_tags (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_medicine_id UUID NOT NULL,
  tag_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_medicine_id, tag_id)
);

-- Add foreign key constraints
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'tags_household_id_fkey'
  ) THEN
    ALTER TABLE public.tags 
    ADD CONSTRAINT tags_household_id_fkey 
    FOREIGN KEY (household_id) REFERENCES households(id) ON DELETE CASCADE;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'medicine_tags_user_medicine_id_fkey'
  ) THEN
    ALTER TABLE public.medicine_tags 
    ADD CONSTRAINT medicine_tags_user_medicine_id_fkey 
    FOREIGN KEY (user_medicine_id) REFERENCES user_medicines(id) ON DELETE CASCADE;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'medicine_tags_tag_id_fkey'
  ) THEN
    ALTER TABLE public.medicine_tags 
    ADD CONSTRAINT medicine_tags_tag_id_fkey 
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE;
  END IF;
END $$;

-- Clear existing tags and replace with pharmaceutical tags
DELETE FROM public.medicine_tags;
DELETE FROM public.tags;

-- Insert Therapeutic Classes (💊 Classes Thérapeutiques)
INSERT INTO public.tags (name, color, household_id, is_system_tag, category)
SELECT 
  tag_name,
  tag_color,
  h.id as household_id,
  true as is_system_tag,
  'therapeutic' as category
FROM households h
CROSS JOIN (
  VALUES 
    ('antibiotique', '#E53E3E'),
    ('antalgique', '#3182CE'),
    ('anti-inflammatoire', '#D69E2E'),
    ('antipyrétique', '#38A169'),
    ('antiallergique', '#805AD5'),
    ('antispasmodique', '#DD6B20'),
    ('corticoïde', '#C53030'),
    ('antifongique', '#2B6CB0'),
    ('antivirale', '#2C7A7B'),
    ('antihypertenseur', '#1A365D'),
    ('antidiabétique', '#553C9A'),
    ('psychotrope', '#744210')
) AS therapeutic_tags(tag_name, tag_color)
ON CONFLICT (name, household_id) DO NOTHING;

-- Insert Usage/Domain Tags (🩺 Domaines d'Usage)
INSERT INTO public.tags (name, color, household_id, is_system_tag, category)
SELECT 
  tag_name,
  tag_color,
  h.id as household_id,
  true as is_system_tag,
  'usage' as category
FROM households h
CROSS JOIN (
  VALUES 
    ('parapharmacie', '#0DCDB7'),
    ('premiers_soins', '#E53E3E'),
    ('complément_alimentaire', '#38A169'),
    ('soins_peau', '#ED8936'),
    ('soins_yeux', '#3182CE'),
    ('soins_oreilles', '#805AD5'),
    ('soins_bouche', '#D69E2E'),
    ('digestif', '#48BB78'),
    ('respiratoire', '#4299E1'),
    ('pédiatrique', '#F56565'),
    ('gynécologie', '#ED64A6'),
    ('dermatologie', '#F6AD55')
) AS usage_tags(tag_name, tag_color)
ON CONFLICT (name, household_id) DO NOTHING;

-- =============================================================================
-- SECURITY AND PERMISSIONS
-- =============================================================================

-- Enable RLS
ALTER TABLE public.tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.medicine_tags ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
DROP POLICY IF EXISTS "tags_household_policy" ON public.tags;
CREATE POLICY "tags_household_policy" ON public.tags
  FOR ALL USING (
    household_id = (SELECT household_id FROM users WHERE id = auth.uid())
  );

DROP POLICY IF EXISTS "medicine_tags_household_policy" ON public.medicine_tags;
CREATE POLICY "medicine_tags_household_policy" ON public.medicine_tags
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_medicines um 
      WHERE um.id = medicine_tags.user_medicine_id 
      AND um.household_id = (SELECT household_id FROM users WHERE id = auth.uid())
    )
  );

-- Grant permissions
GRANT SELECT, INSERT, DELETE ON public.tags TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.medicine_tags TO authenticated;

-- =============================================================================
-- DATABASE FUNCTIONS
-- =============================================================================

-- Update get_household_tags function
DROP FUNCTION IF EXISTS public.get_household_tags(UUID);

CREATE OR REPLACE FUNCTION public.get_household_tags(p_household_id UUID)
RETURNS TABLE (
  id UUID,
  name VARCHAR(50),
  color VARCHAR(7),
  is_system_tag BOOLEAN,
  category VARCHAR(50),
  medicine_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.id,
    t.name,
    t.color,
    t.is_system_tag,
    t.category,
    COUNT(mt.id) as medicine_count
  FROM public.tags t
  LEFT JOIN public.medicine_tags mt ON t.id = mt.tag_id
  WHERE t.household_id = p_household_id
  GROUP BY t.id, t.name, t.color, t.is_system_tag, t.category
  ORDER BY t.category ASC, t.name ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_household_tags(UUID) TO authenticated;

-- =============================================================================
-- PERFORMANCE OPTIMIZATION
-- =============================================================================

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_tags_household_category ON public.tags(household_id, category);
CREATE INDEX IF NOT EXISTS idx_tags_category ON public.tags(category);
CREATE INDEX IF NOT EXISTS idx_medicine_tags_medicine_id ON public.medicine_tags(user_medicine_id);
CREATE INDEX IF NOT EXISTS idx_medicine_tags_tag_id ON public.medicine_tags(tag_id);

-- =============================================================================
-- VERIFICATION
-- =============================================================================

-- Verify migration success
DO $$
DECLARE
  tag_count INTEGER;
  household_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO tag_count FROM public.tags;
  SELECT COUNT(*) INTO household_count FROM public.households;
  
  -- Each household should have 24 pharmaceutical tags (12 therapeutic + 12 usage)
  IF tag_count != (household_count * 24) THEN
    RAISE EXCEPTION 'Migration verification failed: Expected % tags, found %', 
      (household_count * 24), tag_count;
  END IF;
  
  RAISE NOTICE 'Migration completed successfully: % pharmaceutical tags created for % households', 
    tag_count, household_count;
END $$;

-- Add comments for documentation
COMMENT ON TABLE public.tags IS 'Standardized pharmaceutical tags for therapeutic classes and usage domains';
COMMENT ON COLUMN public.tags.category IS 'Tag category: therapeutic (classes thérapeutiques) or usage (domaines d''usage)';
COMMENT ON TABLE public.medicine_tags IS 'Many-to-many relationship between medicines and pharmaceutical tags';
COMMENT ON FUNCTION public.get_household_tags(UUID) IS 'Get all pharmaceutical tags for a household with medicine counts and categories';

-- Migration completed
SELECT 'MedyTrack v1.4.2 migration completed successfully!' as status;
