import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../domain/entities/medicine.dart';
import '../../../domain/usecases/medicine/get_medicines_usecase.dart';
import '../../../domain/usecases/medicine/add_medicine_usecase.dart';
import '../../../domain/usecases/medicine/update_medicine_usecase.dart';
import '../../../domain/usecases/medicine/delete_medicine_usecase.dart';

part 'medicine_event.dart';
part 'medicine_state.dart';

class MedicineBloc extends Bloc<MedicineEvent, MedicineState> {
  final GetMedicinesUseCase getMedicinesUseCase;
  final AddMedicineUseCase addMedicineUseCase;
  final UpdateMedicineUseCase updateMedicineUseCase;
  final DeleteMedicineUseCase deleteMedicineUseCase;

  MedicineBloc({
    required this.getMedicinesUseCase,
    required this.addMedicineUseCase,
    required this.updateMedicineUseCase,
    required this.deleteMedicineUseCase,
  }) : super(MedicineInitial()) {
    on<MedicineLoadRequested>(_onLoadRequested);
    on<MedicineAddRequested>(_onAddRequested);
    on<MedicineUpdateRequested>(_onUpdateRequested);
    on<MedicineDeleteRequested>(_onDeleteRequested);
  }

  Future<void> _onLoadRequested(
    MedicineLoadRequested event,
    Emitter<MedicineState> emit,
  ) async {
    emit(MedicineLoading());

    final result = await getMedicinesUseCase();
    
    result.fold(
      (failure) => emit(MedicineError(failure.message)),
      (medicines) => emit(MedicineLoaded(medicines)),
    );
  }

  Future<void> _onAddRequested(
    MedicineAddRequested event,
    Emitter<MedicineState> emit,
  ) async {
    emit(MedicineLoading());

    final result = await addMedicineUseCase(event.medicine);
    
    result.fold(
      (failure) => emit(MedicineError(failure.message)),
      (medicine) {
        // Reload medicines after adding
        add(MedicineLoadRequested());
      },
    );
  }

  Future<void> _onUpdateRequested(
    MedicineUpdateRequested event,
    Emitter<MedicineState> emit,
  ) async {
    emit(MedicineLoading());

    final result = await updateMedicineUseCase(event.medicine);
    
    result.fold(
      (failure) => emit(MedicineError(failure.message)),
      (medicine) {
        // Reload medicines after updating
        add(MedicineLoadRequested());
      },
    );
  }

  Future<void> _onDeleteRequested(
    MedicineDeleteRequested event,
    Emitter<MedicineState> emit,
  ) async {
    emit(MedicineLoading());

    final result = await deleteMedicineUseCase(event.id);
    
    result.fold(
      (failure) => emit(MedicineError(failure.message)),
      (_) {
        // Reload medicines after deleting
        add(MedicineLoadRequested());
      },
    );
  }
}
