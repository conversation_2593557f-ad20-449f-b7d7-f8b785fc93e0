import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Tag } from '@/types';
import { toast } from 'sonner';

/**
 * Custom hook for managing pharmaceutical tags in MedyTrack
 *
 * This hook provides access to the standardized pharmaceutical tagging system
 * with predefined therapeutic classes and usage domains. All tags are system-managed
 * and cannot be created, edited, or deleted by users.
 *
 * @returns {Object} Hook interface containing:
 *   - tags: Array of available pharmaceutical tags
 *   - isLoading: Loading state for tag operations
 *   - addTagToMedicine: Function to associate a tag with a medicine
 *   - removeTagFromMedicine: Function to remove a tag from a medicine
 *   - refetch: Function to refresh the tags list
 */
export const useTags = () => {
  const [tags, setTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user, householdId } = useAuth();

  const fetchTags = async () => {
    if (!householdId) return;

    try {
      setIsLoading(true);

      // Try using the RPC function first
      const { data: rpcData, error: rpcError } = await supabase
        .rpc('get_household_tags', { p_household_id: householdId });

      if (rpcError) {
        // Fallback to direct query if RPC function doesn't exist

        // Fallback to direct query if RPC function doesn't exist
        const { data: directData, error: directError } = await supabase
          .from('tags')
          .select(`
            id,
            name,
            color,
            is_system_tag,
            category,
            medicine_tags(count)
          `)
          .eq('household_id', householdId)
          .order('is_system_tag', { ascending: false })
          .order('name', { ascending: true });

        if (directError) throw directError;

        if (directData) {
          const formattedTags: Tag[] = directData.map((item: any) => ({
            id: item.id,
            name: item.name,
            color: item.color,
            isSystemTag: item.is_system_tag,
            category: item.category || 'therapeutic',
            medicineCount: item.medicine_tags?.length || 0
          }));

          setTags(formattedTags);
        }
      } else if (rpcData) {
        const formattedTags: Tag[] = rpcData.map((item: any) => ({
          id: item.id,
          name: item.name,
          color: item.color,
          isSystemTag: item.is_system_tag,
          category: item.category || 'therapeutic',
          medicineCount: item.medicine_count
        }));

        setTags(formattedTags);
      }
    } catch (err) {
      // Don't show error toast if tables don't exist yet
      if (!(err as any)?.code?.includes('42P01')) {
        toast.error('Erreur lors du chargement des étiquettes');
      }
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Associates a pharmaceutical tag with a medicine
   *
   * @param medicineId - The ID of the medicine to tag
   * @param tagId - The ID of the pharmaceutical tag to associate
   * @returns Promise<boolean> - Success status of the operation
   */

  const addTagToMedicine = async (medicineId: string, tagId: string) => {
    try {
      const { data, error } = await supabase
        .rpc('add_tag_to_medicine', {
          p_user_medicine_id: medicineId,
          p_tag_id: tagId
        });
        
      if (error) throw error;
      
      if (data) {
        // Update medicine count for the tag
        setTags(prev => prev.map(tag => 
          tag.id === tagId 
            ? { ...tag, medicineCount: (tag.medicineCount || 0) + 1 }
            : tag
        ));
        return true;
      }
      return false;
    } catch (err) {
      toast.error('Erreur lors de l\'ajout de l\'étiquette au médicament');
      return false;
    }
  };

  /**
   * Removes a pharmaceutical tag association from a medicine
   *
   * @param medicineId - The ID of the medicine to untag
   * @param tagId - The ID of the pharmaceutical tag to remove
   * @returns Promise<boolean> - Success status of the operation
   */
  const removeTagFromMedicine = async (medicineId: string, tagId: string) => {
    try {
      const { error } = await supabase
        .from('medicine_tags')
        .delete()
        .eq('user_medicine_id', medicineId)
        .eq('tag_id', tagId);
        
      if (error) throw error;
      
      // Update medicine count for the tag
      setTags(prev => prev.map(tag => 
        tag.id === tagId 
          ? { ...tag, medicineCount: Math.max((tag.medicineCount || 1) - 1, 0) }
          : tag
      ));
      
      return true;
    } catch (err) {
      toast.error('Erreur lors de la suppression de l\'étiquette du médicament');
      return false;
    }
  };

  useEffect(() => {
    fetchTags();
  }, [householdId]);

  return {
    tags,
    isLoading,
    addTagToMedicine,
    removeTagFromMedicine,
    refetch: fetchTags
  };
};
