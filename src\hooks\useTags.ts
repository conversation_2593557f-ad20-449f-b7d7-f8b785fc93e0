import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Tag } from '@/types';
import { toast } from 'sonner';

export const useTags = () => {
  const [tags, setTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user, householdId } = useAuth();

  const fetchTags = async () => {
    if (!householdId) return;

    try {
      setIsLoading(true);

      // Try using the RPC function first
      const { data: rpcData, error: rpcError } = await supabase
        .rpc('get_household_tags', { p_household_id: householdId });

      if (rpcError) {
        console.warn('RPC function not available, falling back to direct query:', rpcError);

        // Fallback to direct query if RPC function doesn't exist
        const { data: directData, error: directError } = await supabase
          .from('tags')
          .select(`
            id,
            name,
            color,
            is_system_tag,
            medicine_tags(count)
          `)
          .eq('household_id', householdId)
          .order('is_system_tag', { ascending: false })
          .order('name', { ascending: true });

        if (directError) throw directError;

        if (directData) {
          const formattedTags: Tag[] = directData.map((item: any) => ({
            id: item.id,
            name: item.name,
            color: item.color,
            isSystemTag: item.is_system_tag,
            medicineCount: item.medicine_tags?.length || 0
          }));

          setTags(formattedTags);
        }
      } else if (rpcData) {
        const formattedTags: Tag[] = rpcData.map((item: any) => ({
          id: item.id,
          name: item.name,
          color: item.color,
          isSystemTag: item.is_system_tag,
          medicineCount: item.medicine_count
        }));

        setTags(formattedTags);
      }
    } catch (err) {
      console.error('Error fetching tags:', err);
      // Don't show error toast if tables don't exist yet
      if (!(err as any)?.code?.includes('42P01')) {
        toast.error('Erreur lors du chargement des étiquettes');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const createTag = async (name: string, color: string = '#0DCDB7') => {
    if (!householdId) return null;
    
    try {
      const { data, error } = await supabase
        .from('tags')
        .insert({
          name,
          color,
          household_id: householdId,
          is_system_tag: false
        })
        .select()
        .single();
        
      if (error) throw error;
      
      const newTag: Tag = {
        id: data.id,
        name: data.name,
        color: data.color,
        isSystemTag: data.is_system_tag,
        medicineCount: 0
      };
      
      setTags(prev => [...prev, newTag]);
      toast.success('Étiquette créée avec succès');
      return newTag;
    } catch (err: any) {
      console.error('Error creating tag:', err);
      if (err.code === '23505') {
        toast.error('Une étiquette avec ce nom existe déjà');
      } else {
        toast.error('Erreur lors de la création de l\'étiquette');
      }
      return null;
    }
  };

  const updateTag = async (tagId: string, name: string, color: string) => {
    try {
      const { error } = await supabase
        .from('tags')
        .update({ name, color, updated_at: new Date().toISOString() })
        .eq('id', tagId);
        
      if (error) throw error;
      
      setTags(prev => prev.map(tag => 
        tag.id === tagId 
          ? { ...tag, name, color }
          : tag
      ));
      
      toast.success('Étiquette mise à jour avec succès');
      return true;
    } catch (err: any) {
      console.error('Error updating tag:', err);
      if (err.code === '23505') {
        toast.error('Une étiquette avec ce nom existe déjà');
      } else {
        toast.error('Erreur lors de la mise à jour de l\'étiquette');
      }
      return false;
    }
  };

  const deleteTag = async (tagId: string) => {
    try {
      const { error } = await supabase
        .from('tags')
        .delete()
        .eq('id', tagId);
        
      if (error) throw error;
      
      setTags(prev => prev.filter(tag => tag.id !== tagId));
      toast.success('Étiquette supprimée avec succès');
      return true;
    } catch (err) {
      console.error('Error deleting tag:', err);
      toast.error('Erreur lors de la suppression de l\'étiquette');
      return false;
    }
  };

  const addTagToMedicine = async (medicineId: string, tagId: string) => {
    try {
      const { data, error } = await supabase
        .rpc('add_tag_to_medicine', {
          p_user_medicine_id: medicineId,
          p_tag_id: tagId
        });
        
      if (error) throw error;
      
      if (data) {
        // Update medicine count for the tag
        setTags(prev => prev.map(tag => 
          tag.id === tagId 
            ? { ...tag, medicineCount: (tag.medicineCount || 0) + 1 }
            : tag
        ));
        return true;
      }
      return false;
    } catch (err) {
      console.error('Error adding tag to medicine:', err);
      toast.error('Erreur lors de l\'ajout de l\'étiquette au médicament');
      return false;
    }
  };

  const removeTagFromMedicine = async (medicineId: string, tagId: string) => {
    try {
      const { error } = await supabase
        .from('medicine_tags')
        .delete()
        .eq('user_medicine_id', medicineId)
        .eq('tag_id', tagId);
        
      if (error) throw error;
      
      // Update medicine count for the tag
      setTags(prev => prev.map(tag => 
        tag.id === tagId 
          ? { ...tag, medicineCount: Math.max((tag.medicineCount || 1) - 1, 0) }
          : tag
      ));
      
      return true;
    } catch (err) {
      console.error('Error removing tag from medicine:', err);
      toast.error('Erreur lors de la suppression de l\'étiquette du médicament');
      return false;
    }
  };

  useEffect(() => {
    fetchTags();
  }, [householdId]);

  return {
    tags,
    isLoading,
    createTag,
    updateTag,
    deleteTag,
    addTagToMedicine,
    removeTagFromMedicine,
    refetch: fetchTags
  };
};
