import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Tag } from '@/types';
import { toast } from 'sonner';

export const useTags = () => {
  const [tags, setTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user, householdId } = useAuth();

  const fetchTags = async () => {
    if (!householdId) return;

    try {
      setIsLoading(true);

      // Try using the RPC function first
      const { data: rpcData, error: rpcError } = await supabase
        .rpc('get_household_tags', { p_household_id: householdId });

      if (rpcError) {
        console.warn('RPC function not available, falling back to direct query:', rpcError);

        // Fallback to direct query if RPC function doesn't exist
        const { data: directData, error: directError } = await supabase
          .from('tags')
          .select(`
            id,
            name,
            color,
            is_system_tag,
            category,
            medicine_tags(count)
          `)
          .eq('household_id', householdId)
          .order('is_system_tag', { ascending: false })
          .order('name', { ascending: true });

        if (directError) throw directError;

        if (directData) {
          const formattedTags: Tag[] = directData.map((item: any) => ({
            id: item.id,
            name: item.name,
            color: item.color,
            isSystemTag: item.is_system_tag,
            category: item.category || 'therapeutic',
            medicineCount: item.medicine_tags?.length || 0
          }));

          setTags(formattedTags);
        }
      } else if (rpcData) {
        const formattedTags: Tag[] = rpcData.map((item: any) => ({
          id: item.id,
          name: item.name,
          color: item.color,
          isSystemTag: item.is_system_tag,
          category: item.category || 'therapeutic',
          medicineCount: item.medicine_count
        }));

        setTags(formattedTags);
      }
    } catch (err) {
      console.error('Error fetching tags:', err);
      // Don't show error toast if tables don't exist yet
      if (!(err as any)?.code?.includes('42P01')) {
        toast.error('Erreur lors du chargement des étiquettes');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Note: Custom tag creation, editing, and deletion removed
  // All tags are now predefined pharmaceutical tags

  const addTagToMedicine = async (medicineId: string, tagId: string) => {
    try {
      const { data, error } = await supabase
        .rpc('add_tag_to_medicine', {
          p_user_medicine_id: medicineId,
          p_tag_id: tagId
        });
        
      if (error) throw error;
      
      if (data) {
        // Update medicine count for the tag
        setTags(prev => prev.map(tag => 
          tag.id === tagId 
            ? { ...tag, medicineCount: (tag.medicineCount || 0) + 1 }
            : tag
        ));
        return true;
      }
      return false;
    } catch (err) {
      console.error('Error adding tag to medicine:', err);
      toast.error('Erreur lors de l\'ajout de l\'étiquette au médicament');
      return false;
    }
  };

  const removeTagFromMedicine = async (medicineId: string, tagId: string) => {
    try {
      const { error } = await supabase
        .from('medicine_tags')
        .delete()
        .eq('user_medicine_id', medicineId)
        .eq('tag_id', tagId);
        
      if (error) throw error;
      
      // Update medicine count for the tag
      setTags(prev => prev.map(tag => 
        tag.id === tagId 
          ? { ...tag, medicineCount: Math.max((tag.medicineCount || 1) - 1, 0) }
          : tag
      ));
      
      return true;
    } catch (err) {
      console.error('Error removing tag from medicine:', err);
      toast.error('Erreur lors de la suppression de l\'étiquette du médicament');
      return false;
    }
  };

  useEffect(() => {
    fetchTags();
  }, [householdId]);

  return {
    tags,
    isLoading,
    addTagToMedicine,
    removeTagFromMedicine,
    refetch: fetchTags
  };
};
