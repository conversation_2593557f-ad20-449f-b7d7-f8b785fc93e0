import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import MedicineSorting from '@/components/medicines/MedicineSorting';
import { ViewMode, SortOption } from '@/components/medicines/MedicineSorting';

describe('View Mode Integration', () => {
  const mockOnSortChange = vi.fn();
  const mockOnViewModeChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render view mode toggle with grouped as default', () => {
    render(
      <MedicineSorting
        sortBy="name-asc"
        onSortChange={mockOnSortChange}
        totalCount={25}
        viewMode="grouped"
        onViewModeChange={mockOnViewModeChange}
        groupedCount={10}
        showViewToggle={true}
      />
    );

    expect(screen.getByText('Affichage:')).toBeInTheDocument();
    expect(screen.getByText('Groupé')).toBeInTheDocument();
    expect(screen.getByText('Individuel')).toBeInTheDocument();
    
    // Should show grouped count format
    expect(screen.getByText('10')).toBeInTheDocument();
    expect(screen.getByText(/groupe/)).toBeInTheDocument();
    expect(screen.getByText(/25 médicaments/)).toBeInTheDocument();
  });

  it('should switch to individual view when clicked', () => {
    render(
      <MedicineSorting
        sortBy="name-asc"
        onSortChange={mockOnSortChange}
        totalCount={25}
        viewMode="grouped"
        onViewModeChange={mockOnViewModeChange}
        groupedCount={10}
        showViewToggle={true}
      />
    );

    const individualButton = screen.getByText('Individuel');
    fireEvent.click(individualButton);

    expect(mockOnViewModeChange).toHaveBeenCalledWith('individual');
  });

  it('should switch to grouped view when clicked', () => {
    render(
      <MedicineSorting
        sortBy="name-asc"
        onSortChange={mockOnSortChange}
        totalCount={25}
        viewMode="individual"
        onViewModeChange={mockOnViewModeChange}
        groupedCount={10}
        showViewToggle={true}
      />
    );

    const groupedButton = screen.getByText('Groupé');
    fireEvent.click(groupedButton);

    expect(mockOnViewModeChange).toHaveBeenCalledWith('grouped');
  });

  it('should show individual count format when in individual mode', () => {
    render(
      <MedicineSorting
        sortBy="name-asc"
        onSortChange={mockOnSortChange}
        totalCount={25}
        viewMode="individual"
        onViewModeChange={mockOnViewModeChange}
        showViewToggle={true}
      />
    );

    expect(screen.getByText('25')).toBeInTheDocument();
    expect(screen.getByText(/médicaments trouvés/)).toBeInTheDocument();
  });

  it('should hide view toggle when showViewToggle is false', () => {
    render(
      <MedicineSorting
        sortBy="name-asc"
        onSortChange={mockOnSortChange}
        totalCount={25}
        viewMode="grouped"
        onViewModeChange={mockOnViewModeChange}
        groupedCount={10}
        showViewToggle={false}
      />
    );

    expect(screen.queryByText('Affichage:')).not.toBeInTheDocument();
    expect(screen.queryByText('Groupé')).not.toBeInTheDocument();
    expect(screen.queryByText('Individuel')).not.toBeInTheDocument();
  });

  it('should handle sorting changes correctly', () => {
    render(
      <MedicineSorting
        sortBy="name-asc"
        onSortChange={mockOnSortChange}
        totalCount={25}
        viewMode="grouped"
        onViewModeChange={mockOnViewModeChange}
        groupedCount={10}
        showViewToggle={true}
      />
    );

    // Find and click the sort dropdown
    const sortTrigger = screen.getByRole('combobox');
    fireEvent.click(sortTrigger);

    // Select a different sort option
    const expiryOption = screen.getByText('Expiration (plus proche)');
    fireEvent.click(expiryOption);

    expect(mockOnSortChange).toHaveBeenCalledWith('expiry-asc');
  });

  it('should apply correct styling to active view mode button', () => {
    render(
      <MedicineSorting
        sortBy="name-asc"
        onSortChange={mockOnSortChange}
        totalCount={25}
        viewMode="grouped"
        onViewModeChange={mockOnViewModeChange}
        groupedCount={10}
        showViewToggle={true}
      />
    );

    const groupedButton = screen.getByText('Groupé');
    const individualButton = screen.getByText('Individuel');

    // Grouped button should have active styling (teal background)
    expect(groupedButton.closest('button')).toHaveClass('bg-teal');
    expect(groupedButton.closest('button')).toHaveClass('text-white');

    // Individual button should have inactive styling
    expect(individualButton.closest('button')).toHaveClass('text-gray-600');
  });

  it('should handle plural forms correctly', () => {
    // Test singular forms
    render(
      <MedicineSorting
        sortBy="name-asc"
        onSortChange={mockOnSortChange}
        totalCount={1}
        viewMode="grouped"
        onViewModeChange={mockOnViewModeChange}
        groupedCount={1}
        showViewToggle={true}
      />
    );

    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText(/groupe/)).toBeInTheDocument();
    expect(screen.getByText(/1 médicament/)).toBeInTheDocument();
  });
});
