
import React from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { 
  Plus, 
  Users, 
  MapPin, 
  LayoutDashboard, 
  Settings 
} from "lucide-react";

const ActionGrid = () => {
  const navigate = useNavigate();
  
  const actions = [
    {
      icon: Plus,
      label: "Ajouter un médicament",
      route: "/add-medicine",
      color: "text-teal"
    },
    {
      icon: Users,
      label: "Gérer ma famille",
      route: "/family",
      color: "text-navy"
    },
    {
      icon: MapPin,
      label: "Mes emplacements",
      route: "/locations",
      color: "text-teal-dark"
    },
    {
      icon: LayoutDashboard,
      label: "Tableau de bord",
      route: "/dashboard",
      color: "text-navy-light"
    },
    {
      icon: Settings,
      label: "Paramètres",
      route: "/settings",
      color: "text-gray-500"
    }
  ];

  return (
    <div className="grid grid-cols-2 gap-4">
      {actions.map((action) => (
        <Card 
          key={action.route}
          className="cursor-pointer hover:bg-gray-50 transition-colors"
          onClick={() => navigate(action.route)}
        >
          <CardContent className="p-4 flex flex-col items-center text-center">
            <action.icon className={`h-8 w-8 mb-2 ${action.color}`} />
            <span className="text-sm">{action.label}</span>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default ActionGrid;
