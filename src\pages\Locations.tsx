
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Plus, ArrowLeft } from "lucide-react";
import { useLocations } from "@/hooks/useLocations";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import LocationCard from "@/components/locations/LocationCard";
import LocationDialog from "@/components/locations/LocationDialog";
import DeleteConfirmDialog from "@/components/dialogs/DeleteConfirmDialog";
import ModernHeader from "@/components/ModernHeader";

const Locations = () => {
  const navigate = useNavigate();
  const { locations, isLoading, addLocation, updateLocation, deleteLocation } = useLocations();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [selectedLocation, setSelectedLocation] = useState<{ id: string, name: string, description?: string } | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleAdd = async () => {
    if (!name.trim()) {
      toast.error('Veuillez saisir un nom');
      return false;
    }

    try {
      console.log("Adding location with name:", name, "and description:", description);
      const result = await addLocation(name, description);
      if (result) {
        setName("");
        setDescription("");
        return true;
      }
      return false;
    } catch (error) {
      console.error("Error in handleAdd:", error);
      toast.error('Erreur lors de l\'ajout de l\'emplacement');
      return false;
    }
  };

  const handleEdit = async () => {
    if (!selectedLocation || !name.trim()) {
      toast.error('Veuillez saisir un nom');
      return false;
    }

    try {
      const result = await updateLocation(selectedLocation.id, { name, description });
      if (result) {
        setName("");
        setDescription("");
        setSelectedLocation(null);
        return true;
      }
      return false;
    } catch (error) {
      console.error("Error in handleEdit:", error);
      toast.error('Erreur lors de la mise à jour de l\'emplacement');
      return false;
    }
  };

  const handleDelete = async () => {
    if (!selectedLocation) return false;
    
    setIsDeleting(true);
    try {
      const result = await deleteLocation(selectedLocation.id);
      if (result) {
        setSelectedLocation(null);
        return true;
      }
      return false;
    } catch (error) {
      console.error("Error in handleDelete:", error);
      toast.error('Erreur lors de la suppression de l\'emplacement');
      return false;
    } finally {
      setIsDeleting(false);
    }
  };

  const openEditDialog = (location: { id: string, name: string, description?: string }) => {
    setSelectedLocation(location);
    setName(location.name);
    setDescription(location.description || "");
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (location: { id: string, name: string }) => {
    setSelectedLocation(location);
    setIsDeleteDialogOpen(true);
  };

  return (
    <div className="medicinet-container animate-fade-in pb-20">
      <ModernHeader
        title="Gestion des emplacements"
        subtitle="Organisez vos médicaments par emplacement"
        showBackButton={true}
        variant="navy"
      />

      {isLoading ? (
        <div className="flex justify-center py-12">
          <Loader2 className="animate-spin h-8 w-8 text-teal" />
        </div>
      ) : (
        <>
          <div className="space-y-4 mb-6">
            {locations.length > 0 ? (
              locations.map((location) => (
                <LocationCard 
                  key={location.id}
                  location={location}
                  onEdit={openEditDialog}
                  onDelete={openDeleteDialog}
                />
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                Aucun emplacement trouvé. Ajoutez-en un pour commencer.
              </div>
            )}

            <Button
              className="w-full py-2 mt-4 bg-teal hover:bg-teal-dark text-white"
              onClick={() => {
                setName("");
                setDescription("");
                setIsDialogOpen(true);
              }}
            >
              <Plus size={18} className="mr-2" />
              Ajouter un emplacement
            </Button>
          </div>

          {/* Add Location Dialog */}
          <LocationDialog
            isOpen={isDialogOpen}
            onOpenChange={setIsDialogOpen}
            title="Ajouter un emplacement"
            name={name}
            description={description}
            setName={setName}
            setDescription={setDescription}
            confirmText="Ajouter"
            onConfirm={handleAdd}
          />

          {/* Edit Location Dialog */}
          <LocationDialog
            isOpen={isEditDialogOpen}
            onOpenChange={setIsEditDialogOpen}
            title="Modifier l'emplacement"
            name={name}
            description={description}
            setName={setName}
            setDescription={setDescription}
            confirmText="Enregistrer"
            onConfirm={handleEdit}
          />

          {/* Delete Confirmation Dialog */}
          <DeleteConfirmDialog
            isOpen={isDeleteDialogOpen}
            onOpenChange={setIsDeleteDialogOpen}
            title="Confirmer la suppression"
            description={`Êtes-vous sûr de vouloir supprimer l'emplacement ${selectedLocation?.name} ?`}
            onConfirm={handleDelete}
            isDeleting={isDeleting}
          />
        </>
      )}
    </div>
  );
};

export default Locations;
