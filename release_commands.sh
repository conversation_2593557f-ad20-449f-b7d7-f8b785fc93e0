# 1. Make sure all changes are committed
git status
git add .
git commit -m "🚀 Prepare for v1.0.0 stable release"

# 2. Update version in package.json to 1.0.0
# (Edit package.json manually or use the command below)
npm version 1.0.0 --no-git-tag-version

# 3. Create an annotated tag for v1.0.0
git tag -a v1.0.0 -m "MédiCabinet v1.0.0 - First stable release

🎉 Major Features:
- Complete medicine management system
- Family member profiles
- Location tracking
- Medicine expiry alerts
- Optimized UI/UX
- Performance improvements"

# 4. Push changes and tag to GitHub
git push origin main
git push origin v1.0.0