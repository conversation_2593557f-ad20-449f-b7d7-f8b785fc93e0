-- Fix infinite recursion in household RLS policies
-- This script fixes the circular dependency in the household_members RLS policies

-- Drop existing problematic policies
DROP POLICY IF EXISTS "Users can view members of their household" ON household_members;
DROP POLICY IF EXISTS "Only owners and admins can manage members" ON household_members;
DROP POLICY IF EXISTS "Users can view invitations for their household" ON household_invitations;
DROP POLICY IF EXISTS "Users can create invitations for their household" ON household_invitations;

-- Create fixed RLS policies for household_members
-- Use the users table household_id instead of self-referencing household_members
CREATE POLICY "Users can view members of their household" ON household_members
  FOR SELECT USING (
    household_id = (SELECT household_id FROM users WHERE id = auth.uid())
  );

CREATE POLICY "Users can insert themselves as members" ON household_members
  FOR INSERT WITH CHECK (
    user_id = auth.uid()
  );

CREATE POLICY "Only owners and admins can manage other members" ON household_members
  FOR ALL USING (
    -- Allow users to see their own record
    user_id = auth.uid()
    OR
    -- Allow owners and admins to manage others
    EXISTS (
      SELECT 1 FROM household_members hm
      WHERE hm.household_id = household_members.household_id
        AND hm.user_id = auth.uid()
        AND hm.role IN ('owner', 'admin')
    )
  );

-- Create fixed RLS policies for household_invitations
-- Use the users table household_id instead of household_members
CREATE POLICY "Users can view invitations for their household" ON household_invitations
  FOR SELECT USING (
    household_id = (SELECT household_id FROM users WHERE id = auth.uid())
  );

CREATE POLICY "Users can create invitations for their household" ON household_invitations
  FOR INSERT WITH CHECK (
    household_id = (SELECT household_id FROM users WHERE id = auth.uid())
    AND
    -- Check if user has permission to invite others
    EXISTS (
      SELECT 1 FROM household_members hm
      WHERE hm.household_id = household_invitations.household_id
        AND hm.user_id = auth.uid()
        AND (hm.permissions->>'can_invite_others')::boolean = true
    )
  );

CREATE POLICY "Users can update invitations for their household" ON household_invitations
  FOR UPDATE USING (
    household_id = (SELECT household_id FROM users WHERE id = auth.uid())
    AND
    -- Check if user has permission to manage invitations
    EXISTS (
      SELECT 1 FROM household_members hm
      WHERE hm.household_id = household_invitations.household_id
        AND hm.user_id = auth.uid()
        AND hm.role IN ('owner', 'admin')
    )
  );

-- Add policy for deleting invitations
CREATE POLICY "Users can delete invitations for their household" ON household_invitations
  FOR DELETE USING (
    household_id = (SELECT household_id FROM users WHERE id = auth.uid())
    AND
    -- Check if user has permission to manage invitations
    EXISTS (
      SELECT 1 FROM household_members hm
      WHERE hm.household_id = household_invitations.household_id
        AND hm.user_id = auth.uid()
        AND hm.role IN ('owner', 'admin')
    )
  );

-- Ensure all users have proper household_members entries
-- This function will be called to ensure data consistency
CREATE OR REPLACE FUNCTION ensure_household_member_exists()
RETURNS TRIGGER AS $$
BEGIN
  -- When a user's household_id is updated, ensure they have a household_members entry
  IF NEW.household_id IS NOT NULL AND NEW.household_id != OLD.household_id THEN
    INSERT INTO household_members (
      household_id,
      user_id,
      role,
      permissions
    ) VALUES (
      NEW.household_id,
      NEW.id,
      'member',
      '{"can_add_medicines": true, "can_edit_medicines": true, "can_delete_medicines": false, "can_manage_family": false, "can_invite_others": false}'::jsonb
    ) ON CONFLICT (household_id, user_id) DO NOTHING;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to ensure household_members consistency
DROP TRIGGER IF EXISTS trigger_ensure_household_member ON users;
CREATE TRIGGER trigger_ensure_household_member
  AFTER UPDATE OF household_id ON users
  FOR EACH ROW
  EXECUTE FUNCTION ensure_household_member_exists();

-- Fix any existing users who might not have household_members entries
INSERT INTO household_members (household_id, user_id, role, permissions)
SELECT 
  u.household_id,
  u.id,
  CASE 
    WHEN h.created_by = u.id THEN 'owner'
    ELSE 'member'
  END as role,
  CASE 
    WHEN h.created_by = u.id THEN '{"can_add_medicines": true, "can_edit_medicines": true, "can_delete_medicines": true, "can_manage_family": true, "can_invite_others": true}'::jsonb
    ELSE '{"can_add_medicines": true, "can_edit_medicines": true, "can_delete_medicines": false, "can_manage_family": false, "can_invite_others": false}'::jsonb
  END as permissions
FROM users u
JOIN households h ON u.household_id = h.id
WHERE u.household_id IS NOT NULL
ON CONFLICT (household_id, user_id) DO UPDATE SET
  role = EXCLUDED.role,
  permissions = EXCLUDED.permissions;
