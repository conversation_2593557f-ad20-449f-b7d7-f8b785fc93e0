
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import SearchBar from "@/components/SearchBar";
import ModernHeader from "@/components/ModernHeader";
import BottomNavigation from "@/components/BottomNavigation";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

const Search = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const navigate = useNavigate();

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
  };

  const handleSearch = () => {
    if (searchQuery.trim().length >= 2) {
      // Redirect to specialty search with the query
      navigate(`/specialty-search?q=${encodeURIComponent(searchQuery)}`);
    } else if (searchQuery.trim().length > 0) {
      toast.warning("Veuillez saisir au moins 2 caractères");
    }
  };

  return (
    <div className="medicinet-container animate-fade-in">
      <ModernHeader
        title="Recherche de Médicaments"
        subtitle="Trouvez des informations sur les médicaments"
        variant="navy"
      />
      
      <div className="mb-6">
        <SearchBar 
          value={searchQuery}
          onChange={handleSearchChange}
          onSearch={handleSearch}
          placeholder="Rechercher par nom, code CIS ou titulaire..."
        />
        <div className="flex justify-between items-center mt-2">
          <p className="text-xs text-muted-foreground">
            Tapez au moins 2 caractères pour lancer une recherche
          </p>
        </div>
      </div>
      
      <div className="text-center py-16 text-muted-foreground">
        <p>Recherchez un médicament pour voir les résultats</p>
      </div>
      
      <BottomNavigation />
    </div>
  );
};

export default Search;
