import 'package:equatable/equatable.dart';

/// Family member entity representing a person in the household
class FamilyMember extends Equatable {
  final String id;
  final String name;
  final String? relationship;
  final DateTime? birthDate;
  final String? notes;
  final String? avatarUrl;
  final String householdId;
  final DateTime createdAt;
  final DateTime updatedAt;

  const FamilyMember({
    required this.id,
    required this.name,
    this.relationship,
    this.birthDate,
    this.notes,
    this.avatarUrl,
    required this.householdId,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Get age if birth date is available
  int? get age {
    if (birthDate == null) return null;
    final now = DateTime.now();
    int age = now.year - birthDate!.year;
    if (now.month < birthDate!.month || 
        (now.month == birthDate!.month && now.day < birthDate!.day)) {
      age--;
    }
    return age;
  }

  /// Get initials for avatar
  String get initials {
    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
    }
    return name[0].toUpperCase();
  }

  /// Get display name with relationship if available
  String get displayNameWithRelationship {
    if (relationship != null && relationship!.isNotEmpty) {
      return '$name ($relationship)';
    }
    return name;
  }

  /// Get age display string
  String get ageDisplay {
    if (age == null) return '';
    return '$age ans';
  }

  /// Check if family member is a child (under 18)
  bool get isChild {
    return age != null && age! < 18;
  }

  /// Check if family member is an adult (18 or over)
  bool get isAdult {
    return age != null && age! >= 18;
  }

  /// Copy with method for immutable updates
  FamilyMember copyWith({
    String? id,
    String? name,
    String? relationship,
    DateTime? birthDate,
    String? notes,
    String? avatarUrl,
    String? householdId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FamilyMember(
      id: id ?? this.id,
      name: name ?? this.name,
      relationship: relationship ?? this.relationship,
      birthDate: birthDate ?? this.birthDate,
      notes: notes ?? this.notes,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      householdId: householdId ?? this.householdId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        relationship,
        birthDate,
        notes,
        avatarUrl,
        householdId,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'FamilyMember(id: $id, name: $name, relationship: $relationship, age: $age)';
  }
}

/// Predefined relationship types
class RelationshipTypes {
  static const List<String> relationships = [
    'Conjoint(e)',
    'Enfant',
    'Parent',
    'Frère/Sœur',
    'Grand-parent',
    'Petit-enfant',
    'Oncle/Tante',
    'Neveu/Nièce',
    'Cousin(e)',
    'Ami(e)',
    'Autre',
  ];

  /// Get relationship display name
  static String getDisplayName(String relationship) {
    return relationship;
  }

  /// Check if relationship is valid
  static bool isValidRelationship(String relationship) {
    return relationships.contains(relationship);
  }

  /// Get default relationship
  static String get defaultRelationship => 'Autre';
}
