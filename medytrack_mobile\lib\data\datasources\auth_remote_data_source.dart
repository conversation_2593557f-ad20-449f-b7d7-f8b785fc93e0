import 'package:supabase_flutter/supabase_flutter.dart';
import '../../core/storage/secure_storage_service.dart';
import '../../domain/entities/user.dart';
import '../../domain/usecases/auth/update_profile_usecase.dart';
import '../models/user_model.dart';

abstract class AuthRemoteDataSource {
  Future<UserModel> signIn(String email, String password);
  Future<UserModel> signUp(String email, String password, String name);
  Future<void> signOut();
  Future<UserModel?> getCurrentUser();
  Future<void> resetPassword(String email);
  Future<UserModel> updateProfile(UpdateProfileParams params);
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final SupabaseClient supabaseClient;
  final SecureStorageService secureStorage;

  AuthRemoteDataSourceImpl({
    required this.supabaseClient,
    required this.secureStorage,
  });

  @override
  Future<UserModel> signIn(String email, String password) async {
    try {
      final response = await supabaseClient.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw Exception('Authentication failed');
      }

      // Store session
      await secureStorage.storeToken(response.session?.accessToken ?? '');

      return UserModel.fromSupabaseUser(response.user!);
    } catch (e) {
      throw Exception('Sign in failed: ${e.toString()}');
    }
  }

  @override
  Future<UserModel> signUp(String email, String password, String name) async {
    try {
      final response = await supabaseClient.auth.signUp(
        email: email,
        password: password,
        data: {'name': name},
      );

      if (response.user == null) {
        throw Exception('Sign up failed');
      }

      return UserModel.fromSupabaseUser(response.user!);
    } catch (e) {
      throw Exception('Sign up failed: ${e.toString()}');
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await supabaseClient.auth.signOut();
      await secureStorage.deleteToken();
    } catch (e) {
      throw Exception('Sign out failed: ${e.toString()}');
    }
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) return null;

      print('🔍 AuthRemoteDataSource: Getting current user - ${user.email}');

      // First, get the basic user data from auth
      var userModel = UserModel.fromSupabaseUser(user);

      // Then, fetch additional data from BOTH profiles and users tables (like web app does)
      try {
        print(
          '🔍 AuthRemoteDataSource: Fetching profile data from profiles table...',
        );

        // Step 1: Fetch from profiles table (like web app)
        final profileResponse = await supabaseClient
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .maybeSingle();

        print(
          '📊 AuthRemoteDataSource: Profiles table response - $profileResponse',
        );

        // Step 2: Fetch from users table for household_id (like web app)
        print(
          '🔍 AuthRemoteDataSource: Fetching user data from users table...',
        );
        final userResponse = await supabaseClient
            .from('users')
            .select('household_id')
            .eq('id', user.id)
            .maybeSingle();

        print('📊 AuthRemoteDataSource: Users table response - $userResponse');

        // Step 3: Combine data from both tables
        String? householdName;
        String? householdId = userResponse?['household_id'] as String?;
        bool isOnboardingCompleted = false;

        // Get onboarding status from profiles table (like web app)
        if (profileResponse != null) {
          isOnboardingCompleted =
              (profileResponse['onboarding_completed'] as bool?) ?? false;
          print(
            '📊 AuthRemoteDataSource: Profile onboarding_completed - $isOnboardingCompleted',
          );
        }

        // If user has household_id, fetch household name
        if (householdId != null) {
          print(
            '🏠 AuthRemoteDataSource: Fetching household name for $householdId...',
          );
          final householdResponse = await supabaseClient
              .from('households')
              .select('name')
              .eq('id', householdId)
              .maybeSingle();

          householdName = householdResponse?['name'] as String?;
          print('🏠 AuthRemoteDataSource: Household name - $householdName');
        }

        // Update user model with database data from both tables
        userModel = userModel.copyWith(
          householdId: householdId,
          householdName: householdName,
          isOnboardingCompleted: isOnboardingCompleted,
        );

        print('✅ AuthRemoteDataSource: Updated user model with database data');
        print(
          '🏠 AuthRemoteDataSource: Final householdId - ${userModel.householdId}',
        );
        print(
          '🏠 AuthRemoteDataSource: Final householdName - ${userModel.householdName}',
        );
        print(
          '✅ AuthRemoteDataSource: Final isOnboardingCompleted - ${userModel.isOnboardingCompleted}',
        );
      } catch (e) {
        print(
          '❌ AuthRemoteDataSource: Error fetching from database tables - $e',
        );
        // Continue with userMetadata-only data if database fetch fails
      }

      return userModel;
    } catch (e) {
      print('❌ AuthRemoteDataSource: Error getting current user - $e');
      return null;
    }
  }

  @override
  Future<void> resetPassword(String email) async {
    try {
      await supabaseClient.auth.resetPasswordForEmail(email);
    } catch (e) {
      throw Exception('Password reset failed: ${e.toString()}');
    }
  }

  @override
  Future<UserModel> updateProfile(UpdateProfileParams params) async {
    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        throw Exception('No authenticated user');
      }

      print('🔄 AuthRemoteDataSource: Updating profile for ${user.email}...');
      print(
        '📊 AuthRemoteDataSource: Update params - name: ${params.name}, householdId: ${params.householdId}, householdName: ${params.householdName}, onboarding: ${params.isOnboardingCompleted}',
      );

      // Update userMetadata if needed
      Map<String, dynamic>? metadataUpdates;
      if (params.name != null) {
        metadataUpdates = {'name': params.name};

        print('🔄 AuthRemoteDataSource: Updating userMetadata...');
        await supabaseClient.auth.updateUser(
          UserAttributes(data: metadataUpdates),
        );
      }

      // Update users table (like web app does)
      Map<String, dynamic> userTableUpdates = {};

      if (params.householdId != null) {
        userTableUpdates['household_id'] = params.householdId;
      }

      if (params.isOnboardingCompleted != null) {
        userTableUpdates['onboarding_completed'] = params.isOnboardingCompleted;
      }

      if (userTableUpdates.isNotEmpty) {
        print('🔄 AuthRemoteDataSource: Updating users table...');

        // First, ensure user exists in users table
        final existingUser = await supabaseClient
            .from('users')
            .select('id')
            .eq('id', user.id)
            .maybeSingle();

        if (existingUser == null) {
          print(
            '🔄 AuthRemoteDataSource: Creating user entry in users table...',
          );
          await supabaseClient.from('users').insert({
            'id': user.id,
            ...userTableUpdates,
          });
        } else {
          print('🔄 AuthRemoteDataSource: Updating existing user entry...');
          await supabaseClient
              .from('users')
              .update(userTableUpdates)
              .eq('id', user.id);
        }
      }

      // If creating/joining a household, create default household if needed
      if (params.householdId != null && params.householdName != null) {
        print('🏠 AuthRemoteDataSource: Ensuring household exists...');

        final existingHousehold = await supabaseClient
            .from('households')
            .select('id')
            .eq('id', params.householdId!)
            .maybeSingle();

        if (existingHousehold == null) {
          print('🏠 AuthRemoteDataSource: Creating new household...');
          await supabaseClient.from('households').insert({
            'id': params.householdId,
            'name': params.householdName,
            'created_by': user.id,
          });
        }
      }

      print('✅ AuthRemoteDataSource: Profile update completed');

      // Return updated user
      return await getCurrentUser() ?? UserModel.fromSupabaseUser(user);
    } catch (e) {
      print('❌ AuthRemoteDataSource: Profile update failed - $e');
      throw Exception('Profile update failed: ${e.toString()}');
    }
  }
}
