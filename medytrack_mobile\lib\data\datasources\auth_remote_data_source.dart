import 'package:supabase_flutter/supabase_flutter.dart';
import '../../core/storage/secure_storage_service.dart';
import '../../domain/usecases/auth/update_profile_usecase.dart';
import '../models/user_model.dart';

abstract class AuthRemoteDataSource {
  Future<UserModel> signIn(String email, String password);
  Future<UserModel> signUp(String email, String password, String name);
  Future<void> signOut();
  Future<UserModel?> getCurrentUser();
  Future<void> resetPassword(String email);
  Future<UserModel> updateProfile(UpdateProfileParams params);
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final SupabaseClient supabaseClient;
  final SecureStorageService secureStorage;

  AuthRemoteDataSourceImpl({
    required this.supabaseClient,
    required this.secureStorage,
  });

  @override
  Future<UserModel> signIn(String email, String password) async {
    try {
      final response = await supabaseClient.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw Exception('Authentication failed');
      }

      // Store session
      await secureStorage.storeToken(response.session?.accessToken ?? '');

      // Get complete user data with database queries (like getCurrentUser does)
      final completeUser = await getCurrentUser();
      if (completeUser == null) {
        throw Exception('Failed to fetch complete user profile after sign in');
      }

      return completeUser;
    } catch (e) {
      throw Exception('Sign in failed: ${e.toString()}');
    }
  }

  @override
  Future<UserModel> signUp(String email, String password, String name) async {
    try {
      final response = await supabaseClient.auth.signUp(
        email: email,
        password: password,
        data: {'name': name},
      );

      if (response.user == null) {
        throw Exception('Sign up failed');
      }

      // Get complete user data with database queries (like getCurrentUser does)
      final completeUser = await getCurrentUser();
      if (completeUser == null) {
        throw Exception('Failed to fetch complete user profile after sign up');
      }

      return completeUser;
    } catch (e) {
      throw Exception('Sign up failed: ${e.toString()}');
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await supabaseClient.auth.signOut();
      await secureStorage.deleteToken();
    } catch (e) {
      throw Exception('Sign out failed: ${e.toString()}');
    }
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) return null;

      // Start with basic user data from auth
      var userModel = UserModel.fromSupabaseUser(user);

      // Mirror web app's fetchProfile pattern exactly
      try {
        // Step 1: Fetch profile data (like web app AuthContext.fetchProfile)
        final profileResponse = await supabaseClient
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();

        // Step 2: Fetch user data for household_id (like web app)
        final userResponse = await supabaseClient
            .from('users')
            .select('household_id')
            .eq('id', user.id)
            .single();

        // Step 3: Process onboarding status (like web app logic)
        bool isOnboardingCompleted = false;
        if (profileResponse != null) {
          final dbOnboardingCompleted = profileResponse['onboarding_completed'] as bool? ?? false;
          // Web app uses localStorage fallback, mobile app uses secure storage
          isOnboardingCompleted = dbOnboardingCompleted;
        }

        // Step 4: Fetch household data if exists (like web app)
        String? householdName;
        String? householdId = userResponse?['household_id'] as String?;
        
        if (householdId != null) {
          final householdResponse = await supabaseClient
              .from('households')
              .select('name')
              .eq('id', householdId)
              .single();
          
          householdName = householdResponse?['name'] as String?;
        }

        // Step 5: Update user model with complete data
        userModel = userModel.copyWith(
          householdId: householdId,
          householdName: householdName,
          isOnboardingCompleted: isOnboardingCompleted,
        );

      } catch (e) {
        // Handle errors gracefully like web app
        // If profile fetch fails, continue with basic user data
        // This mirrors web app's error handling in fetchProfile
      }

      return userModel;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> resetPassword(String email) async {
    try {
      await supabaseClient.auth.resetPasswordForEmail(email);
    } catch (e) {
      throw Exception('Password reset failed: ${e.toString()}');
    }
  }

  @override
  Future<UserModel> updateProfile(UpdateProfileParams params) async {
    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        throw Exception('No authenticated user');
      }

      // Update userMetadata if needed (like web app)
      if (params.name != null) {
        await supabaseClient.auth.updateUser(
          UserAttributes(data: {'name': params.name}),
        );
      }

      // Update profiles table for onboarding_completed (like web app)
      if (params.isOnboardingCompleted != null) {
        final existingProfile = await supabaseClient
            .from('profiles')
            .select('id')
            .eq('id', user.id)
            .maybeSingle();

        if (existingProfile == null) {
          await supabaseClient.from('profiles').insert({
            'id': user.id,
            'onboarding_completed': params.isOnboardingCompleted,
          });
        } else {
          await supabaseClient
              .from('profiles')
              .update({'onboarding_completed': params.isOnboardingCompleted})
              .eq('id', user.id);
        }
      }

      // Update users table for household_id (like web app)
      if (params.householdId != null) {
        final existingUser = await supabaseClient
            .from('users')
            .select('id')
            .eq('id', user.id)
            .maybeSingle();

        if (existingUser == null) {
          await supabaseClient.from('users').insert({
            'id': user.id,
            'household_id': params.householdId,
          });
        } else {
          await supabaseClient
              .from('users')
              .update({'household_id': params.householdId})
              .eq('id', user.id);
        }
      }

      // Create household if needed (like web app)
      if (params.householdId != null && params.householdName != null) {
        final existingHousehold = await supabaseClient
            .from('households')
            .select('id')
            .eq('id', params.householdId!)
            .maybeSingle();

        if (existingHousehold == null) {
          await supabaseClient.from('households').insert({
            'id': params.householdId,
            'name': params.householdName,
            'created_by': user.id,
          });
        }
      }

      // Return updated user
      return await getCurrentUser() ?? UserModel.fromSupabaseUser(user);
    } catch (e) {
      throw Exception('Profile update failed: ${e.toString()}');
    }
  }
}
