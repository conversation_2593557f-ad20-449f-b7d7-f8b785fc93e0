export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      families: {
        Row: {
          created_at: string | null
          id: string
          name: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          name?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string | null
        }
        Relationships: []
      }
      family_members: {
        Row: {
          avatar_url: string | null
          birth_date: string | null
          created_at: string | null
          family_id: string | null
          household_id: string | null
          id: string
          name: string
          relation: string | null
          role: string | null
        }
        Insert: {
          avatar_url?: string | null
          birth_date?: string | null
          created_at?: string | null
          family_id?: string | null
          household_id?: string | null
          id?: string
          name: string
          relation?: string | null
          role?: string | null
        }
        Update: {
          avatar_url?: string | null
          birth_date?: string | null
          created_at?: string | null
          family_id?: string | null
          household_id?: string | null
          id?: string
          name?: string
          relation?: string | null
          role?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "family_members_family_id_fkey"
            columns: ["family_id"]
            isOneToOne: false
            referencedRelation: "families"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "family_members_household_id_fkey"
            columns: ["household_id"]
            isOneToOne: false
            referencedRelation: "households"
            referencedColumns: ["id"]
          },
        ]
      }
      households: {
        Row: {
          created_at: string | null
          created_by: string
          id: string
          name: string
        }
        Insert: {
          created_at?: string | null
          created_by: string
          id?: string
          name: string
        }
        Update: {
          created_at?: string | null
          created_by?: string
          id?: string
          name?: string
        }
        Relationships: []
      }
      locations: {
        Row: {
          created_at: string | null
          description: string | null
          family_id: string | null
          household_id: string | null
          id: string
          name: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          family_id?: string | null
          household_id?: string | null
          id?: string
          name: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          family_id?: string | null
          household_id?: string | null
          id?: string
          name?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "locations_family_id_fkey"
            columns: ["family_id"]
            isOneToOne: false
            referencedRelation: "families"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "locations_household_id_fkey"
            columns: ["household_id"]
            isOneToOne: false
            referencedRelation: "households"
            referencedColumns: ["id"]
          },
        ]
      }
      tunisia_medicines: {
        Row: {
          id: string
          nom: string | null
          dosage: string | null
          forme: string | null
          presentation: string | null
          dci: string | null
          classe: string | null
          sous_classe: string | null
          laboratoire: string | null
          amm: string | null
          date_amm: string | null
          conditionnement_primaire: string | null
          specification_conditionnement_primaire: string | null
          tableau: string | null
          duree_de_conservation: string | null
          indications: string | null
          g_p_b: string | null
          veic: string | null
          created_at: string | null
        }
        Insert: {
          id?: string
          nom?: string | null
          dosage?: string | null
          forme?: string | null
          presentation?: string | null
          dci?: string | null
          classe?: string | null
          sous_classe?: string | null
          laboratoire?: string | null
          amm?: string | null
          date_amm?: string | null
          conditionnement_primaire?: string | null
          specification_conditionnement_primaire?: string | null
          tableau?: string | null
          duree_de_conservation?: string | null
          indications?: string | null
          g_p_b?: string | null
          veic?: string | null
          created_at?: string | null
        }
        Update: {
          id?: string
          nom?: string | null
          dosage?: string | null
          forme?: string | null
          presentation?: string | null
          dci?: string | null
          classe?: string | null
          sous_classe?: string | null
          laboratoire?: string | null
          amm?: string | null
          date_amm?: string | null
          conditionnement_primaire?: string | null
          specification_conditionnement_primaire?: string | null
          tableau?: string | null
          duree_de_conservation?: string | null
          indications?: string | null
          g_p_b?: string | null
          veic?: string | null
          created_at?: string | null
        }
        Relationships: []
      }

      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          email: string
          id: string
          name: string | null
          onboarding_completed: boolean | null
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          email: string
          id: string
          name?: string | null
          onboarding_completed?: boolean | null
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          email?: string
          id?: string
          name?: string | null
          onboarding_completed?: boolean | null
          updated_at?: string | null
        }
        Relationships: []
      }

      sync_log: {
        Row: {
          file_hash: string | null
          id: string
          imported_at: string | null
          source_url: string | null
        }
        Insert: {
          file_hash?: string | null
          id?: string
          imported_at?: string | null
          source_url?: string | null
        }
        Update: {
          file_hash?: string | null
          id?: string
          imported_at?: string | null
          source_url?: string | null
        }
        Relationships: []
      }
      user_medicines: {
        Row: {
          category: string | null
          created_at: string | null
          custom_name: string | null
          dosage: string | null
          expiration: string | null
          expiration_date: string | null
          family_member_id: string | null
          household_id: string | null
          id: string
          is_custom: boolean | null
          location: string | null
          low_stock_threshold: number | null
          medicine_id: string | null
          notes: string | null
          quantity: number | null
        }
        Insert: {
          category?: string | null
          created_at?: string | null
          custom_name?: string | null
          dosage?: string | null
          expiration?: string | null
          expiration_date?: string | null
          family_member_id?: string | null
          household_id?: string | null
          id?: string
          is_custom?: boolean | null
          location?: string | null
          medicine_id?: string | null
          notes?: string | null
          quantity?: number | null
        }
        Update: {
          category?: string | null
          created_at?: string | null
          custom_name?: string | null
          dosage?: string | null
          expiration?: string | null
          expiration_date?: string | null
          family_member_id?: string | null
          household_id?: string | null
          id?: string
          is_custom?: boolean | null
          location?: string | null
          medicine_id?: string | null
          notes?: string | null
          quantity?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "user_medicines_family_member_id_fkey"
            columns: ["family_member_id"]
            isOneToOne: false
            referencedRelation: "family_members"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_medicines_household_id_fkey"
            columns: ["household_id"]
            isOneToOne: false
            referencedRelation: "households"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_medicines_medicine_id_fkey"
            columns: ["medicine_id"]
            isOneToOne: false
            referencedRelation: "tunisia_medicines"
            referencedColumns: ["id"]
          },
        ]
      }
      tags: {
        Row: {
          color: string
          created_at: string | null
          household_id: string
          id: string
          is_system_tag: boolean | null
          name: string
          updated_at: string | null
        }
        Insert: {
          color?: string
          created_at?: string | null
          household_id: string
          id?: string
          is_system_tag?: boolean | null
          name: string
          updated_at?: string | null
        }
        Update: {
          color?: string
          created_at?: string | null
          household_id?: string
          id?: string
          is_system_tag?: boolean | null
          name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tags_household_id_fkey"
            columns: ["household_id"]
            isOneToOne: false
            referencedRelation: "households"
            referencedColumns: ["id"]
          },
        ]
      }
      medicine_tags: {
        Row: {
          created_at: string | null
          id: string
          tag_id: string
          user_medicine_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          tag_id: string
          user_medicine_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          tag_id?: string
          user_medicine_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "medicine_tags_tag_id_fkey"
            columns: ["tag_id"]
            isOneToOne: false
            referencedRelation: "tags"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medicine_tags_user_medicine_id_fkey"
            columns: ["user_medicine_id"]
            isOneToOne: false
            referencedRelation: "user_medicines"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          created_at: string | null
          expiry_warning_days: number | null
          household_id: string | null
          id: string
        }
        Insert: {
          created_at?: string | null
          expiry_warning_days?: number | null
          household_id?: string | null
          id: string
        }
        Update: {
          created_at?: string | null
          expiry_warning_days?: number | null
          household_id?: string | null
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "users_household_id_fkey"
            columns: ["household_id"]
            isOneToOne: false
            referencedRelation: "households"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      dashboard_medicine_alerts_view: {
        Row: {
          amm: string | null
          custom_name: string | null
          dosage: string | null
          expiration: string | null
          expiration_status: string | null
          family_member_id: string | null
          family_member_name: string | null
          household_id: string | null
          is_custom: boolean | null
          location_id: string | null
          location_name: string | null
          low_stock_threshold: number | null
          medicine_name: string | null
          official_label: string | null
          quantity: number | null
          stock_status: string | null
          user_medicine_id: string | null
          medicine_id: string | null
          forme: string | null
          laboratoire: string | null
          dci: string | null
          classe: string | null
          sous_classe: string | null
          created_at: string | null
          category: string | null
          notes: string | null
          indications: string | null
          user_expiry_threshold_months: number | null
        }
        Relationships: [
          {
            foreignKeyName: "user_medicines_family_member_id_fkey"
            columns: ["family_member_id"]
            isOneToOne: false
            referencedRelation: "family_members"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_medicines_household_id_fkey"
            columns: ["household_id"]
            isOneToOne: false
            referencedRelation: "households"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_medicines_medicine_id_fkey"
            columns: ["medicine_id"]
            isOneToOne: false
            referencedRelation: "tunisia_medicines"
            referencedColumns: ["id"]
          },
        ]
      }
      medicines_expiring_soon: {
        Row: {
          dosage: string | null
          expiration: string | null
          household_id: string | null
          location_name: string | null
          medicine_name: string | null
          quantity: number | null
          user_medicine_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_medicines_household_id_fkey"
            columns: ["household_id"]
            isOneToOne: false
            referencedRelation: "households"
            referencedColumns: ["id"]
          },
        ]
      }
      user_dashboard_data: {
        Row: {
          expiring_soon_count: number | null
          household_id: string | null
          location_count: number | null
          total_medicines: number | null
        }
        Relationships: [
          {
            foreignKeyName: "user_medicines_household_id_fkey"
            columns: ["household_id"]
            isOneToOne: false
            referencedRelation: "households"
            referencedColumns: ["id"]
          },
        ]
      }
      medicines_with_tags: {
        Row: {
          category: string | null
          created_at: string | null
          custom_name: string | null
          dosage: string | null
          expiration: string | null
          family_member_id: string | null
          household_id: string | null
          is_custom: boolean | null
          location: string | null
          medicine_id: string | null
          notes: string | null
          quantity: number | null
          tag_names: string[] | null
          tags: Json[] | null
          user_medicine_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_medicines_household_id_fkey"
            columns: ["household_id"]
            isOneToOne: false
            referencedRelation: "households"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
