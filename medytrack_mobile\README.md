# MedyTrack Mobile - Professional Medicine Management

[![Flutter Version](https://img.shields.io/badge/Flutter-3.24+-blue.svg)](https://flutter.dev/)
[![Dart Version](https://img.shields.io/badge/Dart-3.5+-blue.svg)](https://dart.dev/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/Platform-Android%20%7C%20iOS-lightgrey.svg)](https://flutter.dev/)

MedyTrack Mobile is a high-performance native mobile application for professional medicine management, built with Flutter. It provides comprehensive medicine tracking, pharmaceutical tagging, expiry management, and household organization features optimized for Tunisia and French-speaking regions.

## 🎯 **Features**

### **Core Medicine Management**
- ✅ **Add/Edit/Delete Medicines** with comprehensive data preservation
- ✅ **Barcode Scanning** with ML Kit integration
- ✅ **Photo Capture** for medicine identification
- ✅ **Custom Medicine Entry** for non-database medicines
- ✅ **Batch Operations** for efficient management

### **Pharmaceutical Tagging System**
- ✅ **24 Standardized Tags** (12 therapeutic + 12 usage domains)
- ✅ **Professional Categories** optimized for medical use
- ✅ **Color-Coded Organization** with visual consistency
- ✅ **Multi-Tag Support** for comprehensive categorization

### **Smart Organization**
- ✅ **Location-Based Storage** with usage analytics
- ✅ **Family Member Assignment** for household management
- ✅ **Custom Expiry Thresholds** (1-12 months per user)
- ✅ **Low Stock Management** with configurable thresholds

### **Dashboard & Analytics**
- ✅ **Real-Time Statistics** (total, expired, expiring, low stock)
- ✅ **Visual Charts** with interactive data
- ✅ **Alert System** with push notifications
- ✅ **Usage Insights** and trends analysis

### **Mobile-Specific Features**
- ✅ **Offline-First Architecture** with intelligent sync
- ✅ **Biometric Authentication** (Face ID, Touch ID, Fingerprint)
- ✅ **Push Notifications** for expiry and stock alerts
- ✅ **Camera Integration** with image compression
- ✅ **Secure Storage** with platform-specific encryption

## 🏗️ **Architecture**

### **Clean Architecture Pattern**
```
lib/
├── core/                 # Core utilities and configurations
│   ├── config/          # App configuration and constants
│   ├── di/              # Dependency injection setup
│   ├── errors/          # Error handling and failures
│   ├── network/         # Network utilities and info
│   ├── router/          # Navigation and routing
│   ├── storage/         # Local and secure storage
│   ├── theme/           # UI theme and styling
│   └── utils/           # Utility functions and helpers
├── data/                # Data layer implementation
│   ├── datasources/     # Remote and local data sources
│   ├── models/          # Data models and DTOs
│   └── repositories/    # Repository implementations
├── domain/              # Business logic layer
│   ├── entities/        # Core business entities
│   ├── repositories/    # Repository interfaces
│   └── usecases/        # Business use cases
└── presentation/        # UI layer
    ├── bloc/            # State management (BLoC pattern)
    ├── pages/           # Screen implementations
    └── widgets/         # Reusable UI components
```

### **Technology Stack**
- **Framework**: Flutter 3.24+ with Dart 3.5+
- **State Management**: BLoC Pattern with flutter_bloc
- **Navigation**: go_router for declarative routing
- **Backend**: Supabase (PostgreSQL + Auth + Storage)
- **Local Storage**: Hive + Flutter Secure Storage
- **Dependency Injection**: get_it with injectable
- **Camera**: camera + mobile_scanner for barcode scanning
- **Notifications**: Firebase Cloud Messaging + Local Notifications

## 🚀 **Getting Started**

### **Prerequisites**
- Flutter SDK 3.24.0 or higher
- Dart SDK 3.5.0 or higher
- Android Studio / Xcode for platform-specific development
- Firebase project for push notifications
- Supabase project for backend services

### **Installation**

1. **Clone the repository**
   ```bash
   git clone https://github.com/medytrack/medytrack-mobile.git
   cd medytrack-mobile
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code**
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Configure Firebase**
   - Add `google-services.json` to `android/app/`
   - Add `GoogleService-Info.plist` to `ios/Runner/`

5. **Configure Supabase**
   - Update `lib/core/config/app_config.dart` with your Supabase credentials

6. **Run the application**
   ```bash
   # Development
   flutter run --flavor development
   
   # Staging
   flutter run --flavor staging
   
   # Production
   flutter run --flavor production
   ```

### **Build for Release**

#### **Android**
```bash
# Build AAB for Play Store
flutter build appbundle --flavor production --release

# Build APK for direct distribution
flutter build apk --flavor production --release
```

#### **iOS**
```bash
# Build for App Store
flutter build ios --flavor production --release

# Build IPA
flutter build ipa --flavor production --release
```

## 📱 **Platform Support**

### **Android**
- **Minimum SDK**: API 26 (Android 8.0)
- **Target SDK**: API 34 (Android 14)
- **Architecture**: arm64-v8a, armeabi-v7a
- **Features**: Material Design 3, Dynamic theming, Biometric auth

### **iOS**
- **Minimum Version**: iOS 14.0
- **Target Version**: iOS 17.0
- **Architecture**: arm64 (iPhone 6s and later)
- **Features**: Human Interface Guidelines, Face ID/Touch ID

## 🧪 **Testing**

### **Run Tests**
```bash
# Unit tests
flutter test

# Integration tests
flutter test integration_test/

# Widget tests
flutter test test/widget_test/

# Coverage report
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

### **Testing Strategy**
- **Unit Tests**: 80%+ coverage for business logic
- **Widget Tests**: UI component testing
- **Integration Tests**: End-to-end user flows
- **Golden Tests**: Visual regression testing

## 🔧 **Development**

### **Code Generation**
```bash
# Generate models, dependency injection, etc.
flutter packages pub run build_runner build

# Watch for changes
flutter packages pub run build_runner watch
```

### **Linting and Formatting**
```bash
# Analyze code
flutter analyze

# Format code
dart format .

# Fix linting issues
dart fix --apply
```

### **Environment Configuration**
The app supports multiple environments:
- **Development**: Local development with debug features
- **Staging**: Pre-production testing environment
- **Production**: Live production environment

## 📊 **Performance**

### **Benchmarks**
- **App Launch Time**: <3 seconds on mid-range devices
- **Memory Usage**: <200MB baseline, <500MB peak
- **Frame Rate**: 60fps with <16ms frame rendering
- **Battery Usage**: <5% per hour of active use

### **Optimization Features**
- **Image Compression**: Automatic image optimization
- **Lazy Loading**: Efficient list rendering
- **Caching**: Intelligent data and image caching
- **Background Sync**: Optimized background operations

## 🔒 **Security**

### **Data Protection**
- **End-to-End Encryption**: Sensitive data transmission
- **Local Encryption**: Platform-specific secure storage
- **Certificate Pinning**: API communication security
- **Biometric Authentication**: Face ID, Touch ID, Fingerprint

### **Privacy Compliance**
- **GDPR Compliant**: European data protection standards
- **CCPA Compliant**: California privacy regulations
- **Granular Permissions**: User-controlled data access
- **Data Minimization**: Only necessary data collection

## 📈 **Monitoring**

### **Analytics & Crash Reporting**
- **Firebase Analytics**: User behavior insights
- **Firebase Crashlytics**: Crash reporting and analysis
- **Performance Monitoring**: App performance metrics
- **Custom Events**: Feature usage tracking

### **Health Checks**
- **Network Connectivity**: Real-time connection monitoring
- **Database Health**: Supabase connection status
- **Storage Usage**: Local storage optimization
- **Memory Leaks**: Automatic memory management

## 🌍 **Localization**

### **Supported Languages**
- **French (fr)**: Primary language for Tunisia/France
- **English (en)**: International support
- **Arabic (ar)**: Regional support for Tunisia

### **Adding New Languages**
1. Add language code to `CFBundleLocalizations` (iOS)
2. Update `supportedLocales` in `app_config.dart`
3. Create translation files in `lib/l10n/`
4. Generate translations: `flutter gen-l10n`

## 🚀 **Deployment**

### **App Store Deployment**
1. **Prepare Release Build**
   ```bash
   flutter build ipa --flavor production --release
   ```

2. **Upload to App Store Connect**
   - Use Xcode or Transporter app
   - Fill in app metadata and screenshots
   - Submit for review

### **Google Play Deployment**
1. **Prepare Release Build**
   ```bash
   flutter build appbundle --flavor production --release
   ```

2. **Upload to Play Console**
   - Create release in Play Console
   - Upload AAB file
   - Configure store listing and submit

## 📞 **Support**

### **Documentation**
- **API Documentation**: [Supabase Docs](https://supabase.com/docs)
- **Flutter Documentation**: [Flutter.dev](https://flutter.dev/docs)
- **Architecture Guide**: See `/docs/architecture.md`

### **Contributing**
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

### **Issues**
Report bugs and feature requests on [GitHub Issues](https://github.com/medytrack/medytrack-mobile/issues)

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**MedyTrack Mobile** - Professional medicine management for the mobile era 🏥📱
