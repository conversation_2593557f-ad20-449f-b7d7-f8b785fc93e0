import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';

class MedicineDetailPage extends StatelessWidget {
  final String medicineId;
  
  const MedicineDetailPage({
    super.key,
    required this.medicineId,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Détails du médicament'),
        backgroundColor: AppColors.navy,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.medication,
              size: 64,
              color: AppColors.grey400,
            ),
            const SizedBox(height: 16),
            Text(
              'Médicament ID: $medicineId',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: AppColors.grey600,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Page de détails à implémenter',
              style: TextStyle(
                color: AppColors.grey500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
