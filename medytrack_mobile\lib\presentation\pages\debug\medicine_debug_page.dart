import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../bloc/medicine/medicine_bloc.dart';
import '../../../domain/entities/medicine.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/date_utils.dart';

class MedicineDebugPage extends StatefulWidget {
  const MedicineDebugPage({super.key});

  @override
  State<MedicineDebugPage> createState() => _MedicineDebugPageState();
}

class _MedicineDebugPageState extends State<MedicineDebugPage> {
  final _nameController = TextEditingController(text: 'Paracétamol');
  final _dosageController = TextEditingController(text: '500mg');
  final _formController = TextEditingController(text: 'Comprimé');
  final _presentationController = TextEditingController(text: '<PERSON><PERSON><PERSON> de 20');
  final _quantityController = TextEditingController(text: '15');
  final _notesController = TextEditingController(text: 'Test medicine');
  DateTime _selectedExpiryDate = DateTime.now().add(const Duration(days: 365));

  @override
  void initState() {
    super.initState();
    // Load medicines when page opens
    context.read<MedicineBloc>().add(MedicineLoadRequested());
  }

  @override
  void dispose() {
    _nameController.dispose();
    _dosageController.dispose();
    _formController.dispose();
    _presentationController.dispose();
    _quantityController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Medicine Management Debug'),
        backgroundColor: AppColors.navy,
        foregroundColor: Colors.white,
      ),
      body: BlocBuilder<MedicineBloc, MedicineState>(
        builder: (context, state) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Current Medicine State
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Current Medicine State',
                          style: AppTextStyles.headlineSmall.copyWith(
                            color: AppColors.navy,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        _buildStateInfo(state),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // Add Medicine Form
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Add Test Medicine',
                          style: AppTextStyles.headlineSmall.copyWith(
                            color: AppColors.navy,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // Medicine Name
                        TextField(
                          controller: _nameController,
                          decoration: const InputDecoration(
                            labelText: 'Medicine Name',
                            border: OutlineInputBorder(),
                          ),
                        ),
                        const SizedBox(height: 12),
                        
                        // Dosage and Form
                        Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: _dosageController,
                                decoration: const InputDecoration(
                                  labelText: 'Dosage',
                                  border: OutlineInputBorder(),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: TextField(
                                controller: _formController,
                                decoration: const InputDecoration(
                                  labelText: 'Form',
                                  border: OutlineInputBorder(),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        
                        // Presentation and Quantity
                        Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: _presentationController,
                                decoration: const InputDecoration(
                                  labelText: 'Presentation',
                                  border: OutlineInputBorder(),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: TextField(
                                controller: _quantityController,
                                keyboardType: TextInputType.number,
                                decoration: const InputDecoration(
                                  labelText: 'Quantity',
                                  border: OutlineInputBorder(),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        
                        // Expiry Date
                        InkWell(
                          onTap: _selectExpiryDate,
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('Expiry Date: ${AppDateUtils.formatDate(_selectedExpiryDate)}'),
                                const Icon(Icons.calendar_today),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),
                        
                        // Notes
                        TextField(
                          controller: _notesController,
                          maxLines: 2,
                          decoration: const InputDecoration(
                            labelText: 'Notes',
                            border: OutlineInputBorder(),
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // Action Buttons
                        Row(
                          children: [
                            Expanded(
                              child: ElevatedButton(
                                onPressed: state is MedicineLoading ? null : _addMedicine,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.teal,
                                  foregroundColor: Colors.white,
                                ),
                                child: const Text('Add Medicine'),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: ElevatedButton(
                                onPressed: state is MedicineLoading ? null : _loadMedicines,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.navy,
                                  foregroundColor: Colors.white,
                                ),
                                child: const Text('Reload Medicines'),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // Medicines List
                if (state is MedicineLoaded)
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Medicines (${state.medicines.length})',
                            style: AppTextStyles.headlineSmall.copyWith(
                              color: AppColors.navy,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          if (state.medicines.isEmpty)
                            const Center(
                              child: Text('No medicines found'),
                            )
                          else
                            ...state.medicines.map((medicine) => _buildMedicineCard(medicine)),
                        ],
                      ),
                    ),
                  ),
                
                if (state is MedicineLoading)
                  const Padding(
                    padding: EdgeInsets.all(16),
                    child: Center(child: CircularProgressIndicator()),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStateInfo(MedicineState state) {
    String stateText;
    Color stateColor;
    
    if (state is MedicineInitial) {
      stateText = 'Initial - Not loaded yet';
      stateColor = Colors.grey;
    } else if (state is MedicineLoading) {
      stateText = 'Loading - Fetching medicines...';
      stateColor = Colors.blue;
    } else if (state is MedicineLoaded) {
      stateText = 'Loaded - ${state.medicines.length} medicines found';
      stateColor = Colors.green;
    } else if (state is MedicineError) {
      stateText = 'Error - ${state.message}';
      stateColor = Colors.red;
    } else {
      stateText = 'Unknown state: ${state.runtimeType}';
      stateColor = Colors.grey;
    }
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: stateColor.withOpacity(0.1),
        border: Border.all(color: stateColor),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        stateText,
        style: AppTextStyles.bodyMedium.copyWith(
          color: stateColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildMedicineCard(Medicine medicine) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    medicine.name,
                    style: AppTextStyles.titleMedium.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.navy,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => _deleteMedicine(medicine.id),
                  icon: const Icon(Icons.delete, color: Colors.red),
                ),
              ],
            ),
            if (medicine.dosage != null || medicine.form != null)
              Text('${medicine.dosage ?? ''} ${medicine.form ?? ''}'),
            Text('Quantity: ${medicine.quantity}'),
            Text('Expires: ${AppDateUtils.formatDate(medicine.expiryDate)}'),
            if (medicine.notes != null)
              Text('Notes: ${medicine.notes}'),
          ],
        ),
      ),
    );
  }

  Future<void> _selectExpiryDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedExpiryDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
    );
    if (date != null) {
      setState(() {
        _selectedExpiryDate = date;
      });
    }
  }

  void _addMedicine() {
    final medicine = Medicine(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: _nameController.text,
      dosage: _dosageController.text.isEmpty ? null : _dosageController.text,
      form: _formController.text.isEmpty ? null : _formController.text,
      presentation: _presentationController.text.isEmpty ? null : _presentationController.text,
      quantity: int.tryParse(_quantityController.text) ?? 1,
      expiryDate: _selectedExpiryDate,
      notes: _notesController.text.isEmpty ? null : _notesController.text,
      lowStockThreshold: 5,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    
    context.read<MedicineBloc>().add(MedicineAddRequested(medicine));
  }

  void _loadMedicines() {
    context.read<MedicineBloc>().add(MedicineLoadRequested());
  }

  void _deleteMedicine(String id) {
    context.read<MedicineBloc>().add(MedicineDeleteRequested(id));
  }
}
