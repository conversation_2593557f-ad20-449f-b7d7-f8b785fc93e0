import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { ArrowUpDown, Grid3X3, List } from "lucide-react";
import { cn } from "@/lib/utils";

export type SortOption =
  | 'name-asc'
  | 'name-desc'
  | 'expiry-asc'
  | 'expiry-desc'
  | 'quantity-asc'
  | 'quantity-desc'
  | 'created-desc';

export type ViewMode = 'individual' | 'grouped';

interface MedicineSortingProps {
  sortBy: SortOption;
  onSortChange: (sortBy: SortOption) => void;
  totalCount: number;
  viewMode?: ViewMode;
  onViewModeChange?: (viewMode: ViewMode) => void;
  groupedCount?: number;
  showViewToggle?: boolean;
}

const SORT_OPTIONS = [
  { value: 'name-asc', label: 'Nom (A-Z)' },
  { value: 'name-desc', label: 'Nom (Z-A)' },
  { value: 'expiry-asc', label: 'Expiration (plus proche)' },
  { value: 'expiry-desc', label: 'Expiration (plus lointaine)' },
  { value: 'quantity-asc', label: 'Quantité (plus faible)' },
  { value: 'quantity-desc', label: 'Quantité (plus élevée)' },
  { value: 'created-desc', label: 'Ajouté récemment' }
];

const MedicineSorting = ({
  sortBy,
  onSortChange,
  totalCount,
  viewMode = 'grouped',
  onViewModeChange,
  groupedCount,
  showViewToggle = true
}: MedicineSortingProps) => {

  // Format count display based on view mode
  const getCountDisplay = () => {
    if (viewMode === 'grouped' && groupedCount !== undefined) {
      return (
        <div className="text-sm text-gray-600">
          <span className="font-medium text-navy">{groupedCount}</span> groupe{groupedCount !== 1 ? 's' : ''} •
          <span className="ml-1">{totalCount} médicament{totalCount !== 1 ? 's' : ''}</span>
        </div>
      );
    }

    return (
      <div className="text-sm text-gray-600">
        <span className="font-medium text-navy">{totalCount}</span> médicament{totalCount !== 1 ? 's' : ''} trouvé{totalCount !== 1 ? 's' : ''}
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* View Mode Toggle */}
      {showViewToggle && onViewModeChange && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Label className="text-sm font-medium text-navy">
              Affichage:
            </Label>
            <div className="flex bg-gray-100 rounded-lg p-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onViewModeChange('grouped')}
                className={cn(
                  "px-3 py-1 text-xs font-medium transition-all",
                  viewMode === 'grouped'
                    ? "bg-teal text-white shadow-sm"
                    : "text-gray-600 hover:text-navy hover:bg-gray-200"
                )}
              >
                <Grid3X3 size={14} className="mr-1" />
                Groupé
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onViewModeChange('individual')}
                className={cn(
                  "px-3 py-1 text-xs font-medium transition-all",
                  viewMode === 'individual'
                    ? "bg-teal text-white shadow-sm"
                    : "text-gray-600 hover:text-navy hover:bg-gray-200"
                )}
              >
                <List size={14} className="mr-1" />
                Individuel
              </Button>
            </div>
          </div>

          {getCountDisplay()}
        </div>
      )}

      {/* Sorting Controls */}
      <div className="flex items-center justify-between">
        {!showViewToggle && getCountDisplay()}

        <div className="flex items-center space-x-2">
          <ArrowUpDown size={16} className="text-teal" />
          <Label htmlFor="sort-select" className="text-sm font-medium text-navy">
            Trier par:
          </Label>
          <Select value={sortBy} onValueChange={onSortChange}>
            <SelectTrigger
              id="sort-select"
              className="w-48 border-teal/20 focus:border-teal focus:ring-teal"
            >
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {SORT_OPTIONS.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};

export default MedicineSorting;
