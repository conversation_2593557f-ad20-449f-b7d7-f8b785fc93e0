
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Location } from '@/types';
import { toast } from 'sonner';

export const useLocations = () => {
  const [locations, setLocations] = useState<Location[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user, householdId } = useAuth();

  // Note: We now get householdId directly from AuthContext

  const fetchLocations = async () => {
    if (!user || !householdId) {
      return;
    }
    setIsLoading(true);

    try {
      // Fetch locations for the household
      const { data, error } = await supabase
        .from('locations')
        .select('*')
        .eq('household_id', householdId);

      if (error) {
        throw error;
      }

      const formattedData: Location[] = (data || []).map((item) => ({
        id: item.id,
        name: item.name,
        description: item.description || '',
        icon: 'home' // Default icon
      }));

      setLocations(formattedData);
    } catch (err) {
      toast.error('Erreur lors du chargement des emplacements');
    } finally {
      setIsLoading(false);
    }
  };

  const addLocation = async (name: string, description: string = '') => {
    if (!user || !householdId) {
      console.error('Cannot add location: missing user or householdId', { user: !!user, householdId });
      toast.error('Erreur: utilisateur ou foyer non configuré');
      return null;
    }

    try {
      // Insert with household_id instead of user_id
      const insertData = {
        name,
        description,
        household_id: householdId
      };

      console.log('Inserting location with data:', insertData);

      const { data, error } = await supabase
        .from('locations')
        .insert(insertData)
        .select();

      if (error) {
        console.error('Insert error:', error);
        throw error;
      }

      if (!data || data.length === 0) {
        throw new Error('No data returned after insert');
      }

      const newLoc = data[0];
      const newLocation: Location = {
        id: newLoc.id,
        name: newLoc.name,
        description: newLoc.description || '',
        icon: 'home'
      };

      setLocations([...locations, newLocation]);
      toast.success('Emplacement ajouté avec succès');
      return newLocation;
    } catch (err) {
      console.error('Error adding location:', err);
      toast.error('Erreur lors de l\'ajout de l\'emplacement');
      return null;
    }
  };

  const updateLocation = async (id: string, updates: Partial<Location>) => {
    if (!user || !householdId) return false;
    
    try {
      const updateData = {
        ...(updates.name !== undefined && { name: updates.name }),
        ...(updates.description !== undefined && { description: updates.description })
      };
      
      const { error } = await supabase
        .from('locations')
        .update(updateData)
        .eq('id', id)
        .eq('household_id', householdId);
      
      if (error) throw error;
      
      setLocations(locations.map(loc => 
        loc.id === id ? { ...loc, ...updates } : loc
      ));
      
      toast.success('Emplacement mis à jour avec succès');
      return true;
    } catch (err) {
      console.error('Error updating location:', err);
      toast.error('Erreur lors de la mise à jour de l\'emplacement');
      return false;
    }
  };

  const deleteLocation = async (id: string) => {
    if (!user || !householdId) return false;
    
    try {
      const { error } = await supabase
        .from('locations')
        .delete()
        .eq('id', id)
        .eq('household_id', householdId);
      
      if (error) throw error;
      
      setLocations(locations.filter(loc => loc.id !== id));
      toast.success('Emplacement supprimé avec succès');
      return true;
    } catch (err) {
      console.error('Error deleting location:', err);
      toast.error('Erreur lors de la suppression de l\'emplacement');
      return false;
    }
  };

  useEffect(() => {
    if (user && householdId) {
      fetchLocations();
    } else {
      setLocations([]);
      setIsLoading(false);
    }
  }, [user, householdId]);

  return {
    locations,
    isLoading,
    fetchLocations,
    addLocation,
    updateLocation,
    deleteLocation
  };
};
