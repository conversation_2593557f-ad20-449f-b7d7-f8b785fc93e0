
import React from "react";
import BottomNavigation from "@/components/BottomNavigation";
import FloatingActionButton from "@/components/FloatingActionButton";
import { useLocation } from "react-router-dom";
import featureFlags from "@/config/featureFlags";

interface LayoutProps {
  children: React.ReactNode;
  showFAB?: boolean;
  showBottomNav?: boolean;
}

const Layout = ({ 
  children, 
  showFAB = true, 
  showBottomNav = true 
}: LayoutProps) => {
  const location = useLocation();
  
  // Don't show FAB on add pages
  const shouldShowFAB = showFAB && 
    !location.pathname.includes("add-") &&
    !location.pathname.includes("/auth");

  // Don't show bottom nav on auth pages
  const shouldShowBottomNav = showBottomNav && 
    !location.pathname.includes("/auth");

  return (
    <div className="medicinet-container animate-fade-in pb-20">
      {children}
      {shouldShowFAB && <FloatingActionButton />}
      {shouldShowBottomNav && <BottomNavigation />}
    </div>
  );
};

export default Layout;
