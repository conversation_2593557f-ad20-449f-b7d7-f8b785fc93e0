{"buildFiles": ["C:\\Users\\<USER>\\Dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\GitHub\\medy-track (Lovable)\\medytrack_mobile\\build\\.cxx\\Debug\\4js2g2r3\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\GitHub\\medy-track (Lovable)\\medytrack_mobile\\build\\.cxx\\Debug\\4js2g2r3\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}