
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { FamilyMember } from '@/types';
import { toast } from 'sonner';

export const useFamilyMembers = () => {
  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [householdId, setHouseholdId] = useState<string | null>(null);
  const { user, profile } = useAuth();

  // Get the household ID for the current user
  const fetchHouseholdId = async () => {
    if (!user) return null;
    
    try {
      const { data, error } = await supabase
        .from('users')
        .select('household_id')
        .eq('id', user.id)
        .single();
        
      if (error) throw error;
      return data?.household_id || null;
    } catch (err) {
      console.error('Error fetching household ID:', err);
      return null;
    }
  };

  const fetchFamilyMembers = async () => {
    if (!user || !householdId) return;
    setIsLoading(true);
    
    try {
      const { data, error } = await supabase
        .from('family_members')
        .select('*')
        .eq('household_id', householdId);

      if (error) {
        console.error('Error fetching family members:', error);
        throw error;
      }
      
      const formattedData: FamilyMember[] = (data || []).map((item) => ({
        id: item.id,
        name: item.name,
        relation: item.relation,
        birthDate: item.birth_date,
        avatar: item.avatar_url
      }));

      // Add the main user as the first family member if not already present
      const mainUserName = profile?.name || user?.user_metadata?.name || user?.email?.split('@')[0] || 'Utilisateur principal';
      const mainUserExists = formattedData.some(member =>
        member.name === mainUserName ||
        member.relation === 'Moi' ||
        member.relation === 'Utilisateur principal'
      );

      if (!mainUserExists && user?.id && householdId) {
        // Create a proper family member entry in the database for the main user
        try {
          const { data: newMainUser, error: insertError } = await supabase
            .from('family_members')
            .insert({
              name: mainUserName,
              relation: 'Moi',
              household_id: householdId
            })
            .select()
            .single();

          if (!insertError && newMainUser) {
            const mainUserMember: FamilyMember = {
              id: newMainUser.id,
              name: newMainUser.name,
              relation: newMainUser.relation,
              birthDate: newMainUser.birth_date,
              avatar: newMainUser.avatar_url
            };
            formattedData.unshift(mainUserMember); // Add at the beginning
          } else if (insertError) {
            console.error('Error creating main user family member:', insertError);
          }
        } catch (err) {
          console.error('Error creating main user family member:', err);
          // If creation fails, don't add the main user to avoid UUID issues
        }
      }

      setFamilyMembers(formattedData);
    } catch (err) {
      console.error('Error fetching family members:', err);
      toast.error('Erreur lors du chargement des membres de famille');
    } finally {
      setIsLoading(false);
    }
  };

  const addFamilyMember = async (name: string, relation?: string, birthDate?: string) => {
    if (!user || !householdId) return null;
    
    try {
      // Use household_id instead of user_id
      const { data, error } = await supabase
        .from('family_members')
        .insert({
          name,
          relation,
          birth_date: birthDate,
          household_id: householdId
        })
        .select();
      
      if (error) {
        console.error('Insert error:', error);
        throw error;
      }
      
      if (!data || data.length === 0) {
        throw new Error('No data returned after insert');
      }
      
      const newMember = data[0];
      const newFamilyMember: FamilyMember = {
        id: newMember.id,
        name: newMember.name,
        relation: newMember.relation,
        birthDate: newMember.birth_date,
        avatar: newMember.avatar_url
      };
      
      setFamilyMembers([...familyMembers, newFamilyMember]);
      toast.success('Membre ajouté avec succès');
      return newFamilyMember;
    } catch (err) {
      console.error('Error adding family member:', err);
      toast.error('Erreur lors de l\'ajout du membre');
      return null;
    }
  };

  const updateFamilyMember = async (id: string, updates: Partial<FamilyMember>) => {
    if (!user || !householdId) return false;
    
    try {
      const updateData = {
        ...(updates.name !== undefined && { name: updates.name }),
        ...(updates.relation !== undefined && { relation: updates.relation }),
        ...(updates.birthDate !== undefined && { birth_date: updates.birthDate }),
        ...(updates.avatar !== undefined && { avatar_url: updates.avatar })
      };
      
      const { error } = await supabase
        .from('family_members')
        .update(updateData)
        .eq('id', id)
        .eq('household_id', householdId);
      
      if (error) throw error;
      
      setFamilyMembers(familyMembers.map(member => 
        member.id === id ? { ...member, ...updates } : member
      ));
      
      toast.success('Membre mis à jour avec succès');
      return true;
    } catch (err) {
      console.error('Error updating family member:', err);
      toast.error('Erreur lors de la mise à jour du membre');
      return false;
    }
  };

  const deleteFamilyMember = async (id: string) => {
    if (!user || !householdId) return false;
    
    try {
      const { error } = await supabase
        .from('family_members')
        .delete()
        .eq('id', id)
        .eq('household_id', householdId);
      
      if (error) throw error;
      
      setFamilyMembers(familyMembers.filter(member => member.id !== id));
      toast.success('Membre supprimé avec succès');
      return true;
    } catch (err) {
      console.error('Error deleting family member:', err);
      toast.error('Erreur lors de la suppression du membre');
      return false;
    }
  };

  useEffect(() => {
    if (user) {
      fetchHouseholdId().then(id => {
        if (id) {
          setHouseholdId(id);
        }
      });
    }
  }, [user]);

  useEffect(() => {
    if (householdId) {
      fetchFamilyMembers();
    }
  }, [householdId]);

  return {
    familyMembers,
    isLoading,
    fetchFamilyMembers,
    addFamilyMember,
    updateFamilyMember,
    deleteFamilyMember,
    householdId
  };
};
