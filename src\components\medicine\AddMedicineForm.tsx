
import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { CalendarIcon, CheckCircle2, PencilLine } from "lucide-react";
import { Medicine, Location, FamilyMember } from "@/types";
import { formatCompleteMedicineName } from "@/utils/medicineUtils";
import MonthYearPicker from "@/components/ui/month-year-picker";

interface AddMedicineFormProps {
  formData: any;
  onFormChange: (field: string, value: any) => void;
  onSubmit: (e: React.FormEvent) => void;
  locations: Location[];
  familyMembers: FamilyMember[];
  isCustom: boolean;
  selectedMedicine: any | null;
}

const AddMedicineForm = ({
  formData,
  onFormChange,
  onSubmit,
  locations,
  familyMembers,
  isCustom,
  selectedMedicine
}: AddMedicineFormProps) => {
  const handleCategoryChange = (value: string) => {
    onFormChange('category', value);
  };

  return (
    <form onSubmit={onSubmit} className="space-y-6">
      <div className={cn(
        "flex items-center gap-2 p-3 rounded-lg text-sm",
        isCustom ? "bg-orange-50 text-orange-700" : "bg-green-50 text-green-700"
      )}>
        {isCustom ? (
          <>
            <PencilLine className="h-5 w-5" />
            <span>Origine: Saisie manuelle 📝</span>
          </>
        ) : (
          <>
            <CheckCircle2 className="h-5 w-5" />
            <span>Origine: Base officielle ✅</span>
          </>
        )}
      </div>

      {isCustom && (
        <div className="space-y-2">
          <Label htmlFor="custom_name">Nom du médicament (manuel)*</Label>
          <Input
            id="custom_name"
            value={formData.custom_name || ''}
            onChange={(e) => onFormChange('custom_name', e.target.value)}
            required
          />
        </div>
      )}

      {!isCustom && selectedMedicine && (
        <div className="bg-gray-50 p-3 rounded-lg mb-4">
          <h3 className="font-medium">{formatCompleteMedicineName(selectedMedicine as any)}</h3>
          {selectedMedicine.holder && (
            <p className="text-sm text-gray-600">Laboratoire: {selectedMedicine.holder}</p>
          )}
        </div>
      )}



      <div className="space-y-2">
        <Label htmlFor="category">Catégorie</Label>
        <Select 
          value={formData.category || 'other'} 
          onValueChange={handleCategoryChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="Choisir une catégorie" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="pain">Douleur</SelectItem>
            <SelectItem value="cold">Rhume / Toux</SelectItem>
            <SelectItem value="allergy">Allergie</SelectItem>
            <SelectItem value="digestion">Digestion</SelectItem>
            <SelectItem value="first-aid">Premier secours</SelectItem>
            <SelectItem value="prescription">Ordonnance</SelectItem>
            <SelectItem value="other">Autre</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="quantity">Quantité*</Label>
          <Input
            id="quantity"
            type="number"
            min={1}
            value={formData.quantity || 1}
            onChange={(e) => onFormChange('quantity', parseInt(e.target.value))}
            required
          />
        </div>

        <MonthYearPicker
          label="Date d'expiration"
          value={formData.expiryDate}
          onChange={(date) => onFormChange('expiryDate', date)}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="location">Emplacement</Label>
        <Select 
          value={formData.location || ''} 
          onValueChange={(value) => onFormChange('location', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Choisir un emplacement" />
          </SelectTrigger>
          <SelectContent>
            {locations.map((location) => (
              <SelectItem key={location.id} value={location.id}>
                {location.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="familyMember">Membre de la famille</Label>
        <Select
          value={formData.familyMember || ''}
          onValueChange={(value) => onFormChange('familyMember', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Choisir un membre" />
          </SelectTrigger>
          <SelectContent>
            {familyMembers.map((member) => (
              <SelectItem key={member.id} value={member.id}>
                {member.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="lowStockThreshold">Seuil de stock faible</Label>
        <Input
          id="lowStockThreshold"
          type="number"
          min="0"
          value={formData.lowStockThreshold || 0}
          onChange={(e) => onFormChange('lowStockThreshold', parseInt(e.target.value) || 0)}
          placeholder="0"
        />
        <p className="text-xs text-gray-500">
          Vous serez alerté quand la quantité atteint ce seuil
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="notes">Notes</Label>
        <Textarea
          id="notes"
          value={formData.notes || ''}
          onChange={(e) => onFormChange('notes', e.target.value)}
          placeholder="Ajoutez des notes ou instructions..."
        />
      </div>

      <Button type="submit" className="w-full">
        Enregistrer
      </Button>
    </form>
  );
};

export default AddMedicineForm;
