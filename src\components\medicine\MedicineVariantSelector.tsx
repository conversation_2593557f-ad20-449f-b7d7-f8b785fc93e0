import React, { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Loader2, Check, Plus } from "lucide-react";
import { MedicineVariant } from "@/hooks/useMedicineSearch";

interface MedicineVariantSelectorProps {
  medicineName: string;
  variants: MedicineVariant[];
  isLoading: boolean;
  onVariantSelect: (variant: MedicineVariant) => void;
  onCancel: () => void;
  onCreateManual?: (medicineName: string) => void;
}

const MedicineVariantSelector = ({
  medicineName,
  variants,
  isLoading,
  onVariantSelect,
  onCancel,
  onCreateManual
}: MedicineVariantSelectorProps) => {
  const [selectedDosage, setSelectedDosage] = useState<string>("");
  const [selectedForme, setSelectedForme] = useState<string>("");
  const [selectedPresentation, setSelectedPresentation] = useState<string>("");
  const [availableDosages, setAvailableDosages] = useState<string[]>([]);
  const [availableFormes, setAvailableFormes] = useState<string[]>([]);
  const [availablePresentations, setAvailablePresentations] = useState<string[]>([]);
  const [filteredVariants, setFilteredVariants] = useState<MedicineVariant[]>([]);

  useEffect(() => {
    if (variants.length > 0) {
      // Get unique dosages, forms, and presentations
      const dosages = [...new Set(variants.map(v => v.dosage).filter(Boolean))].sort();
      const formes = [...new Set(variants.map(v => v.forme).filter(Boolean))].sort();
      const presentations = [...new Set(variants.map(v => v.presentation).filter(Boolean))].sort();

      setAvailableDosages(dosages);
      setAvailableFormes(formes);
      setAvailablePresentations(presentations);

      // Reset selections when variants change
      setSelectedDosage("");
      setSelectedForme("");
      setSelectedPresentation("");
      setFilteredVariants(variants);
    }
  }, [variants]);

  useEffect(() => {
    // Filter variants based on current selections
    let filtered = variants;

    if (selectedDosage) {
      filtered = filtered.filter(v => v.dosage === selectedDosage);
    }

    if (selectedForme) {
      filtered = filtered.filter(v => v.forme === selectedForme);
    }

    if (selectedPresentation) {
      filtered = filtered.filter(v => v.presentation === selectedPresentation);
    }

    setFilteredVariants(filtered);

    // Update available options based on current selections
    let baseVariants = variants;

    // Filter base variants for interdependent dropdowns
    if (selectedDosage) {
      baseVariants = baseVariants.filter(v => v.dosage === selectedDosage);
    }
    if (selectedForme) {
      baseVariants = baseVariants.filter(v => v.forme === selectedForme);
    }
    if (selectedPresentation) {
      baseVariants = baseVariants.filter(v => v.presentation === selectedPresentation);
    }

    // Update available formes based on selected dosage and presentation
    if (selectedDosage || selectedPresentation) {
      let formesBase = variants;
      if (selectedDosage) formesBase = formesBase.filter(v => v.dosage === selectedDosage);
      if (selectedPresentation) formesBase = formesBase.filter(v => v.presentation === selectedPresentation);

      const availableFormes = [...new Set(formesBase.map(v => v.forme).filter(Boolean))].sort();
      setAvailableFormes(availableFormes);
    } else {
      const allFormes = [...new Set(variants.map(v => v.forme).filter(Boolean))].sort();
      setAvailableFormes(allFormes);
    }

    // Update available dosages based on selected forme and presentation
    if (selectedForme || selectedPresentation) {
      let dosagesBase = variants;
      if (selectedForme) dosagesBase = dosagesBase.filter(v => v.forme === selectedForme);
      if (selectedPresentation) dosagesBase = dosagesBase.filter(v => v.presentation === selectedPresentation);

      const availableDosages = [...new Set(dosagesBase.map(v => v.dosage).filter(Boolean))].sort();
      setAvailableDosages(availableDosages);
    } else {
      const allDosages = [...new Set(variants.map(v => v.dosage).filter(Boolean))].sort();
      setAvailableDosages(allDosages);
    }

    // Update available presentations based on selected dosage and forme
    if (selectedDosage || selectedForme) {
      let presentationsBase = variants;
      if (selectedDosage) presentationsBase = presentationsBase.filter(v => v.dosage === selectedDosage);
      if (selectedForme) presentationsBase = presentationsBase.filter(v => v.forme === selectedForme);

      const availablePresentations = [...new Set(presentationsBase.map(v => v.presentation).filter(Boolean))].sort();
      setAvailablePresentations(availablePresentations);
    } else {
      const allPresentations = [...new Set(variants.map(v => v.presentation).filter(Boolean))].sort();
      setAvailablePresentations(allPresentations);
    }
  }, [selectedDosage, selectedForme, selectedPresentation, variants]);

  const handleConfirm = () => {
    if (filteredVariants.length === 1) {
      onVariantSelect(filteredVariants[0]);
    }
  };

  const canConfirm = filteredVariants.length === 1;

  if (isLoading) {
    return (
      <div className="p-6 border rounded-lg bg-white">
        <div className="flex items-center justify-center space-x-2">
          <Loader2 className="animate-spin h-5 w-5" />
          <span>Chargement des variantes...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 border rounded-lg bg-white space-y-4">
      <div>
        <h3 className="text-lg font-medium text-navy mb-2">
          Sélectionner la variante de "{medicineName}"
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          Choisissez le dosage, la forme et la présentation spécifiques pour ce médicament.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="dosage">Dosage</Label>
          <Select value={selectedDosage} onValueChange={setSelectedDosage}>
            <SelectTrigger>
              <SelectValue placeholder="Sélectionner un dosage" />
            </SelectTrigger>
            <SelectContent>
              {availableDosages.map((dosage) => (
                <SelectItem key={dosage} value={dosage}>
                  {dosage}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="forme">Forme</Label>
          <Select value={selectedForme} onValueChange={setSelectedForme}>
            <SelectTrigger>
              <SelectValue placeholder="Sélectionner une forme" />
            </SelectTrigger>
            <SelectContent>
              {availableFormes.map((forme) => (
                <SelectItem key={forme} value={forme}>
                  {forme}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="presentation">Présentation</Label>
          <Select value={selectedPresentation} onValueChange={setSelectedPresentation}>
            <SelectTrigger>
              <SelectValue placeholder="Sélectionner une présentation" />
            </SelectTrigger>
            <SelectContent>
              {availablePresentations.map((presentation) => (
                <SelectItem key={presentation} value={presentation}>
                  {presentation}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {filteredVariants.length > 1 && (
        <div className="p-3 bg-amber-50 border border-amber-200 rounded-md">
          <p className="text-sm text-amber-800">
            {filteredVariants.length} variantes correspondent à votre sélection. 
            Veuillez affiner votre choix.
          </p>
        </div>
      )}

      {filteredVariants.length === 1 && (
        <div className="p-3 bg-green-50 border border-green-200 rounded-md">
          <p className="text-sm text-green-800 flex items-center">
            <Check className="h-4 w-4 mr-2" />
            Variante sélectionnée: {filteredVariants[0].nom} - {filteredVariants[0].dosage} - {filteredVariants[0].forme} - {filteredVariants[0].presentation}
          </p>
        </div>
      )}

      {variants.length === 0 && !isLoading && (
        <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
          <p className="text-orange-800 text-sm mb-3">
            Aucune variante trouvée pour "{medicineName}". Vous pouvez créer une entrée manuelle.
          </p>
          <Button
            onClick={() => onCreateManual?.(medicineName)}
            className="w-full bg-orange-600 hover:bg-orange-700 text-white"
          >
            <Plus className="mr-2 h-4 w-4" />
            Créer "{medicineName}" manuellement
          </Button>
        </div>
      )}

      <div className="flex space-x-3 pt-4">
        <Button
          onClick={handleConfirm}
          disabled={!canConfirm}
          className="bg-teal hover:bg-teal-dark text-white"
        >
          Confirmer la sélection
        </Button>
        <Button
          variant="outline"
          onClick={onCancel}
        >
          Annuler
        </Button>
        {onCreateManual && variants.length > 0 && (
          <Button
            variant="secondary"
            onClick={() => onCreateManual(medicineName)}
          >
            <Plus className="mr-2 h-4 w-4" />
            Entrée manuelle
          </Button>
        )}
      </div>
    </div>
  );
};

export default MedicineVariantSelector;
