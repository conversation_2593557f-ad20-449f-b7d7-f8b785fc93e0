import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../entities/medicine.dart';
import '../../repositories/medicine_repository.dart';

class UpdateMedicineUseCase {
  final MedicineRepository repository;

  UpdateMedicineUseCase(this.repository);

  Future<Either<Failure, Medicine>> call(Medicine medicine) async {
    return await repository.updateMedicine(medicine);
  }
}
