import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Medicine, Tag } from '@/types';

// Mock data for testing
const mockMedicine: Medicine = {
  id: 'test-medicine-id',
  name: 'Paracétamol 500mg',
  dosage: '500mg',
  quantity: 20,
  expiryDate: '2025-06-30',
  category: 'pain',
  tags: [
    {
      id: 'tag-1',
      name: 'antalgique',
      color: '#3182CE',
      isSystemTag: true,
      category: 'therapeutic'
    },
    {
      id: 'tag-2',
      name: 'premiers_soins',
      color: '#E53E3E',
      isSystemTag: true,
      category: 'usage'
    }
  ],
  location: 'location-1',
  locationName: 'Armoire à pharmacie',
  notes: 'Prendre avec de la nourriture',
  lowStockThreshold: 5,
  is_custom: false,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  familyMember: {
    id: 'family-1',
    name: '<PERSON>'
  }
};

const mockCustomMedicine: Medicine = {
  ...mockMedicine,
  id: 'custom-medicine-id',
  name: 'Vitamine D personnalisée',
  custom_name: 'Vitamine D personnalisée',
  is_custom: true,
  medicine_id: null
};

describe('EditMedicine Bug Fix Validation', () => {
  describe('Data Preservation', () => {
    it('should preserve all medicine data when creating medicine object', () => {
      // Simulate the medicine object creation in EditMedicine
      const medicineObj = {
        ...mockMedicine,
        tags: mockMedicine.tags || []
      };

      expect(medicineObj.id).toBe(mockMedicine.id);
      expect(medicineObj.name).toBe(mockMedicine.name);
      expect(medicineObj.dosage).toBe(mockMedicine.dosage);
      expect(medicineObj.quantity).toBe(mockMedicine.quantity);
      expect(medicineObj.expiryDate).toBe(mockMedicine.expiryDate);
      expect(medicineObj.tags).toHaveLength(2);
      expect(medicineObj.location).toBe(mockMedicine.location);
      expect(medicineObj.notes).toBe(mockMedicine.notes);
      expect(medicineObj.lowStockThreshold).toBe(mockMedicine.lowStockThreshold);
      expect(medicineObj.familyMember?.id).toBe(mockMedicine.familyMember?.id);
    });

    it('should properly initialize form data with medicine values', () => {
      // Simulate form data initialization in EditMedicine
      const formData = {
        custom_name: mockMedicine.is_custom ? (mockMedicine.custom_name || '') : '',
        quantity: mockMedicine.quantity,
        category: mockMedicine.category,
        tags: mockMedicine.tags || [],
        location: mockMedicine.location || '',
        familyMember: mockMedicine.familyMember?.id || '',
        notes: mockMedicine.notes || '',
        lowStockThreshold: mockMedicine.lowStockThreshold || 0,
        expiryDate: mockMedicine.expiryDate,
        dosage: mockMedicine.dosage || '',
      };

      expect(formData.quantity).toBe(20);
      expect(formData.tags).toHaveLength(2);
      expect(formData.location).toBe('location-1');
      expect(formData.familyMember).toBe('family-1');
      expect(formData.notes).toBe('Prendre avec de la nourriture');
      expect(formData.lowStockThreshold).toBe(5);
      expect(formData.expiryDate).toBe('2025-06-30');
      expect(formData.dosage).toBe('500mg');
    });

    it('should handle custom medicine name correctly', () => {
      // Test custom medicine form initialization
      const formData = {
        custom_name: mockCustomMedicine.is_custom ? (mockCustomMedicine.custom_name || '') : '',
        quantity: mockCustomMedicine.quantity,
        category: mockCustomMedicine.category,
        tags: mockCustomMedicine.tags || [],
        location: mockCustomMedicine.location || '',
        familyMember: mockCustomMedicine.familyMember?.id || '',
        notes: mockCustomMedicine.notes || '',
        lowStockThreshold: mockCustomMedicine.lowStockThreshold || 0,
        expiryDate: mockCustomMedicine.expiryDate,
        dosage: mockCustomMedicine.dosage || '',
      };

      expect(formData.custom_name).toBe('Vitamine D personnalisée');
    });
  });

  describe('Tag Loading', () => {
    it('should properly format loaded tags', () => {
      // Simulate tag loading from database
      const medicineTagsData = [
        {
          tag_id: 'tag-1',
          tags: {
            id: 'tag-1',
            name: 'antalgique',
            color: '#3182CE',
            is_system_tag: true,
            category: 'therapeutic'
          }
        },
        {
          tag_id: 'tag-2',
          tags: {
            id: 'tag-2',
            name: 'premiers_soins',
            color: '#E53E3E',
            is_system_tag: true,
            category: 'usage'
          }
        }
      ];

      const existingTags: Tag[] = medicineTagsData.map((mt: any) => ({
        id: mt.tags.id,
        name: mt.tags.name,
        color: mt.tags.color,
        isSystemTag: mt.tags.is_system_tag,
        category: mt.tags.category || 'therapeutic'
      }));

      expect(existingTags).toHaveLength(2);
      expect(existingTags[0].id).toBe('tag-1');
      expect(existingTags[0].name).toBe('antalgique');
      expect(existingTags[0].isSystemTag).toBe(true);
      expect(existingTags[0].category).toBe('therapeutic');
      expect(existingTags[1].category).toBe('usage');
    });

    it('should handle empty tags gracefully', () => {
      const medicineTagsData: any[] = [];
      const existingTags: Tag[] = medicineTagsData.map((mt: any) => ({
        id: mt.tags.id,
        name: mt.tags.name,
        color: mt.tags.color,
        isSystemTag: mt.tags.is_system_tag,
        category: mt.tags.category || 'therapeutic'
      }));

      expect(existingTags).toHaveLength(0);
    });
  });

  describe('Update Data Preparation', () => {
    it('should prepare update data correctly for database medicine', () => {
      const formData = {
        custom_name: '',
        quantity: 25, // Changed from 20
        category: 'pain',
        tags: mockMedicine.tags || [],
        location: 'location-2', // Changed
        familyMember: 'family-1',
        notes: 'Prendre avec de la nourriture - mise à jour', // Changed
        lowStockThreshold: 3, // Changed from 5
        expiryDate: '2025-06-30',
        dosage: '500mg',
      };

      const medicineToUpdate = {
        quantity: formData.quantity,
        expiryDate: formData.expiryDate,
        category: formData.category,
        location: formData.location || '',
        notes: formData.notes,
        lowStockThreshold: formData.lowStockThreshold,
        dosage: formData.dosage,
        familyMember: formData.familyMember ?
          { id: formData.familyMember, name: '' } : null
      };

      expect(medicineToUpdate.quantity).toBe(25);
      expect(medicineToUpdate.location).toBe('location-2');
      expect(medicineToUpdate.notes).toBe('Prendre avec de la nourriture - mise à jour');
      expect(medicineToUpdate.lowStockThreshold).toBe(3);
      // Should not include custom_name for database medicine
      expect('custom_name' in medicineToUpdate).toBe(false);
    });

    it('should include custom_name for custom medicines', () => {
      const formData = {
        custom_name: 'Vitamine D personnalisée - modifiée',
        quantity: 25,
        category: 'other',
        tags: [],
        location: 'location-1',
        familyMember: '',
        notes: '',
        lowStockThreshold: 0,
        expiryDate: '2025-06-30',
        dosage: '',
      };

      const medicineToUpdate: any = {
        quantity: formData.quantity,
        expiryDate: formData.expiryDate,
        category: formData.category,
        location: formData.location || '',
        notes: formData.notes,
        lowStockThreshold: formData.lowStockThreshold,
        dosage: formData.dosage,
        familyMember: formData.familyMember ?
          { id: formData.familyMember, name: '' } : null
      };

      // For custom medicines, add custom_name if provided
      if (mockCustomMedicine.is_custom && formData.custom_name.trim()) {
        medicineToUpdate.custom_name = formData.custom_name.trim();
      }

      expect(medicineToUpdate.custom_name).toBe('Vitamine D personnalisée - modifiée');
    });
  });

  describe('Default Value Prevention', () => {
    it('should not override existing location when medicine is loaded', () => {
      const medicine = mockMedicine;
      const formData = { location: 'location-1' };
      const locations = [{ id: 'default-location', name: 'Default' }];

      // Simulate the useEffect logic
      const shouldSetDefault = locations.length > 0 && !formData.location && !medicine;
      
      expect(shouldSetDefault).toBe(false); // Should not set default because medicine is loaded
    });

    it('should set default location only when no medicine is loaded', () => {
      const medicine = null;
      const formData = { location: '' };
      const locations = [{ id: 'default-location', name: 'Default' }];

      // Simulate the useEffect logic
      const shouldSetDefault = locations.length > 0 && !formData.location && !medicine;
      
      expect(shouldSetDefault).toBe(true); // Should set default because no medicine is loaded
    });
  });
});
