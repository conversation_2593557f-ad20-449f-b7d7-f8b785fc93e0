import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import ModernHeader from "@/components/ModernHeader";
import BottomNavigation from "@/components/BottomNavigation";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, AlertCircle, Pill, MapPin, Users, Loader2 } from "lucide-react";
import { Medicine } from "@/types";
import { formatDate, formatDateShort, isExpired, isNearExpiry } from "@/utils/helpers";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";

interface AlertsData {
  expired: Medicine[];
  expiringSoon: Medicine[];
  lowQuantity: Medicine[];
}

const Alerts = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("expired");
  const [isLoading, setIsLoading] = useState(true);
  const [alertsData, setAlertsData] = useState<AlertsData>({
    expired: [],
    expiringSoon: [],
    lowQuantity: []
  });
  const { user, householdId } = useAuth();
  
  useEffect(() => {
    if (!user || !householdId) return;
    
    const fetchAlerts = async () => {
      setIsLoading(true);
      
      try {
        // Get user_medicines data first
        const { data: userMedicinesData, error: userMedicinesError } = await supabase
          .from('user_medicines')
          .select('*')
          .eq('household_id', householdId);

        if (userMedicinesError) throw userMedicinesError;

        // Get locations data
        const { data: locationsData, error: locationsError } = await supabase
          .from('locations')
          .select('id, name')
          .eq('household_id', householdId);

        if (locationsError) throw locationsError;

        // Get family members data
        const { data: familyMembersData, error: familyMembersError } = await supabase
          .from('family_members')
          .select('id, name')
          .eq('household_id', householdId);

        if (familyMembersError) throw familyMembersError;

        // Get tunisia medicines data for non-custom medicines
        const medicineIds = userMedicinesData
          ?.filter(med => !med.is_custom && med.medicine_id)
          .map(med => med.medicine_id) || [];

        let tunisiaMedicinesData: any[] = [];
        if (medicineIds.length > 0) {
          const { data: tmData, error: tmError } = await supabase
            .from('tunisia_medicines')
            .select('*')
            .in('id', medicineIds);

          if (tmError) throw tmError;
          tunisiaMedicinesData = tmData || [];
        }

        // Create lookup maps
        const locationsMap = new Map(locationsData?.map(loc => [loc.id, loc.name]) || []);
        const familyMembersMap = new Map(familyMembersData?.map(fm => [fm.id, fm.name]) || []);
        const tunisiaMedicinesMap = new Map(tunisiaMedicinesData.map(tm => [tm.id, tm]) || []);

        // Process the data and categorize it using client-side logic
        const expired: Medicine[] = [];
        const expiringSoon: Medicine[] = [];
        const lowQuantity: Medicine[] = [];

        if (userMedicinesData) {
          userMedicinesData.forEach((item: any) => {
            // Get related data from maps
            const locationName = locationsMap.get(item.location) || 'Non spécifié';
            const familyMemberName = familyMembersMap.get(item.family_member_id) || '';
            const tunisiaMedicine = tunisiaMedicinesMap.get(item.medicine_id);

            // Determine the appropriate name for the medicine
            const medicineName = item.is_custom
              ? item.custom_name
              : (tunisiaMedicine?.nom || 'Médicament sans nom');

            const medicine: Medicine = {
              id: item.id,
              name: medicineName,
              dosage: item.dosage || tunisiaMedicine?.dosage || '',
              quantity: item.quantity || 0,
              expiryDate: item.expiration || new Date().toISOString().split('T')[0],
              category: (item.category || 'other'),
              location: item.location || '',
              locationName: locationName,
              notes: item.notes || '',
              barcode: tunisiaMedicine?.amm || '',
              medicine_id: item.medicine_id,
              custom_name: item.custom_name || undefined,
              is_custom: item.is_custom || false,
              form: tunisiaMedicine?.forme || '',
              holder: tunisiaMedicine?.laboratoire || '',
              lowStockThreshold: item.low_stock_threshold || 0,
              createdAt: item.created_at || new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              familyMember: item.family_member_id ? {
                id: item.family_member_id,
                name: familyMemberName
              } : null
            };

            // Use client-side logic for accurate categorization
            if (medicine.expiryDate && isExpired(medicine.expiryDate)) {
              expired.push(medicine);
            } else if (medicine.expiryDate && isNearExpiry(medicine.expiryDate)) {
              expiringSoon.push(medicine);
            }

            // Check for low stock using custom threshold
            const threshold = item.low_stock_threshold || 0;
            if (medicine.quantity <= threshold && medicine.quantity > 0) {
              lowQuantity.push(medicine);
            }
          });
        }
        
        setAlertsData({
          expired,
          expiringSoon,
          lowQuantity
        });
        
      } catch (err) {
        toast.error("Erreur lors du chargement des alertes");
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchAlerts();
  }, [user, householdId]);
  
  const handleMedicineClick = (medicine: Medicine) => {
    navigate(`/medicine/${medicine.id}`);
  };
  
  const getMedicineDisplayName = (medicine: Medicine) => {
    if (medicine.is_custom) {
      return medicine.custom_name || "Médicament manuel";
    } else {
      return medicine.name || "Médicament inconnu";
    }
  };
  
  const AlertItem = ({ medicine }: { medicine: Medicine }) => {
    // Check expiry status for border colors
    const expired = medicine.expiryDate ? isExpired(medicine.expiryDate) : false;
    const nearExpiry = medicine.expiryDate ? isNearExpiry(medicine.expiryDate) : false;

    // Get thick left/right border colors based on medicine status
    const getBorderColor = () => {
      if (expired) return "border-l-4 border-r-4 border-red-500";
      if (nearExpiry) return "border-l-4 border-r-4 border-amber-500";
      if (!medicine.expiryDate) return "border-l-4 border-r-4 border-gray-400";
      return "border-l-4 border-r-4 border-teal";
    };

    // Get hover background color based on status
    const getHoverBgColor = () => {
      if (expired) return "hover:bg-red-50";
      if (nearExpiry) return "hover:bg-amber-50";
      if (!medicine.expiryDate) return "hover:bg-gray-50";
      return "hover:bg-teal/5";
    };

    return (
      <div
        className={`p-4 ${getBorderColor()} ${getHoverBgColor()} rounded-lg mb-3 cursor-pointer transition-colors`}
        onClick={() => handleMedicineClick(medicine)}
      >
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <h3 className="font-bold text-navy">{getMedicineDisplayName(medicine)}</h3>
          {medicine.dosage && <p className="text-sm text-gray-600">{medicine.dosage}</p>}
          
          <div className="flex flex-wrap items-center gap-x-3 gap-y-1 mt-2 text-sm">
            <div className="flex items-center text-gray-600">
              <span>Qté: {medicine.quantity}</span>
            </div>

            <div className="flex items-center text-gray-600">
              <span>Exp: {formatDateShort(medicine.expiryDate)}</span>
            </div>
          </div>

          <div className="flex flex-wrap items-center gap-x-3 gap-y-1 mt-1 text-xs text-gray-600">
            {medicine.locationName && (
              <div className="flex items-center">
                <MapPin size={12} className="mr-1" />
                <span>{medicine.locationName}</span>
              </div>
            )}
            
            {medicine.familyMember && (
              <div className="flex items-center">
                <Users size={12} className="mr-1" />
                <span>{medicine.familyMember.name}</span>
              </div>
            )}
            
            {medicine.category && (
              <div className="flex items-center">
                <Pill size={12} className="mr-1" />
                <span className="capitalize">
                  {medicine.category === 'pain' && 'Douleur'}
                  {medicine.category === 'cold' && 'Rhume'}
                  {medicine.category === 'allergy' && 'Allergie'}
                  {medicine.category === 'digestion' && 'Digestion'}
                  {medicine.category === 'first-aid' && 'Premiers soins'}
                  {medicine.category === 'prescription' && 'Ordonnance'}
                  {medicine.category === 'other' && 'Autre'}
                </span>
              </div>
            )}
          </div>
        </div>

        {activeTab === 'expired' && (
          <AlertCircle size={20} className="text-red-500 flex-shrink-0" />
        )}
        {activeTab === 'expiring-soon' && (
          <AlertCircle size={20} className="text-amber-500 flex-shrink-0" />
        )}
        {activeTab === 'low-quantity' && (
          <Pill size={20} className="text-teal flex-shrink-0" />
        )}
      </div>
    </div>
  );
  };
  
  return (
    <div className="medicinet-container animate-fade-in pb-20">
      <ModernHeader
        title="Alertes"
        subtitle="Surveillez vos médicaments expirés et en stock bas"
        showBackButton={true}
        variant="navy"
      />

      {isLoading ? (
        <div className="flex justify-center py-12">
          <Loader2 className="animate-spin h-8 w-8 text-teal" />
        </div>
      ) : (
        <Tabs defaultValue="expired" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="expired">
              Expirés 
              <span className="ml-1 bg-muted text-muted-foreground rounded-full px-1.5 text-xs">
                {alertsData.expired.length}
              </span>
            </TabsTrigger>
            <TabsTrigger value="expiring-soon">
              Bientôt
              <span className="ml-1 bg-muted text-muted-foreground rounded-full px-1.5 text-xs">
                {alertsData.expiringSoon.length}
              </span>
            </TabsTrigger>
            <TabsTrigger value="low-quantity">
              Stock bas
              <span className="ml-1 bg-muted text-muted-foreground rounded-full px-1.5 text-xs">
                {alertsData.lowQuantity.length}
              </span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="expired">
            {alertsData.expired.length > 0 ? (
              <div className="space-y-2">
                {alertsData.expired.map((medicine) => (
                  <AlertItem key={medicine.id} medicine={medicine} />
                ))}
              </div>
            ) : (
              <div className="text-center py-10">
                <p className="text-muted-foreground">Aucun médicament expiré.</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="expiring-soon">
            {alertsData.expiringSoon.length > 0 ? (
              <div className="space-y-2">
                {alertsData.expiringSoon.map((medicine) => (
                  <AlertItem key={medicine.id} medicine={medicine} />
                ))}
              </div>
            ) : (
              <div className="text-center py-10">
                <p className="text-muted-foreground">Aucun médicament proche de l'expiration.</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="low-quantity">
            {alertsData.lowQuantity.length > 0 ? (
              <div className="space-y-2">
                {alertsData.lowQuantity.map((medicine) => (
                  <AlertItem key={medicine.id} medicine={medicine} />
                ))}
              </div>
            ) : (
              <div className="text-center py-10">
                <p className="text-muted-foreground">Aucun médicament en stock bas.</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      )}
      
      <BottomNavigation />
    </div>
  );
};

export default Alerts;
