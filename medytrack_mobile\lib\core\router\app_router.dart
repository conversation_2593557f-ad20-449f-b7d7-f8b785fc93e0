import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../presentation/bloc/auth/auth_bloc.dart';
import '../../presentation/pages/auth/auth_page.dart';
import '../../presentation/pages/onboarding/onboarding_page.dart';
import '../../presentation/pages/dashboard/dashboard_page.dart';
import '../../presentation/pages/medicine/medicine_list_page.dart';
import '../../presentation/pages/medicine/medicine_detail_page.dart';
import '../../presentation/pages/medicine/add_medicine_page.dart';
import '../../presentation/pages/medicine/edit_medicine_page.dart';
import '../../presentation/pages/search/search_page.dart';
import '../../presentation/pages/settings/settings_page.dart';
import '../../presentation/pages/profile/profile_page.dart';
import '../../presentation/pages/household/household_page.dart';
import '../../presentation/pages/locations/locations_page.dart';
import '../../presentation/pages/family/family_page.dart';
import '../../presentation/pages/alerts/alerts_page.dart';
import '../../presentation/pages/scanner/scanner_page.dart';
import '../../presentation/pages/splash/splash_page.dart';

class AppRouter {
  static final _rootNavigatorKey = GlobalKey<NavigatorState>();
  static final _shellNavigatorKey = GlobalKey<NavigatorState>();

  static GoRouter get router => _router;

  static final GoRouter _router = GoRouter(
    navigatorKey: _rootNavigatorKey,
    initialLocation: '/splash',
    debugLogDiagnostics: true,
    redirect: (context, state) {
      final authState = context.read<AuthBloc>().state;
      final isOnAuthPage = state.matchedLocation == '/auth';
      final isOnSplashPage = state.matchedLocation == '/splash';
      final isOnOnboardingPage = state.matchedLocation == '/onboarding';

      // Always allow splash page
      if (isOnSplashPage) {
        return null;
      }

      // Handle authentication states
      if (authState is AuthUnauthenticated) {
        return isOnAuthPage ? null : '/auth';
      }

      if (authState is AuthOnboardingRequired) {
        return isOnOnboardingPage ? null : '/onboarding';
      }

      if (authState is AuthAuthenticated) {
        if (isOnAuthPage || isOnOnboardingPage) {
          return '/dashboard';
        }
        return null;
      }

      // Default to auth page for unknown states
      return '/auth';
    },
    routes: [
      // Splash Route
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),

      // Auth Route
      GoRoute(
        path: '/auth',
        name: 'auth',
        builder: (context, state) => const AuthPage(),
      ),

      // Onboarding Route
      GoRoute(
        path: '/onboarding',
        name: 'onboarding',
        builder: (context, state) => const OnboardingPage(),
      ),

      // Main App Shell with Bottom Navigation
      ShellRoute(
        navigatorKey: _shellNavigatorKey,
        builder: (context, state, child) {
          return MainShell(child: child);
        },
        routes: [
          // Dashboard Route
          GoRoute(
            path: '/dashboard',
            name: 'dashboard',
            builder: (context, state) => const DashboardPage(),
          ),

          // Medicine Routes
          GoRoute(
            path: '/medicines',
            name: 'medicines',
            builder: (context, state) => const MedicineListPage(),
            routes: [
              GoRoute(
                path: '/add',
                name: 'add-medicine',
                builder: (context, state) => const AddMedicinePage(),
              ),
              GoRoute(
                path: '/:id',
                name: 'medicine-detail',
                builder: (context, state) {
                  final medicineId = state.pathParameters['id']!;
                  return MedicineDetailPage(medicineId: medicineId);
                },
                routes: [
                  GoRoute(
                    path: '/edit',
                    name: 'edit-medicine',
                    builder: (context, state) {
                      final medicineId = state.pathParameters['id']!;
                      return EditMedicinePage(medicineId: medicineId);
                    },
                  ),
                ],
              ),
            ],
          ),

          // Search Route
          GoRoute(
            path: '/search',
            name: 'search',
            builder: (context, state) => const SearchPage(),
          ),

          // Alerts Route
          GoRoute(
            path: '/alerts',
            name: 'alerts',
            builder: (context, state) => const AlertsPage(),
          ),

          // Profile Route
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
            routes: [
              GoRoute(
                path: '/settings',
                name: 'settings',
                builder: (context, state) => const SettingsPage(),
              ),
              GoRoute(
                path: '/household',
                name: 'household',
                builder: (context, state) => const HouseholdPage(),
              ),
              GoRoute(
                path: '/locations',
                name: 'locations',
                builder: (context, state) => const LocationsPage(),
              ),
              GoRoute(
                path: '/family',
                name: 'family',
                builder: (context, state) => const FamilyPage(),
              ),
            ],
          ),
        ],
      ),

      // Scanner Route (Full Screen)
      GoRoute(
        path: '/scanner',
        name: 'scanner',
        builder: (context, state) => const ScannerPage(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page non trouvée',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'La page demandée n\'existe pas.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/dashboard'),
              child: const Text('Retour à l\'accueil'),
            ),
          ],
        ),
      ),
    ),
  );
}

/// Main shell widget with bottom navigation
class MainShell extends StatelessWidget {
  final Widget child;

  const MainShell({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: child,
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _getCurrentIndex(context),
        onTap: (index) => _onItemTapped(context, index),
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard_outlined),
            activeIcon: Icon(Icons.dashboard),
            label: 'Tableau de bord',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.medication_outlined),
            activeIcon: Icon(Icons.medication),
            label: 'Médicaments',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.search_outlined),
            activeIcon: Icon(Icons.search),
            label: 'Recherche',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.notifications_outlined),
            activeIcon: Icon(Icons.notifications),
            label: 'Alertes',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person_outlined),
            activeIcon: Icon(Icons.person),
            label: 'Profil',
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.push('/medicines/add'),
        child: const Icon(Icons.add),
      ),
    );
  }

  int _getCurrentIndex(BuildContext context) {
    final location = GoRouterState.of(context).matchedLocation;
    
    if (location.startsWith('/dashboard')) return 0;
    if (location.startsWith('/medicines')) return 1;
    if (location.startsWith('/search')) return 2;
    if (location.startsWith('/alerts')) return 3;
    if (location.startsWith('/profile')) return 4;
    
    return 0;
  }

  void _onItemTapped(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go('/dashboard');
        break;
      case 1:
        context.go('/medicines');
        break;
      case 2:
        context.go('/search');
        break;
      case 3:
        context.go('/alerts');
        break;
      case 4:
        context.go('/profile');
        break;
    }
  }
}
