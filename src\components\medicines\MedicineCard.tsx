import { memo, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MapPin, Users, AlertTriangle, Package, Calendar, Plus } from "lucide-react";
import { Medicine } from "@/types";
import { formatDateShort, isExpired, isNearExpiry, getCategoryLabel } from "@/utils/helpers";
import { useUserSettings } from "@/hooks/useUserSettings";
import { cn } from "@/lib/utils";

interface MedicineCardProps {
  medicine: Medicine;
  onClick: (medicine: Medicine) => void;
  showMultipleExpiryIndicator?: boolean;
  multipleExpiryDates?: string[];
}

const MedicineCard = ({
  medicine,
  onClick,
  showMultipleExpiryIndicator = false,
  multipleExpiryDates = []
}: MedicineCardProps) => {
  const [showAllExpiryDates, setShowAllExpiryDates] = useState(false);
  const { settings } = useUserSettings();

  // Get medicine name only (without dosage and form)
  const getMedicineName = () => {
    if (medicine.is_custom) {
      return medicine.custom_name || "Médicament manuel";
    }
    return medicine.name || "Médicament";
  };

  // Get dosage and form separately
  const getDosageAndForm = () => {
    const parts = [];
    if (medicine.dosage) parts.push(medicine.dosage);
    if (medicine.form) parts.push(medicine.form);
    return parts.join(" - ");
  };



  // Check expiry status using custom threshold
  const expired = medicine.expiryDate ? isExpired(medicine.expiryDate) : false;
  const nearExpiry = medicine.expiryDate ? isNearExpiry(medicine.expiryDate, settings.expiryWarningMonths) : false;

  // Check stock status
  const lowStock = medicine.quantity <= 2;
  const outOfStock = medicine.quantity === 0;

  // Get left border color based on medicine status (removed right border)
  const getBorderColor = () => {
    if (expired) return "border-l-4 border-red-500";
    if (nearExpiry) return "border-l-4 border-amber-500";
    if (!medicine.expiryDate) return "border-l-4 border-gray-400";
    return "border-l-4 border-teal";
  };

  // Get status badge info
  const getStatusInfo = () => {
    if (expired) {
      return { text: "Expiré", className: "bg-red-100 text-red-800 border-red-200" };
    }
    if (nearExpiry) {
      return { text: "Expire bientôt", className: "bg-amber-100 text-amber-800 border-amber-200" };
    }
    return { text: "Valide", className: "bg-green-100 text-green-800 border-green-200" };
  };

  const statusInfo = getStatusInfo();

  // Get hover background color based on status
  const getHoverBgColor = () => {
    if (expired) return "hover:bg-red-50";
    if (nearExpiry) return "hover:bg-amber-50";
    if (!medicine.expiryDate) return "hover:bg-gray-50";
    return "hover:bg-teal/5";
  };

  return (
    <Card
      className={cn(
        "cursor-pointer transition-all duration-200 mb-3",
        getBorderColor(),
        getHoverBgColor()
      )}
      onClick={() => onClick(medicine)}
    >
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Primary Information Section */}
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              {/* Medicine Name - Bold, 18px, Navy */}
              <h3 className="font-bold text-navy text-lg leading-tight mb-1">
                {getMedicineName()}
              </h3>

              {/* Dosage and Form - Bold, 14px, Black */}
              {getDosageAndForm() && (
                <p className="font-bold text-black text-sm leading-tight">
                  {getDosageAndForm()}
                </p>
              )}
            </div>

            {/* Status Indicator - High Visibility */}
            <div className="flex flex-col items-end gap-1 ml-3">
              <Badge
                className={cn(
                  "text-sm font-bold",
                  statusInfo.className
                )}
              >
                {statusInfo.text}
              </Badge>

              {/* Expiration Date - MM/YY format */}
              {medicine.expiryDate && (
                <div className="text-xs text-gray-600">
                  {formatDateShort(medicine.expiryDate)}
                </div>
              )}

              {/* Multiple Expiry Indicator */}
              {showMultipleExpiryIndicator && multipleExpiryDates.length > 1 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 text-xs text-teal hover:text-teal hover:bg-teal/10"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowAllExpiryDates(!showAllExpiryDates);
                  }}
                >
                  <Calendar size={12} className="mr-1" />
                  <Plus size={8} className="absolute -top-1 -right-1" />
                  Dates multiples
                </Button>
              )}

              {medicine.is_custom && (
                <Badge className="bg-teal/10 text-teal border-teal/20 text-xs">
                  Manuel
                </Badge>
              )}
            </div>
          </div>

          {/* Secondary Information Section */}
          <div className="space-y-2">
            {/* Quantity and Location Row */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Package size={16} className={outOfStock ? "text-red-500" : lowStock ? "text-amber-500" : "text-teal"} />
                <span className={cn(
                  "text-sm font-medium",
                  outOfStock ? "text-red-500" : lowStock ? "text-amber-600" : "text-black"
                )}>
                  {medicine.quantity} {medicine.quantity > 1 ? 'unités' : 'unité'}
                </span>
              </div>

              <div className="flex items-center space-x-1">
                <MapPin size={14} className="text-teal" />
                <span className="text-sm text-black">
                  {medicine.locationName || 'Non spécifié'}
                </span>
              </div>
            </div>

            {/* Tags and Family Member Row */}
            <div className="flex items-center justify-between">
              <div className="flex flex-wrap gap-1">
                {/* Display tags if available, otherwise show category */}
                {medicine.tags && medicine.tags.length > 0 ? (
                  medicine.tags.map((tag) => (
                    <span
                      key={tag.id}
                      className="px-2 py-1 rounded-full text-xs font-medium"
                      style={{
                        backgroundColor: `${tag.color}20`,
                        color: tag.color,
                        border: `1px solid ${tag.color}40`
                      }}
                    >
                      {tag.name}
                    </span>
                  ))
                ) : (
                  <span className="bg-gray-100 px-2 py-1 rounded-full text-xs">
                    {getCategoryLabel(medicine.category)}
                  </span>
                )}
              </div>

              {medicine.familyMember && (
                <div className="flex items-center space-x-1">
                  <Users size={14} className="text-teal" />
                  <span className="text-sm text-gray-600">
                    {medicine.familyMember.name}
                  </span>
                </div>
              )}
            </div>

            {/* Stock Warning */}
            {(outOfStock || lowStock) && (
              <div className={cn(
                "flex items-center space-x-1 text-sm",
                outOfStock ? "text-red-600" : "text-amber-600"
              )}>
                <AlertTriangle size={14} />
                <span>{outOfStock ? "Rupture de stock" : "Stock bas"}</span>
              </div>
            )}
          </div>

          {/* Expanded Multiple Expiry Dates */}
          {showAllExpiryDates && multipleExpiryDates.length > 1 && (
            <div className="mt-3 p-3 bg-gray-50 rounded-lg border">
              <h4 className="text-sm font-semibold text-navy mb-2">
                Toutes les dates d'expiration:
              </h4>
              <div className="space-y-1">
                {multipleExpiryDates.map((date, index) => (
                  <div key={index} className="text-sm text-gray-700">
                    • {formatDateShort(date)}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default memo(MedicineCard);
