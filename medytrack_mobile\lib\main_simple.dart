import 'package:flutter/material.dart';

void main() {
  runApp(const MedyTrackApp());
}

class MedyTrackApp extends StatelessWidget {
  const MedyTrackApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'MedyTrack',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: const Color(0xFF2D4A8E)),
        useMaterial3: true,
      ),
      home: const MyHomePage(title: 'MedyTrack - Gestion de médicaments'),
    );
  }
}

class MyHomePage extends StatelessWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF2D4A8E),
        foregroundColor: Colors.white,
        title: Text(title),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(
              Icons.medical_services,
              size: 100,
              color: Color(0xFF0DCDB7),
            ),
            SizedBox(height: 20),
            Text(
              'Bienvenue dans MedyTrack!',
              style: TextStyle(
                fontSize: 24, 
                fontWeight: FontWeight.bold,
                color: Color(0xFF2D4A8E),
              ),
            ),
            SizedBox(height: 10),
            Text(
              'Application de gestion de médicaments',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 20),
            Text(
              '🎉 L\'application fonctionne correctement! 🎉',
              style: TextStyle(
                fontSize: 18, 
                color: Colors.green,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 30),
            Card(
              margin: EdgeInsets.symmetric(horizontal: 32),
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  children: [
                    Text(
                      '✅ Succès du débogage!',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    SizedBox(height: 10),
                    Text(
                      'L\'application MedyTrack mobile s\'exécute correctement sur l\'émulateur Android.',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
