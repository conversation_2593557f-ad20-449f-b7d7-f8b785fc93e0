
import React from "react";
import { Loader2 } from "lucide-react";
import { Medicine } from "@/types";

interface MedicineSearchResultsProps {
  isSearching: boolean;
  searchResults: Medicine[];
  onSelect: (medicine: Medicine) => void;
}

const MedicineSearchResults = ({ 
  isSearching, 
  searchResults = [], 
  onSelect 
}: MedicineSearchResultsProps) => {
  if (isSearching) {
    return (
      <div className="flex items-center justify-center p-4">
        <Loader2 className="animate-spin mr-2" />
        <span>Recherche en cours...</span>
      </div>
    );
  }

  if (searchResults.length === 0) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        Aucun résultat trouvé
      </div>
    );
  }

  return (
    <ul className="py-1 max-h-64 overflow-auto">
      {searchResults.map((medicine) => (
        <li
          key={medicine.id}
          className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
          onClick={() => onSelect(medicine)}
        >
          <div className="font-medium">{medicine.name}</div>
          <div className="text-sm text-muted-foreground">
            Cliquez pour voir les variantes disponibles
          </div>
        </li>
      ))}
    </ul>
  );
};

export default MedicineSearchResults;
