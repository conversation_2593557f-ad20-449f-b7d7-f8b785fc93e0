
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";

interface WelcomeCardProps {
  onAddClick: () => void;
}

const WelcomeCard = ({ onAddClick }: WelcomeCardProps) => {
  return (
    <Card>
      <CardContent className="p-6 text-center">
        <h3 className="text-lg font-medium mb-4 text-navy">
          Commencez par ajouter votre premier médicament
        </h3>
        <Button onClick={onAddClick} className="bg-teal hover:bg-teal-dark text-white">
          <Plus className="mr-2 h-4 w-4" />
          Ajouter maintenant
        </Button>
      </CardContent>
    </Card>
  );
};

export default WelcomeCard;
