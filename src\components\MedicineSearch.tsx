
import React from "react";
import { Input } from "@/components/ui/input";
import { Medicine } from "@/types";
import { supabase } from "@/integrations/supabase/client";
import { Loader2, Search } from "lucide-react";
import { Button } from "./ui/button";

interface MedicineSearchProps {
  onSelect: (medicine: Medicine) => void;
}

interface TunisiaMedicine {
  id: string;
  nom: string | null;
  dosage: string | null;
  forme: string | null;
  laboratoire: string | null;
  amm: string | null;
  dci: string | null;
  classe: string | null;
  sous_classe: string | null;
}

export const MedicineSearch = ({ onSelect }: MedicineSearchProps) => {
  const [searchQuery, setSearchQuery] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(false);
  const [results, setResults] = React.useState<TunisiaMedicine[]>([]);
  const [showResults, setShowResults] = React.useState(false);

  const searchMedicines = async (query: string) => {
    if (query.trim().length < 2) {
      setResults([]);
      return;
    }

    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('tunisia_medicines')
        .select('*')
        .or(`nom.ilike.%${query}%,amm.ilike.%${query}%,laboratoire.ilike.%${query}%`)
        .limit(10);

      if (error) throw error;
      setResults(data || []);
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelect = (medicine: TunisiaMedicine) => {
    const formattedMedicine: Medicine = {
      id: medicine.id,
      name: medicine.nom || '',
      dosage: medicine.dosage || '',
      quantity: 0,
      expiryDate: new Date().toISOString().split('T')[0],
      category: 'other',
      location: '',
      barcode: medicine.amm || undefined,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      notes: `Forme: ${medicine.forme || 'N/A'}${medicine.laboratoire ? ', Laboratoire: ' + medicine.laboratoire : ''}${medicine.dci ? ', DCI: ' + medicine.dci : ''}${medicine.classe ? ', Classe: ' + medicine.classe : ''}`,
    };

    onSelect(formattedMedicine);
    setSearchQuery('');
    setShowResults(false);
  };

  React.useEffect(() => {
    const timer = setTimeout(() => {
      searchMedicines(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  return (
    <div className="relative">
      <div className="relative flex items-center space-x-2">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            type="text"
            placeholder="Rechercher un médicament..."
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setShowResults(true);
            }}
            className="pl-10"
          />
        </div>
      </div>

      {showResults && (searchQuery.length >= 2) && (
        <div className="absolute z-50 w-full mt-1 bg-white border rounded-md shadow-lg max-h-60 overflow-auto">
          {isLoading ? (
            <div className="flex items-center justify-center p-4">
              <Loader2 className="animate-spin" />
            </div>
          ) : results.length > 0 ? (
            <ul className="py-1">
              {results.map((medicine) => (
                <li
                  key={medicine.id}
                  className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                  onClick={() => handleSelect(medicine)}
                >
                  <div className="font-medium">{medicine.nom}</div>
                  <div className="text-sm text-muted-foreground">
                    {medicine.dosage} - {medicine.forme}
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <div className="p-4 text-center text-muted-foreground">
              Aucun résultat trouvé
            </div>
          )}
        </div>
      )}
    </div>
  );
};
