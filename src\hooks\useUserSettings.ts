import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

export interface UserSettings {
  expiryWarningMonths: number;
}

/**
 * Custom hook for managing user-specific settings in MedyTrack
 *
 * This hook provides access to user preferences including custom expiry warning
 * thresholds. Settings are stored per-user and persist across sessions.
 *
 * @returns {Object} Hook interface containing:
 *   - settings: Current user settings object
 *   - isLoading: Loading state for settings operations
 *   - updateExpiryWarningMonths: Function to update expiry warning threshold
 */
export const useUserSettings = () => {
  const [settings, setSettings] = useState<UserSettings>({
    expiryWarningMonths: 1 // Default to 1 month
  });
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();

  const fetchSettings = async () => {
    if (!user) return;

    try {
      setIsLoading(true);

      const { data, error } = await supabase
        .from('users')
        .select('expiry_warning_days')
        .eq('id', user.id)
        .maybeSingle();

      if (error) {
        // If column doesn't exist yet, use default settings
        if (error.code === '42703') {
          setSettings({
            expiryWarningMonths: 1
          });
          return;
        }
        throw error;
      }

      if (data) {
        setSettings({
          expiryWarningMonths: data.expiry_warning_days || 1
        });
      }
    } catch (err) {
      // Don't show error toast if column doesn't exist yet
      if ((err as any)?.code !== '42703') {
        toast.error('Erreur lors du chargement des paramètres');
      }
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Updates the user's custom expiry warning threshold
   *
   * @param months - Number of months before expiry to show warnings (1-12)
   * @returns Promise<boolean> - Success status of the update operation
   */
  const updateExpiryWarningMonths = async (months: number) => {
    if (!user) return false;
    
    try {
      const { error } = await supabase
        .from('users')
        .update({ expiry_warning_days: months })
        .eq('id', user.id);
        
      if (error) throw error;
      
      setSettings(prev => ({
        ...prev,
        expiryWarningMonths: months
      }));
      
      toast.success('Paramètres mis à jour avec succès');
      return true;
    } catch (err) {
      toast.error('Erreur lors de la mise à jour des paramètres');
      return false;
    }
  };

  useEffect(() => {
    fetchSettings();
  }, [user]);

  return {
    settings,
    isLoading,
    updateExpiryWarningMonths,
    refetch: fetchSettings
  };
};
