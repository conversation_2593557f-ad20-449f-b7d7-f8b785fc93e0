import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

export interface UserSettings {
  expiryWarningMonths: number;
}

export const useUserSettings = () => {
  const [settings, setSettings] = useState<UserSettings>({
    expiryWarningMonths: 1 // Default to 1 month
  });
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();

  const fetchSettings = async () => {
    if (!user) return;

    try {
      setIsLoading(true);

      const { data, error } = await supabase
        .from('users')
        .select('expiry_warning_days')
        .eq('id', user.id)
        .maybeSingle();

      if (error) {
        // If column doesn't exist yet, use default settings
        if (error.code === '42703') {
          console.warn('expiry_warning_days column not found, using default settings');
          setSettings({
            expiryWarningMonths: 1
          });
          return;
        }
        throw error;
      }

      if (data) {
        setSettings({
          expiryWarningMonths: data.expiry_warning_days || 1
        });
      }
    } catch (err) {
      console.error('Error fetching user settings:', err);
      // Don't show error toast if column doesn't exist yet
      if ((err as any)?.code !== '42703') {
        toast.error('Erreur lors du chargement des paramètres');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const updateExpiryWarningMonths = async (months: number) => {
    if (!user) return false;
    
    try {
      const { error } = await supabase
        .from('users')
        .update({ expiry_warning_days: months })
        .eq('id', user.id);
        
      if (error) throw error;
      
      setSettings(prev => ({
        ...prev,
        expiryWarningMonths: months
      }));
      
      toast.success('Paramètres mis à jour avec succès');
      return true;
    } catch (err) {
      console.error('Error updating expiry warning months:', err);
      toast.error('Erreur lors de la mise à jour des paramètres');
      return false;
    }
  };

  useEffect(() => {
    fetchSettings();
  }, [user]);

  return {
    settings,
    isLoading,
    updateExpiryWarningMonths,
    refetch: fetchSettings
  };
};
