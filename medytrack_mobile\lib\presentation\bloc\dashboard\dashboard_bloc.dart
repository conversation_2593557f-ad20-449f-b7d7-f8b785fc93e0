import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../domain/entities/medicine.dart';
import '../../../domain/usecases/medicine/get_medicines_usecase.dart';

part 'dashboard_event.dart';
part 'dashboard_state.dart';

class DashboardBloc extends Bloc<DashboardEvent, DashboardState> {
  final GetMedicinesUseCase getMedicinesUseCase;

  DashboardBloc({
    required this.getMedicinesUseCase,
  }) : super(DashboardInitial()) {
    on<DashboardLoadRequested>(_onLoadRequested);
  }

  Future<void> _onLoadRequested(
    DashboardLoadRequested event,
    Emitter<DashboardState> emit,
  ) async {
    emit(DashboardLoading());

    final result = await getMedicinesUseCase();
    
    result.fold(
      (failure) => emit(DashboardError(failure.message)),
      (medicines) {
        final stats = _calculateStats(medicines);
        emit(DashboardLoaded(medicines, stats));
      },
    );
  }

  DashboardStats _calculateStats(List<Medicine> medicines) {
    int total = medicines.length;
    int expired = medicines.where((m) => m.isExpired).length;
    int expiringSoon = medicines.where((m) => m.isExpiringSoon).length;
    int lowStock = medicines.where((m) => m.isLowStock).length;
    int outOfStock = medicines.where((m) => m.isOutOfStock).length;

    return DashboardStats(
      total: total,
      expired: expired,
      expiringSoon: expiringSoon,
      lowStock: lowStock,
      outOfStock: outOfStock,
    );
  }
}
