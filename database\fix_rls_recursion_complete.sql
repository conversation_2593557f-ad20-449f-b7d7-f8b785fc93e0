-- Complete fix for R<PERSON> infinite recursion
-- This script completely removes the problematic policies and creates simpler ones

-- Disable <PERSON><PERSON> temporarily to clean up
ALTER TABLE household_members DISABLE ROW LEVEL SECURITY;
ALTER TABLE household_invitations DISABLE ROW LEVEL SECURITY;

-- Drop ALL existing policies to start fresh
DROP POLICY IF EXISTS "Users can view members of their household" ON household_members;
DROP POLICY IF EXISTS "Users can insert themselves as members" ON household_members;
DROP POLICY IF EXISTS "Only owners and admins can manage other members" ON household_members;
DROP POLICY IF EXISTS "Only owners and admins can manage members" ON household_members;
DROP POLICY IF EXISTS "Users can view invitations for their household" ON household_invitations;
DROP POLICY IF EXISTS "Users can create invitations for their household" ON household_invitations;
DROP POLICY IF EXISTS "Users can update invitations for their household" ON household_invitations;
DROP POLICY IF EXISTS "Users can delete invitations for their household" ON household_invitations;

-- Re-enable RLS
ALTER TABLE household_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE household_invitations ENABLE ROW LEVEL SECURITY;

-- Create simple, non-recursive policies for household_members
-- Policy 1: Users can view all members of their own household
CREATE POLICY "household_members_select_policy" ON household_members
  FOR SELECT USING (
    household_id = (SELECT household_id FROM users WHERE id = auth.uid())
  );

-- Policy 2: Users can insert themselves as members
CREATE POLICY "household_members_insert_policy" ON household_members
  FOR INSERT WITH CHECK (
    user_id = auth.uid()
  );

-- Policy 3: Users can update their own membership
CREATE POLICY "household_members_update_policy" ON household_members
  FOR UPDATE USING (
    user_id = auth.uid()
  );

-- Policy 4: Users can delete their own membership
CREATE POLICY "household_members_delete_policy" ON household_members
  FOR DELETE USING (
    user_id = auth.uid()
  );

-- Create simple policies for household_invitations
-- Policy 1: Users can view invitations for their household
CREATE POLICY "household_invitations_select_policy" ON household_invitations
  FOR SELECT USING (
    household_id = (SELECT household_id FROM users WHERE id = auth.uid())
  );

-- Policy 2: Users can create invitations for their household (simplified)
CREATE POLICY "household_invitations_insert_policy" ON household_invitations
  FOR INSERT WITH CHECK (
    household_id = (SELECT household_id FROM users WHERE id = auth.uid())
    AND invited_by = auth.uid()
  );

-- Policy 3: Users can update invitations they created
CREATE POLICY "household_invitations_update_policy" ON household_invitations
  FOR UPDATE USING (
    invited_by = auth.uid()
  );

-- Policy 4: Users can delete invitations they created
CREATE POLICY "household_invitations_delete_policy" ON household_invitations
  FOR DELETE USING (
    invited_by = auth.uid()
  );

-- Update the create_household_invitation function to be simpler
CREATE OR REPLACE FUNCTION create_household_invitation(
  p_household_id UUID,
  p_invited_by UUID,
  p_email TEXT,
  p_permissions JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_token TEXT;
  v_invitation_id UUID;
  v_expires_at TIMESTAMP WITH TIME ZONE;
  v_user_household_id UUID;
BEGIN
  -- Verify the user belongs to the household
  SELECT household_id INTO v_user_household_id
  FROM users 
  WHERE id = p_invited_by;
  
  IF v_user_household_id != p_household_id THEN
    RAISE EXCEPTION 'User does not belong to this household';
  END IF;
  
  -- Generate unique token
  v_token := encode(gen_random_bytes(32), 'base64');
  
  -- Set expiration to 7 days from now
  v_expires_at := NOW() + INTERVAL '7 days';
  
  -- Insert invitation
  INSERT INTO household_invitations (
    household_id,
    invited_by,
    email,
    token,
    expires_at,
    permissions
  ) VALUES (
    p_household_id,
    p_invited_by,
    p_email,
    v_token,
    v_expires_at,
    COALESCE(p_permissions, '{"can_add_medicines": true, "can_edit_medicines": true, "can_delete_medicines": false, "can_manage_family": false, "can_invite_others": false}'::jsonb)
  ) RETURNING id INTO v_invitation_id;
  
  RETURN v_invitation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION create_household_invitation TO authenticated;

-- Update the revoke function to be simpler
CREATE OR REPLACE FUNCTION revoke_household_invitation(
  p_invitation_id UUID,
  p_user_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  v_invitation RECORD;
BEGIN
  -- Get invitation details and verify ownership
  SELECT * INTO v_invitation
  FROM household_invitations
  WHERE id = p_invitation_id
    AND invited_by = p_user_id;
  
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- Revoke invitation
  UPDATE household_invitations
  SET status = 'revoked', updated_at = NOW()
  WHERE id = p_invitation_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION revoke_household_invitation TO authenticated;
