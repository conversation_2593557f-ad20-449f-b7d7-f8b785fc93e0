import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Medicine } from "@/types";
import { formatCompleteMedicineName } from "@/utils/medicineUtils";
import { formatDateShort, isExpired, isNearExpiry } from "@/utils/helpers";
import { AlertTriangle, Calendar, Package, MapPin, User, Info } from "lucide-react";

interface DuplicateDetectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  existingMedicine: Medicine;
  newMedicineData: {
    quantity: number;
    expiryDate: string;
    location?: string;
    locationName?: string;
    familyMember?: { id: string; name: string } | null;
  };
  onUpdateExisting: (newQuantity: number) => void;
  onCreateSeparate: () => void;
  duplicateType: 'same_expiry' | 'different_expiry';
}

const DuplicateDetectionDialog = ({
  isOpen,
  onClose,
  existingMedicine,
  newMedicineData,
  onUpdateExisting,
  onCreateSeparate,
  duplicateType
}: DuplicateDetectionDialogProps) => {
  const handleUpdateExisting = () => {
    const newTotalQuantity = existingMedicine.quantity + newMedicineData.quantity;
    onUpdateExisting(newTotalQuantity);
    onClose();
  };

  const handleCreateSeparate = () => {
    onCreateSeparate();
    onClose();
  };

  // Helper function to get expiry status
  const getExpiryStatus = (expiryDate: string) => {
    if (isExpired(expiryDate)) {
      return { text: 'Expiré', color: 'bg-red-100 text-red-800', icon: AlertTriangle };
    }
    if (isNearExpiry(expiryDate)) {
      return { text: 'Expire bientôt', color: 'bg-orange-100 text-orange-800', icon: AlertTriangle };
    }
    return { text: 'Valide', color: 'bg-green-100 text-green-800', icon: Calendar };
  };

  const existingStatus = getExpiryStatus(existingMedicine.expiryDate);
  const newStatus = getExpiryStatus(newMedicineData.expiryDate);
  const ExistingStatusIcon = existingStatus.icon;
  const NewStatusIcon = newStatus.icon;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <div className="w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <DialogTitle className="text-lg">Médicament similaire détecté</DialogTitle>
              <DialogDescription className="text-sm">
                Un médicament identique existe déjà dans votre collection
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-4">
          {/* Existing Medicine Info */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-bold text-navy">Médicament existant</h4>
              <Badge className={existingStatus.color}>
                <ExistingStatusIcon size={12} className="mr-1" />
                {existingStatus.text}
              </Badge>
            </div>
            <p className="font-bold text-sm mb-3">{formatCompleteMedicineName(existingMedicine)}</p>
            <div className="grid grid-cols-2 gap-3 text-sm text-gray-700">
              <div className="flex items-center gap-2">
                <Package size={14} className="text-blue-600" />
                <span>Quantité: <strong>{existingMedicine.quantity}</strong></span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar size={14} className="text-blue-600" />
                <span>Expire: <strong>{formatDateShort(existingMedicine.expiryDate)}</strong></span>
              </div>
              {existingMedicine.locationName && (
                <div className="flex items-center gap-2">
                  <MapPin size={14} className="text-blue-600" />
                  <span>Lieu: <strong>{existingMedicine.locationName}</strong></span>
                </div>
              )}
              {existingMedicine.familyMember?.name && (
                <div className="flex items-center gap-2">
                  <User size={14} className="text-blue-600" />
                  <span>Pour: <strong>{existingMedicine.familyMember.name}</strong></span>
                </div>
              )}
            </div>
          </div>

          {/* New Medicine Info */}
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-bold text-navy">Nouveau médicament</h4>
              <Badge className={newStatus.color}>
                <NewStatusIcon size={12} className="mr-1" />
                {newStatus.text}
              </Badge>
            </div>
            <p className="font-bold text-sm mb-3">{formatCompleteMedicineName(existingMedicine)}</p>
            <div className="grid grid-cols-2 gap-3 text-sm text-gray-700">
              <div className="flex items-center gap-2">
                <Package size={14} className="text-green-600" />
                <span>Quantité: <strong>{newMedicineData.quantity}</strong></span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar size={14} className="text-green-600" />
                <span>Expire: <strong>{formatDateShort(newMedicineData.expiryDate)}</strong></span>
              </div>
              {newMedicineData.locationName && (
                <div className="flex items-center gap-2">
                  <MapPin size={14} className="text-green-600" />
                  <span>Lieu: <strong>{newMedicineData.locationName}</strong></span>
                </div>
              )}
              {newMedicineData.familyMember?.name && (
                <div className="flex items-center gap-2">
                  <User size={14} className="text-green-600" />
                  <span>Pour: <strong>{newMedicineData.familyMember.name}</strong></span>
                </div>
              )}
            </div>
          </div>

          {/* Smart Recommendation */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-start gap-3">
              <Info className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h5 className="font-semibold text-blue-900 mb-2">Recommandation intelligente</h5>
                {duplicateType === 'same_expiry' ? (
                  <div className="space-y-2">
                    <p className="text-sm text-blue-800">
                      ✅ <strong>Dates d'expiration identiques</strong> - Fusion recommandée
                    </p>
                    <p className="text-xs text-blue-700">
                      Mettre à jour la quantité permet un meilleur suivi et évite la duplication.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <p className="text-sm text-orange-800">
                      ⚠️ <strong>Dates d'expiration différentes</strong> - Entrées séparées recommandées
                    </p>
                    <p className="text-xs text-orange-700">
                      Garder des entrées séparées permet un meilleur suivi des dates d'expiration.
                    </p>
                  </div>
                )}
                <div className="mt-3 p-2 bg-white/50 rounded text-xs text-gray-600">
                  <strong>Résultat si fusion:</strong> {existingMedicine.quantity + newMedicineData.quantity} unités au total
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-3 pt-4">
          <Button variant="outline" onClick={onClose} className="order-3 sm:order-1">
            Annuler
          </Button>
          <Button
            variant="secondary"
            onClick={handleUpdateExisting}
            className="bg-blue-500 hover:bg-blue-600 text-white order-1 sm:order-2"
          >
            <Package className="w-4 h-4 mr-2" />
            Fusionner les quantités
            <Badge variant="secondary" className="ml-2 bg-white/20 text-white">
              {existingMedicine.quantity + newMedicineData.quantity}
            </Badge>
          </Button>
          <Button
            onClick={handleCreateSeparate}
            className="bg-teal hover:bg-teal-dark text-white order-2 sm:order-3"
          >
            <Calendar className="w-4 h-4 mr-2" />
            Garder séparé
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DuplicateDetectionDialog;
