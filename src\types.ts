
// Define our app's data types

export interface Medicine {
  id: string;
  name: string;
  dosage?: string;
  quantity: number;
  expiryDate: string;
  category: Category; // Keep for backward compatibility during transition
  tags?: Tag[]; // New tagging system
  location: string;
  locationName?: string;
  imageUrl?: string;
  notes?: string;
  barcode?: string | null;
  medicine_id?: string | null;
  createdAt: string;
  updatedAt: string;
  familyMember?: FamilyMember | null;
  price?: number;
  reimbursement_rate?: string;
  form?: string;
  presentation?: string;
  holder?: string;
  custom_name?: string;
  is_custom?: boolean;
  indications?: string;
  lowStockThreshold?: number;
}

export type Category =
  | "pain"
  | "cold"
  | "allergy"
  | "digestion"
  | "first-aid"
  | "prescription"
  | "other";

export interface Tag {
  id: string;
  name: string;
  color: string;
  isSystemTag: boolean;
  category: 'therapeutic' | 'usage'; // therapeutic = Classes Thérapeutiques, usage = Domaines d'Usage
  medicineCount?: number;
}

export interface Location {
  id: string;
  name: string;
  description?: string;
  icon: string;
}

export interface User {
  id: string;
  name: string;
  avatar?: string;
  isAdmin: boolean;
}

export interface FamilyMember {
  id: string;
  name: string;
  relation?: string;
  birthDate?: string;
  avatar?: string;
}

export interface DatabaseMedicine {
  id: string;
  nom: string | null;
  dosage: string | null;
  forme: string | null;
  presentation: string | null;
  laboratoire: string | null;
  amm: string | null;
  dci: string | null;
  classe: string | null;
  sous_classe: string | null;
}

export interface DashboardData {
  totalMedicines: number;
  expiringSoon: number;
  tagCount: number;
  locationCount: number;
  totalLocations: number;
  totalTags: number;
  locationUsagePercentage: number;
  tagUsagePercentage: number;
}

export interface MedicineExpiringSoon {
  id: string;
  label: string | null;
  expirationDate: string | null;
  quantity: number | null;
  familyMemberName: string | null;
  location: string | null;
}
