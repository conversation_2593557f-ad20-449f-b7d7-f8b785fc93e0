
import React from "react";
import { Plus } from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { MapPin, Users, Pill } from "lucide-react";

const FloatingActionButton = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Use navy blue on home page, teal on other pages
  const isHomePage = location.pathname === '/';
  const buttonColor = isHomePage ? 'bg-navy hover:bg-navy-dark' : 'bg-teal hover:bg-teal-dark';

  // Don't show FAB on my-medicines page as it has its own FAB
  if (location.pathname === '/my-medicines') {
    return null;
  }

  const menuItems = [
    {
      icon: Pill,
      label: "Ajouter un médicament",
      action: () => navigate("/add-medicine"),
      color: "text-teal",
    },
    {
      icon: MapPin,
      label: "Ajouter un emplacement",
      action: () => navigate("/locations?add=true"),
      color: "text-navy",
    },
    {
      icon: Users,
      label: "Ajouter un membre",
      action: () => navigate("/family?add=true"),
      color: "text-teal-dark",
    },
  ];

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          className={`fixed bottom-20 right-4 h-14 w-14 rounded-full shadow-lg z-10 ${buttonColor} text-white`}
          aria-label="Ajouter"
        >
          <Plus size={24} />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-56 p-0" align="end" side="top" sideOffset={16}>
        <div className="flex flex-col">
          {menuItems.map((item, index) => (
            <Button
              key={index}
              variant="ghost"
              className="justify-start py-6"
              onClick={item.action}
            >
              <item.icon className={`mr-2 h-5 w-5 ${item.color}`} />
              {item.label}
            </Button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default FloatingActionButton;
