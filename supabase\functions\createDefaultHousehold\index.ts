
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.4';

serve(async (req) => {
  // Create a Supabase client with the Auth context of the logged in user
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
  );

  try {
    // Get the user ID from the request
    const { userId, name } = await req.json();

    if (!userId) {
      return new Response(
        JSON.stringify({ error: 'User ID is required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Create a new household with default name
    const { data: household, error: householdError } = await supabaseClient
      .from('households')
      .insert({
        name: 'Mon foyer',
        created_by: userId
      })
      .select()
      .single();

    if (householdError) {
      console.error('Error creating household:', householdError);
      throw householdError;
    }

    // Update the user with the household ID
    const { error: userError } = await supabaseClient
      .from('users')
      .update({ household_id: household.id })
      .eq('id', userId);

    if (userError) {
      console.error('Error updating user:', userError);
      throw userError;
    }

    return new Response(
      JSON.stringify({ 
        message: 'Default household created successfully',
        household_id: household.id
      }),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error in createDefaultHousehold function:', error);
    
    return new Response(
      JSON.stringify({ error: error.message || 'Internal Server Error' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
});
