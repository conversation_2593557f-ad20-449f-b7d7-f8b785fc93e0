
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import ProtectedRoute from "@/components/ProtectedRoute";

// Pages
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import AddMedicine from "./pages/AddMedicine";
import MedicineDetail from "./pages/MedicineDetail";
import Alerts from "./pages/Alerts";
import Search from "./pages/Search";
import Settings from "./pages/Settings";
import Scan from "./pages/Scan";
import SpecialtySearch from "./pages/SpecialtySearch";
import Auth from "./pages/Auth";
import Dashboard from "./pages/Dashboard";
import FamilyManager from "./pages/FamilyManager";
import Locations from "./pages/Locations";
import Profile from "./pages/Profile";
import HouseholdSettings from "./pages/HouseholdSettings";
import PasswordReset from "./pages/PasswordReset";
import Onboarding from "./pages/Onboarding";
import OnboardingRoute from "./components/OnboardingRoute";

import MyMedicines from "./pages/MyMedicines";
import EditMedicine from "./pages/EditMedicine";
import JoinHousehold from "./pages/JoinHousehold";
import MedicineGroupDetail from "./pages/MedicineGroupDetail";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AuthProvider>
          <Routes>
            <Route path="/auth" element={<Auth />} />
            <Route path="/auth/callback" element={<PasswordReset />} />
            <Route path="/onboarding" element={<OnboardingRoute><Onboarding /></OnboardingRoute>} />

            <Route path="/my-medicines" element={<ProtectedRoute><MyMedicines /></ProtectedRoute>} />
            <Route path="/" element={<ProtectedRoute><Index /></ProtectedRoute>} />
            <Route path="/add-medicine" element={<ProtectedRoute><AddMedicine /></ProtectedRoute>} />
            <Route path="/edit-medicine/:id" element={<ProtectedRoute><EditMedicine /></ProtectedRoute>} />
            <Route path="/medicine/:id" element={<ProtectedRoute><MedicineDetail /></ProtectedRoute>} />
            <Route path="/medicine-group/:groupKey" element={<ProtectedRoute><MedicineGroupDetail /></ProtectedRoute>} />
            <Route path="/alerts" element={<ProtectedRoute><Alerts /></ProtectedRoute>} />
            <Route path="/search" element={<ProtectedRoute><Search /></ProtectedRoute>} />
            <Route path="/specialty-search" element={<ProtectedRoute><SpecialtySearch /></ProtectedRoute>} />
            <Route path="/settings" element={<ProtectedRoute><Settings /></ProtectedRoute>} />
            <Route path="/scan" element={<ProtectedRoute><Scan /></ProtectedRoute>} />
            <Route path="/dashboard" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
            <Route path="/family" element={<ProtectedRoute><FamilyManager /></ProtectedRoute>} />
            <Route path="/locations" element={<ProtectedRoute><Locations /></ProtectedRoute>} />
            <Route path="/profile" element={<ProtectedRoute><Profile /></ProtectedRoute>} />
            <Route path="/household-settings" element={<ProtectedRoute><HouseholdSettings /></ProtectedRoute>} />
            <Route path="/join-household" element={<JoinHousehold />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
