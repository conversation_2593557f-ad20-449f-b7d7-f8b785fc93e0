
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { format } from "date-fns";
import { AlertTriangle, ArrowLeft, Calendar, CircleUser, LogOut, Mail, Moon, Shield, Trash2, Upload, X } from "lucide-react";
import DeleteAccountDialog from "@/components/profile/DeleteAccountDialog";
import ProfileAvatar from "@/components/profile/ProfileAvatar";
import { fr, enUS } from "date-fns/locale";
import { Badge } from "@/components/ui/badge";
import PasswordChangeForm from "@/components/profile/PasswordChangeForm";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import ModernHeader from "@/components/ModernHeader";

const Profile = () => {
  const navigate = useNavigate();
  const { user, profile, signOut, loading: authLoading } = useAuth();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isPasswordFormVisible, setIsPasswordFormVisible] = useState(false);
  const [isEmailEditing, setIsEmailEditing] = useState(false);
  const [newEmail, setNewEmail] = useState("");
  const [isUpdatingEmail, setIsUpdatingEmail] = useState(false);
  const [language, setLanguage] = useState("fr");
  const [darkMode, setDarkMode] = useState(false);
  const [sessionExpiry, setSessionExpiry] = useState<Date | null>(null);

  // Get session expiry
  useEffect(() => {
    const getSession = async () => {
      const { data } = await supabase.auth.getSession();
      if (data.session?.expires_at) {
        const expiryDate = new Date(data.session.expires_at * 1000);
        setSessionExpiry(expiryDate);
      }
    };
    getSession();
  }, []);



  const formatDate = (dateString: string, lang: string = language) => {
    try {
      const date = new Date(dateString);
      return format(date, "PPpp", { locale: lang === "fr" ? fr : enUS });
    } catch (error) {
      return "Date inconnue";
    }
  };

  const handleEmailChange = async () => {
    if (!newEmail) {
      toast.error("Veuillez saisir une adresse email valide");
      return;
    }
    
    setIsUpdatingEmail(true);
    try {
      const { error } = await supabase.auth.updateUser({
        email: newEmail
      });
      
      if (error) {
        toast.error("Erreur lors de la mise à jour de l'email", {
          description: error.message
        });
      } else {
        toast.success("Email mis à jour", {
          description: "Un lien de confirmation a été envoyé à votre nouvelle adresse email."
        });
        setIsEmailEditing(false);
      }
    } catch (error) {
      toast.error("Une erreur est survenue");
    } finally {
      setIsUpdatingEmail(false);
    }
  };

  const handleLogout = async () => {
    await signOut();
    navigate('/auth');
  };

  if (authLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  if (!user) {
    navigate('/auth');
    return null;
  }

  return (
    <div className="medicinet-container animate-fade-in pb-20">
      <ModernHeader
        title="Mon Profil"
        subtitle="Gérez votre compte et vos préférences personnelles"
        showBackButton={true}
        variant="navy"
      />

      <div className="space-y-6">
        {/* Avatar & Profile */}
        <Card className="border-teal/20">
          <CardHeader>
            <CardTitle className="flex items-center text-navy">
              <CircleUser className="mr-2 h-5 w-5 text-teal" />
              Avatar & Profil
            </CardTitle>
            <CardDescription>Gérez votre photo de profil et vos informations personnelles</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row items-center gap-6">
              <div className="flex-shrink-0">
                <ProfileAvatar user={user} />
              </div>
              <div className="space-y-2 w-full">
                <h3 className="font-medium text-lg text-navy">{profile?.name || user.email}</h3>
                <p className="text-muted-foreground text-sm">{user.email}</p>
                {user.user_metadata?.full_name && (
                  <p className="text-sm">{user.user_metadata.full_name}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Account Info */}
        <Card className="border-navy/20">
          <CardHeader>
            <CardTitle className="flex items-center text-navy">
              <Calendar className="mr-2 h-5 w-5 text-teal" />
              Informations du compte
            </CardTitle>
            <CardDescription>Détails sur votre compte MédiCabinet</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Date de création</p>
                <p>{user.created_at ? formatDate(user.created_at) : 'Inconnue'}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Dernière connexion</p>
                <p>{user.last_sign_in_at ? formatDate(user.last_sign_in_at) : 'Inconnue'}</p>
              </div>
            </div>
            
            <Separator />
            
            <div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Statut de session</p>
                  <div className="flex items-center mt-1">
                    <Badge variant={user ? "default" : "destructive"} className="mr-2">
                      {user ? "Connecté" : "Déconnecté"}
                    </Badge>
                    {sessionExpiry && (
                      <span className="text-xs text-muted-foreground">
                        Expire le {formatDate(sessionExpiry.toISOString())}
                      </span>
                    )}
                  </div>
                </div>
                <Button variant="ghost" size="sm" onClick={handleLogout} className="text-teal hover:bg-teal/10">
                  <LogOut className="mr-2 h-4 w-4" />
                  Déconnexion
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Preferences */}
        <Card className="border-teal/20">
          <CardHeader>
            <CardTitle className="flex items-center text-navy">
              <Moon className="mr-2 h-5 w-5 text-teal" />
              Préférences
            </CardTitle>
            <CardDescription>Personnalisez votre expérience</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="language">Langue</Label>
                <p className="text-sm text-muted-foreground">Choisissez la langue de l'application</p>
              </div>
              <Select value={language} onValueChange={setLanguage}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Langue" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fr">Français</SelectItem>
                  <SelectItem value="en">English</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <Separator />
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="dark-mode">Mode sombre</Label>
                <p className="text-sm text-muted-foreground">Basculer entre les thèmes clair et sombre</p>
              </div>
              <Switch 
                id="dark-mode" 
                checked={darkMode}
                onCheckedChange={setDarkMode}
              />
            </div>
          </CardContent>
        </Card>



        {/* Security */}
        <Card className="border-navy/20">
          <CardHeader>
            <CardTitle className="flex items-center text-navy">
              <Shield className="mr-2 h-5 w-5 text-teal" />
              Sécurité
            </CardTitle>
            <CardDescription>Gérez vos identifiants de connexion</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="email">Adresse email</Label>
              {!isEmailEditing ? (
                <div className="flex items-center justify-between mt-1">
                  <p className="text-sm">{user.email}</p>
                  <Button variant="outline" size="sm" onClick={() => {
                    setIsEmailEditing(true);
                    setNewEmail(user.email || "");
                  }}>
                    Modifier
                  </Button>
                </div>
              ) : (
                <div className="flex items-center gap-2 mt-1">
                  <Input
                    id="email"
                    type="email"
                    value={newEmail}
                    onChange={(e) => setNewEmail(e.target.value)}
                    disabled={isUpdatingEmail}
                    className="max-w-md"
                  />
                  <Button variant="outline" size="icon" onClick={() => setIsEmailEditing(false)} disabled={isUpdatingEmail}>
                    <X className="h-4 w-4" />
                  </Button>
                  <Button onClick={handleEmailChange} disabled={isUpdatingEmail} className="bg-teal hover:bg-teal-dark text-white">
                    {isUpdatingEmail ? 'Mise à jour...' : 'Enregistrer'}
                  </Button>
                </div>
              )}
            </div>
            
            <Separator />
            
            <div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="password">Mot de passe</Label>
                  <p className="text-sm text-muted-foreground">Modifier votre mot de passe</p>
                </div>
                <Button 
                  variant="outline" 
                  onClick={() => setIsPasswordFormVisible(!isPasswordFormVisible)}
                >
                  {isPasswordFormVisible ? 'Annuler' : 'Modifier'}
                </Button>
              </div>
              {isPasswordFormVisible && (
                <div className="mt-4">
                  <PasswordChangeForm />
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Danger Zone */}
        <Card className="border-destructive/20">
          <CardHeader>
            <CardTitle className="flex items-center text-destructive">
              <AlertTriangle className="mr-2 h-5 w-5" />
              Zone de danger
            </CardTitle>
            <CardDescription>Actions irréversibles pour votre compte</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Supprimer mon compte</h3>
                <p className="text-sm text-muted-foreground">
                  Toutes vos données personnelles seront supprimées définitivement
                </p>
              </div>
              <Button 
                variant="destructive"
                onClick={() => setIsDeleteDialogOpen(true)}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Supprimer mon compte
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <DeleteAccountDialog 
        isOpen={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        userId={user.id}
      />
    </div>
  );
};

export default Profile;
