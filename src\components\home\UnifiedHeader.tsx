import React from "react";
import { <PERSON> } from "react-router-dom";
import { Bell } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import ProfileMenu from "@/components/ProfileMenu";
import { useNotificationCount } from "@/hooks/useNotificationCount";
import featureFlags from "@/config/featureFlags";

interface UnifiedHeaderProps {
  userName?: string;
  showNotifications?: boolean;
  showAvatar?: boolean;
}

const UnifiedHeader = ({
  userName,
  showNotifications = true,
  showAvatar = true,
}: UnifiedHeaderProps) => {
  const { notificationCount } = useNotificationCount();
  // Time-based greeting logic from ProfileStats
  const getTimeBasedGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Bonjour";
    if (hour < 18) return "Bon après-midi";
    return "Bonsoir";
  };

  // Only show notifications if the feature flag is enabled
  const displayNotifications = showNotifications && featureFlags.enableNotifications;

  return (
    <div className="bg-navy rounded-xl p-6 text-white mb-6">
      <div className="flex justify-between items-start">
        {/* Left side - Greeting */}
        <div className="flex-1">
          <h1 className="text-xl font-semibold">
            {getTimeBasedGreeting()}, {userName || "Utilisateur"} 👋
          </h1>
          <p className="text-navy-light mt-1 opacity-90">
            Bienvenue dans votre espace MedyTrack
          </p>
        </div>

        {/* Right side - Navigation elements */}
        <div className="flex items-center gap-4 ml-4">
          {displayNotifications && (
            <Link to="/alerts" className="relative">
              <Bell size={22} className="text-white hover:text-navy-light transition-colors" />
              {notificationCount > 0 && (
                <Badge
                  variant="destructive"
                  className="absolute -top-2 -right-2 w-5 h-5 p-0 flex items-center justify-center rounded-full"
                >
                  {notificationCount}
                </Badge>
              )}
            </Link>
          )}
          
          {showAvatar && <ProfileMenu />}
        </div>
      </div>
    </div>
  );
};

export default UnifiedHeader;
