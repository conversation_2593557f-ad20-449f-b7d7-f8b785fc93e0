
import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { TunisiaMedicine } from '@/hooks/useSpecialtySearch';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';

interface SpecialtyDetailsProps {
  specialty: TunisiaMedicine;
  isOpen: boolean;
  onClose: () => void;
}

const SpecialtyDetails = ({ specialty, isOpen, onClose }: SpecialtyDetailsProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{specialty.nom || 'Détails du médicament'}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium text-muted-foreground">AMM</h4>
              <p>{specialty.amm || 'Non spécifié'}</p>
            </div>

            <div>
              <h4 className="text-sm font-medium text-muted-foreground">Forme</h4>
              <p>{specialty.forme || 'Non spécifié'}</p>
            </div>

            <div>
              <h4 className="text-sm font-medium text-muted-foreground">Dosage</h4>
              <p>{specialty.dosage || 'Non spécifié'}</p>
            </div>

            <div>
              <h4 className="text-sm font-medium text-muted-foreground">Laboratoire</h4>
              <p>{specialty.laboratoire || 'Non spécifié'}</p>
            </div>

            <div>
              <h4 className="text-sm font-medium text-muted-foreground">Date AMM</h4>
              <p>{specialty.date_amm ? format(new Date(specialty.date_amm), 'dd/MM/yyyy') : 'Non spécifié'}</p>
            </div>

            <div>
              <h4 className="text-sm font-medium text-muted-foreground">DCI</h4>
              <p>{specialty.dci || 'Non spécifié'}</p>
            </div>

            <div>
              <h4 className="text-sm font-medium text-muted-foreground">Classe</h4>
              <p>{specialty.classe || 'Non spécifié'}</p>
            </div>

            <div>
              <h4 className="text-sm font-medium text-muted-foreground">Sous-classe</h4>
              <p>{specialty.sous_classe || 'Non spécifié'}</p>
            </div>

            <div>
              <h4 className="text-sm font-medium text-muted-foreground">Présentation</h4>
              <p>{specialty.presentation || 'Non spécifié'}</p>
            </div>

            <div>
              <h4 className="text-sm font-medium text-muted-foreground">Conditionnement primaire</h4>
              <p>{specialty.conditionnement_primaire || 'Non spécifié'}</p>
            </div>

            {specialty.indications && (
              <div className="col-span-2">
                <h4 className="text-sm font-medium text-muted-foreground">Indications</h4>
                <p>{specialty.indications}</p>
              </div>
            )}

            {specialty.duree_de_conservation && (
              <div>
                <h4 className="text-sm font-medium text-muted-foreground">Durée de conservation</h4>
                <p>{specialty.duree_de_conservation}</p>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SpecialtyDetails;
