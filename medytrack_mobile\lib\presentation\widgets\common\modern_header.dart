import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lucide_icons/lucide_icons.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../bloc/auth/auth_bloc.dart';

/// Modern header component that mirrors the web app's ModernHeader
/// Features navy blue gradient background, profile avatar, and notification bell
class ModernHeader extends StatelessWidget implements PreferredSizeWidget {
  final String greeting;
  final String? subtitle;
  final bool showNotificationBadge;
  final int notificationCount;
  final VoidCallback? onNotificationTap;
  final VoidCallback? onProfileTap;
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  const ModernHeader({
    super.key,
    required this.greeting,
    this.subtitle,
    this.showNotificationBadge = false,
    this.notificationCount = 0,
    this.onNotificationTap,
    this.onProfileTap,
    this.showBackButton = false,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: AppColors.navyGradient,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(20, 16, 20, 24),
          child: Row(
            children: [
              // Back button (if needed)
              if (showBackButton) ...[
                IconButton(
                  onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                  icon: const Icon(
                    LucideIcons.arrowLeft,
                    color: Colors.white,
                    size: 24,
                  ),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.white.withOpacity(0.2),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
              ],
              
              // Greeting and subtitle
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      greeting,
                      style: AppTextStyles.greeting,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (subtitle != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        subtitle!,
                        style: AppTextStyles.subGreeting,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Notification bell with badge
              Stack(
                children: [
                  IconButton(
                    onPressed: onNotificationTap,
                    icon: const Icon(
                      LucideIcons.bell,
                      color: Colors.white,
                      size: 22,
                    ),
                    style: IconButton.styleFrom(
                      backgroundColor: AppColors.navy,
                      shape: const CircleBorder(),
                      padding: const EdgeInsets.all(12),
                    ),
                  ),
                  if (showNotificationBadge && notificationCount > 0)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: const BoxDecoration(
                          color: AppColors.error,
                          shape: BoxShape.circle,
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          notificationCount > 99 ? '99+' : notificationCount.toString(),
                          style: AppTextStyles.overline.copyWith(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              ),
              
              const SizedBox(width: 12),
              
              // Profile avatar
              BlocBuilder<AuthBloc, AuthState>(
                builder: (context, state) {
                  String initials = 'U';
                  if (state is AuthAuthenticated && state.user.firstName.isNotEmpty) {
                    initials = state.user.firstName[0].toUpperCase();
                    if (state.user.lastName.isNotEmpty) {
                      initials += state.user.lastName[0].toUpperCase();
                    }
                  }
                  
                  return GestureDetector(
                    onTap: onProfileTap,
                    child: Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        color: AppColors.teal,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.white,
                          width: 2,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          initials,
                          style: AppTextStyles.titleMedium.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(120);
}

/// Factory methods for common header configurations
extension ModernHeaderFactory on ModernHeader {
  /// Creates a dashboard header with personalized greeting
  static ModernHeader dashboard({
    required String userName,
    int notificationCount = 0,
    VoidCallback? onNotificationTap,
    VoidCallback? onProfileTap,
  }) {
    return ModernHeader(
      greeting: 'Bonjour, $userName',
      subtitle: 'Gérez vos médicaments facilement',
      showNotificationBadge: notificationCount > 0,
      notificationCount: notificationCount,
      onNotificationTap: onNotificationTap,
      onProfileTap: onProfileTap,
    );
  }

  /// Creates a page header with title and back button
  static ModernHeader page({
    required String title,
    String? subtitle,
    VoidCallback? onBackPressed,
    VoidCallback? onProfileTap,
  }) {
    return ModernHeader(
      greeting: title,
      subtitle: subtitle,
      showBackButton: true,
      onBackPressed: onBackPressed,
      onProfileTap: onProfileTap,
    );
  }

  /// Creates a simple header with just title
  static ModernHeader simple({
    required String title,
    VoidCallback? onProfileTap,
  }) {
    return ModernHeader(
      greeting: title,
      onProfileTap: onProfileTap,
    );
  }
}
