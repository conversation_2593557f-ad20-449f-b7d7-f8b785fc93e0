-- Fix profiles table missing column
-- Add onboarding_completed column to profiles table

ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS onboarding_completed BOOLEAN DEFAULT false;

-- Update existing users to have onboarding completed if they have a household
UPDATE profiles 
SET onboarding_completed = true 
WHERE id IN (
  SELECT id FROM users WHERE household_id IS NOT NULL
);

-- Add comment to explain the column
COMMENT ON COLUMN profiles.onboarding_completed IS 'Indicates if the user has completed the onboarding process';

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_profiles_onboarding_completed 
ON profiles(onboarding_completed);
