
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { QrCode, Pencil } from "lucide-react";
import featureFlags from "@/config/featureFlags";

interface MedicineAddTabsProps {
  activeTab: 'scan' | 'manual';
  onTabChange: (tab: 'scan' | 'manual') => void;
}

const MedicineAddTabs = ({ activeTab, onTabChange }: MedicineAddTabsProps) => {
  // Only show scan tab if the feature flag is enabled
  const showScanTab = featureFlags.showScanFeature;
  
  return (
    <Tabs value={activeTab} className="mb-4" onValueChange={(value) => onTabChange(value as 'scan' | 'manual')}>
      <TabsList className="w-full">
        <TabsTrigger value="manual" className="flex-1 flex items-center">
          <Pencil size={16} className="mr-2" />
          <span><PERSON><PERSON> manuelle</span>
        </TabsTrigger>
        
        {showScanTab && (
          <TabsTrigger value="scan" className="flex-1 flex items-center">
            <QrCode size={16} className="mr-2" />
            <span>Scanner un code-barres</span>
          </TabsTrigger>
        )}
      </TabsList>
    </Tabs>
  );
};

export default MedicineAddTabs;
