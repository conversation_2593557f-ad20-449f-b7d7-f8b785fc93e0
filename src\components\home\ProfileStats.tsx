
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Pill } from "lucide-react";

interface ProfileStatsProps {
  userName?: string;
  medicineCount: number;
}

const ProfileStats = ({ 
  userName, 
  medicineCount
}: ProfileStatsProps) => {
  const getTimeBasedGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Bonjour";
    if (hour < 18) return "Bon après-midi";
    return "Bonsoir";
  };

  return (
    <div className="space-y-4">
      {/* Navy blue greeting section */}
      <div className="bg-navy rounded-xl p-6 text-white">
        <h2 className="text-xl font-semibold">
          {getTimeBasedGreeting()}, {userName || "Utilisateur"} 👋
        </h2>
        <p className="text-navy-light mt-1 opacity-90">
          Bienvenue dans votre espace MedyTrack
        </p>
      </div>

      {/* Medicine count card */}
      <Card className="border-teal/20">
        <CardContent className="pt-6 text-center">
          <div className="w-12 h-12 bg-teal/10 rounded-full flex items-center justify-center mx-auto mb-3">
            <Pill className="h-6 w-6 text-teal" />
          </div>
          <div className="text-3xl font-bold text-navy mb-1">{medicineCount}</div>
          <div className="text-sm text-gray-600">Médicaments dans votre armoire</div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileStats;
