
import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface FamilyMemberFormProps {
  name: string;
  relation: string;
  birthDate: string;
  setName: (name: string) => void;
  setRelation: (relation: string) => void;
  setBirthDate: (birthDate: string) => void;
  relationOptions: Array<{ value: string; label: string; }>;
}

const FamilyMemberForm = ({
  name,
  relation,
  birthDate,
  setName,
  setRelation,
  setBirthDate,
  relationOptions
}: FamilyMemberFormProps) => {
  return (
    <div className="space-y-4 py-4">
      <div className="space-y-2">
        <Label htmlFor="name">Prénom & Nom</Label>
        <Input
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Entrez le nom complet"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="relation">Relation</Label>
        <Select value={relation} onValueChange={setRelation}>
          <SelectTrigger>
            <SelectValue placeholder="Sélectionnez une relation" />
          </SelectTrigger>
          <SelectContent>
            {relationOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-2">
        <Label htmlFor="birthDate">Date de naissance</Label>
        <Input
          id="birthDate"
          type="date"
          value={birthDate}
          onChange={(e) => setBirthDate(e.target.value)}
        />
      </div>
    </div>
  );
};

export default FamilyMemberForm;
