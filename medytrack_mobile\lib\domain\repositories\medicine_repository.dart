import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/medicine.dart';

abstract class MedicineRepository {
  Future<Either<Failure, List<Medicine>>> getMedicines();
  Future<Either<Failure, Medicine>> addMedicine(Medicine medicine);
  Future<Either<Failure, Medicine>> updateMedicine(Medicine medicine);
  Future<Either<Failure, void>> deleteMedicine(String id);
}
