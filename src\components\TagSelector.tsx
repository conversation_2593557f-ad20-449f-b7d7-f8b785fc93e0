import React, { useState } from "react";
import { Tag } from "@/types";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { Check, X } from "lucide-react";
import { useTags } from "@/hooks/useTags";
import { cn } from "@/lib/utils";

interface TagSelectorProps {
  selectedTags: Tag[];
  onTagsChange: (tags: Tag[]) => void;
  placeholder?: string;
  className?: string;
}

/**
 * TagSelector Component for Pharmaceutical Tags
 *
 * This component provides a dropdown interface for selecting from predefined
 * pharmaceutical tags organized into therapeutic classes and usage domains.
 * Users can select multiple tags but cannot create custom tags.
 */

const TagSelector: React.FC<TagSelectorProps> = ({
  selectedTags,
  onTagsChange,
  placeholder = "Sélectionner des étiquettes pharmaceutiques...",
  className
}) => {
  const { tags, isLoading } = useTags();
  const [open, setOpen] = useState(false);

  const handleTagSelect = (tag: Tag) => {
    const isSelected = selectedTags.some(t => t.id === tag.id);
    
    if (isSelected) {
      onTagsChange(selectedTags.filter(t => t.id !== tag.id));
    } else {
      onTagsChange([...selectedTags, tag]);
    }
  };

  const handleRemoveTag = (tagId: string) => {
    onTagsChange(selectedTags.filter(t => t.id !== tagId));
  };



  // Organize pharmaceutical tags by category for better UX
  const therapeuticTags = tags.filter(tag => tag.category === 'therapeutic');
  const usageTags = tags.filter(tag => tag.category === 'usage');

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex flex-wrap gap-2">
        {selectedTags.map((tag) => (
          <Badge
            key={tag.id}
            variant="secondary"
            className="flex items-center gap-1 px-2 py-1"
            style={{ backgroundColor: `${tag.color}20`, borderColor: tag.color, color: tag.color }}
          >
            {tag.name}
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 hover:bg-transparent"
              onClick={() => handleRemoveTag(tag.id)}
            >
              <X size={12} />
            </Button>
          </Badge>
        ))}
      </div>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-start text-left font-normal"
          >
            {selectedTags.length > 0
              ? `${selectedTags.length} étiquette(s) sélectionnée(s)`
              : placeholder}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput placeholder="Rechercher une étiquette pharmaceutique..." />
            <CommandEmpty>
              <div className="p-4 text-center">
                <p className="text-sm text-muted-foreground">
                  Aucune étiquette pharmaceutique trouvée.
                </p>
              </div>
            </CommandEmpty>

            {/* Therapeutic Classes */}
            <CommandGroup heading="💊 Classes Thérapeutiques">
              {therapeuticTags.map((tag) => (
                <CommandItem
                  key={tag.id}
                  value={tag.name}
                  onSelect={() => handleTagSelect(tag)}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: tag.color }}
                    />
                    <span>{tag.name}</span>
                  </div>
                  <Check
                    className={cn(
                      "h-4 w-4",
                      selectedTags.some(t => t.id === tag.id) ? "opacity-100" : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>

            {/* Usage/Domain Tags */}
            <CommandGroup heading="🩺 Domaines d'Usage">
              {usageTags.map((tag) => (
                <CommandItem
                  key={tag.id}
                  value={tag.name}
                  onSelect={() => handleTagSelect(tag)}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: tag.color }}
                    />
                    <span>{tag.name}</span>
                  </div>
                  <Check
                    className={cn(
                      "h-4 w-4",
                      selectedTags.some(t => t.id === tag.id) ? "opacity-100" : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      <div className="text-xs text-muted-foreground">
        Étiquettes pharmaceutiques standardisées pour une organisation professionnelle
      </div>
    </div>
  );
};

export default TagSelector;
