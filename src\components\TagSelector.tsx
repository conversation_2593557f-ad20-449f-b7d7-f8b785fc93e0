import React, { useState } from "react";
import { Tag } from "@/types";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { Check, Plus, X } from "lucide-react";
import { useTags } from "@/hooks/useTags";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface TagSelectorProps {
  selectedTags: Tag[];
  onTagsChange: (tags: Tag[]) => void;
  placeholder?: string;
  className?: string;
}

const TagSelector: React.FC<TagSelectorProps> = ({
  selectedTags,
  onTagsChange,
  placeholder = "Sélectionner des étiquettes...",
  className
}) => {
  const { tags, createTag, isLoading } = useTags();
  const [open, setOpen] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newTagName, setNewTagName] = useState("");
  const [newTagColor, setNewTagColor] = useState("#0DCDB7");
  const [isCreating, setIsCreating] = useState(false);

  const handleTagSelect = (tag: Tag) => {
    const isSelected = selectedTags.some(t => t.id === tag.id);
    
    if (isSelected) {
      onTagsChange(selectedTags.filter(t => t.id !== tag.id));
    } else {
      onTagsChange([...selectedTags, tag]);
    }
  };

  const handleRemoveTag = (tagId: string) => {
    onTagsChange(selectedTags.filter(t => t.id !== tagId));
  };

  const handleCreateTag = async () => {
    if (!newTagName.trim()) {
      toast.error("Le nom de l'étiquette est requis");
      return;
    }

    setIsCreating(true);
    const newTag = await createTag(newTagName.trim(), newTagColor);
    if (newTag) {
      onTagsChange([...selectedTags, newTag]);
      setNewTagName("");
      setNewTagColor("#0DCDB7");
      setIsCreateDialogOpen(false);
      setOpen(false);
    }
    setIsCreating(false);
  };

  const availableTags = tags.filter(tag => !selectedTags.some(selected => selected.id === tag.id));

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex flex-wrap gap-2">
        {selectedTags.map((tag) => (
          <Badge
            key={tag.id}
            variant="secondary"
            className="flex items-center gap-1 px-2 py-1"
            style={{ backgroundColor: `${tag.color}20`, borderColor: tag.color, color: tag.color }}
          >
            {tag.name}
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 hover:bg-transparent"
              onClick={() => handleRemoveTag(tag.id)}
            >
              <X size={12} />
            </Button>
          </Badge>
        ))}
      </div>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-start text-left font-normal"
          >
            {selectedTags.length > 0
              ? `${selectedTags.length} étiquette(s) sélectionnée(s)`
              : placeholder}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput placeholder="Rechercher une étiquette..." />
            <CommandEmpty>
              <div className="p-4 text-center">
                <p className="text-sm text-muted-foreground mb-2">
                  Aucune étiquette trouvée.
                </p>
                <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Plus size={12} className="mr-1" />
                      Créer une nouvelle étiquette
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Créer une nouvelle étiquette</DialogTitle>
                      <DialogDescription>
                        Ajoutez une étiquette personnalisée pour organiser vos médicaments.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="new-tag-name">Nom de l'étiquette</Label>
                        <Input
                          id="new-tag-name"
                          value={newTagName}
                          onChange={(e) => setNewTagName(e.target.value)}
                          placeholder="Ex: Vitamines, Urgence..."
                          maxLength={50}
                        />
                      </div>
                      <div>
                        <Label htmlFor="new-tag-color">Couleur</Label>
                        <div className="flex items-center gap-2">
                          <Input
                            id="new-tag-color"
                            type="color"
                            value={newTagColor}
                            onChange={(e) => setNewTagColor(e.target.value)}
                            className="w-16 h-10"
                          />
                          <Badge style={{ backgroundColor: newTagColor, color: 'white' }}>
                            {newTagName || "Aperçu"}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                        Annuler
                      </Button>
                      <Button onClick={handleCreateTag} disabled={isCreating}>
                        {isCreating ? "Création..." : "Créer"}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CommandEmpty>
            <CommandGroup>
              {availableTags.map((tag) => (
                <CommandItem
                  key={tag.id}
                  value={tag.name}
                  onSelect={() => handleTagSelect(tag)}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: tag.color }}
                    />
                    <span>{tag.name}</span>
                    {tag.isSystemTag && (
                      <Badge variant="outline" className="text-xs">
                        Système
                      </Badge>
                    )}
                  </div>
                  <Check
                    className={cn(
                      "h-4 w-4",
                      selectedTags.some(t => t.id === tag.id) ? "opacity-100" : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
              
              {availableTags.length > 0 && (
                <CommandItem
                  onSelect={() => setIsCreateDialogOpen(true)}
                  className="border-t mt-2 pt-2"
                >
                  <Plus size={12} className="mr-2" />
                  Créer une nouvelle étiquette
                </CommandItem>
              )}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default TagSelector;
