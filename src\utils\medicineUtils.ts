import { Medicine } from "@/types";
import { MedicineVariant } from "@/hooks/useMedicineSearch";

/**
 * Interface for grouped medicine data
 */
export interface GroupedMedicine {
  // Grouping key components
  name: string;
  dosage?: string;
  form?: string;
  presentation?: string;

  // Aggregated data
  totalQuantity: number;
  earliestExpiry: string;
  hasMultipleExpiries: boolean;

  // Individual medicines in this group
  medicines: Medicine[];

  // Computed properties
  groupKey: string;
  isCustom: boolean;
}

/**
 * Interface for medicine grouping options
 */
export interface MedicineGroupingOptions {
  includePresentation?: boolean; // Whether to include presentation in grouping key
  includeFamilyMember?: boolean; // Whether to group by family member as well
}

/**
 * Formats the complete medicine name including dosage, form, and presentation
 * Format: "Medicine Name - Dosage - Form - Presentation"
 * Example: "Paracétamol - 500mg - Comprimé - Boîte de 30"
 */
export const formatCompleteMedicineName = (medicine: Medicine): string => {
  if (medicine.is_custom) {
    return medicine.custom_name || "Médicament manuel";
  }

  const parts = [medicine.name];

  if (medicine.dosage) {
    parts.push(medicine.dosage);
  }

  if (medicine.form) {
    parts.push(medicine.form);
  }

  if (medicine.presentation) {
    parts.push(medicine.presentation);
  }

  return parts.filter(Boolean).join(" - ");
};

/**
 * Formats medicine name for display in search results
 * Shows just the base name for search, detailed info in subtitle
 */
export const formatMedicineSearchName = (nom: string, dosage?: string, forme?: string): string => {
  return nom;
};

/**
 * Formats medicine subtitle for search results
 * Shows dosage, form, and presentation information
 */
export const formatMedicineSearchSubtitle = (dosage?: string, forme?: string, presentation?: string): string => {
  const parts = [];

  if (dosage) {
    parts.push(dosage);
  }

  if (forme) {
    parts.push(forme);
  }

  if (presentation) {
    parts.push(presentation);
  }

  return parts.join(" - ");
};

/**
 * Gets the display name for a medicine (handles custom vs database medicines)
 */
export const getMedicineDisplayName = (medicine: Medicine): string => {
  if (medicine.is_custom) {
    return medicine.custom_name || "Médicament manuel";
  }
  
  return formatCompleteMedicineName(medicine);
};

/**
 * Formats medicine name for compact display (used in cards, lists)
 * Truncates if too long but keeps essential information
 */
export const formatCompactMedicineName = (medicine: Medicine, maxLength: number = 50): string => {
  const fullName = formatCompleteMedicineName(medicine);
  
  if (fullName.length <= maxLength) {
    return fullName;
  }
  
  // If too long, prioritize name and dosage over form
  if (medicine.is_custom) {
    return (medicine.custom_name || "Médicament manuel").substring(0, maxLength - 3) + "...";
  }
  
  const nameAndDosage = [medicine.name, medicine.dosage].filter(Boolean).join(" - ");
  
  if (nameAndDosage.length <= maxLength) {
    return nameAndDosage;
  }
  
  // If still too long, just use the name
  return (medicine.name || "Médicament").substring(0, maxLength - 3) + "...";
};

/**
 * Formats a MedicineVariant for display
 * Format: "Medicine Name - Dosage - Form - Presentation"
 * Example: "Levostamine - 5mg - Comprimé - Boîte de 30"
 */
export const formatMedicineVariantName = (variant: MedicineVariant): string => {
  const parts = [variant.nom];

  if (variant.dosage) {
    parts.push(variant.dosage);
  }

  if (variant.forme) {
    parts.push(variant.forme);
  }

  if (variant.presentation) {
    parts.push(variant.presentation);
  }

  return parts.filter(Boolean).join(" - ");
};

/**
 * Formats a MedicineVariant for compact display
 * Prioritizes name, dosage, and presentation over form if space is limited
 */
export const formatCompactMedicineVariantName = (variant: MedicineVariant, maxLength: number = 50): string => {
  const fullName = formatMedicineVariantName(variant);

  if (fullName.length <= maxLength) {
    return fullName;
  }

  // If too long, prioritize name, dosage, and presentation
  const nameAndDosage = [variant.nom, variant.dosage].filter(Boolean).join(" - ");
  const withPresentation = [nameAndDosage, variant.presentation].filter(Boolean).join(" - ");

  if (withPresentation.length <= maxLength) {
    return withPresentation;
  }

  if (nameAndDosage.length <= maxLength) {
    return nameAndDosage;
  }

  // If still too long, just use the name
  return (variant.nom || "Médicament").substring(0, maxLength - 3) + "...";
};

/**
 * Generates a unique grouping key for a medicine
 * Used to group medicines with the same name, dosage, and form
 */
export const generateMedicineGroupKey = (
  medicine: Medicine,
  options: MedicineGroupingOptions = {}
): string => {
  const { includePresentation = false, includeFamilyMember = false } = options;

  if (medicine.is_custom) {
    // Custom medicines are grouped by their custom name only (case-insensitive)
    const customName = (medicine.custom_name || "Médicament manuel").toLowerCase().trim();
    return includeFamilyMember && medicine.familyMember
      ? `custom:${customName}:${medicine.familyMember.id}`
      : `custom:${customName}`;
  }

  // Build grouping key from name, dosage, and form
  const keyParts = [
    medicine.name?.toLowerCase().trim() || "unknown",
    medicine.dosage?.toLowerCase().trim() || "no-dosage",
    medicine.form?.toLowerCase().trim() || "no-form"
  ];

  // Optionally include presentation
  if (includePresentation) {
    keyParts.push(medicine.presentation?.toLowerCase().trim() || "no-presentation");
  }

  // Optionally include family member
  if (includeFamilyMember && medicine.familyMember) {
    keyParts.push(`member:${medicine.familyMember.id}`);
  }

  return keyParts.join("|");
};

/**
 * Groups medicines by name, dosage, and form combination
 * Returns a map of grouped medicines with aggregated data
 */
export const groupMedicines = (
  medicines: Medicine[],
  options: MedicineGroupingOptions = {}
): Map<string, GroupedMedicine> => {
  const groups = new Map<string, GroupedMedicine>();

  medicines.forEach(medicine => {
    const groupKey = generateMedicineGroupKey(medicine, options);

    if (groups.has(groupKey)) {
      // Add to existing group
      const existingGroup = groups.get(groupKey)!;
      existingGroup.medicines.push(medicine);
      existingGroup.totalQuantity += medicine.quantity;

      // Update earliest expiry if this medicine expires earlier
      if (new Date(medicine.expiryDate) < new Date(existingGroup.earliestExpiry)) {
        existingGroup.earliestExpiry = medicine.expiryDate;
      }

      // Check if there are multiple different expiry dates
      const uniqueExpiries = new Set(existingGroup.medicines.map(m => m.expiryDate));
      existingGroup.hasMultipleExpiries = uniqueExpiries.size > 1;

    } else {
      // Create new group
      const newGroup: GroupedMedicine = {
        name: medicine.is_custom ? (medicine.custom_name || "Médicament manuel") : medicine.name,
        dosage: medicine.dosage,
        form: medicine.form,
        presentation: medicine.presentation,
        totalQuantity: medicine.quantity,
        earliestExpiry: medicine.expiryDate,
        hasMultipleExpiries: false,
        medicines: [medicine],
        groupKey,
        isCustom: medicine.is_custom || false
      };

      groups.set(groupKey, newGroup);
    }
  });

  return groups;
};

/**
 * Converts grouped medicines map to array and sorts by earliest expiry
 */
export const getGroupedMedicinesArray = (
  groupedMedicines: Map<string, GroupedMedicine>,
  sortBy: 'name' | 'expiry' | 'quantity' = 'expiry'
): GroupedMedicine[] => {
  const groupsArray = Array.from(groupedMedicines.values());

  switch (sortBy) {
    case 'name':
      return groupsArray.sort((a, b) => a.name.localeCompare(b.name));

    case 'quantity':
      return groupsArray.sort((a, b) => b.totalQuantity - a.totalQuantity);

    case 'expiry':
    default:
      return groupsArray.sort((a, b) =>
        new Date(a.earliestExpiry).getTime() - new Date(b.earliestExpiry).getTime()
      );
  }
};

/**
 * Filters grouped medicines based on expiry status
 */
export const filterGroupedMedicines = (
  groupedMedicines: GroupedMedicine[],
  filter: 'all' | 'expired' | 'expiring' | 'active' = 'all'
): GroupedMedicine[] => {
  if (filter === 'all') {
    return groupedMedicines;
  }

  const today = new Date();
  const thirtyDaysFromNow = new Date();
  thirtyDaysFromNow.setDate(today.getDate() + 30);

  return groupedMedicines.filter(group => {
    const expiryDate = new Date(group.earliestExpiry);

    switch (filter) {
      case 'expired':
        return expiryDate < today;
      case 'expiring':
        return expiryDate >= today && expiryDate <= thirtyDaysFromNow;
      case 'active':
        return expiryDate > thirtyDaysFromNow;
      default:
        return true;
    }
  });
};

/**
 * Detects potential duplicate medicines when adding a new medicine
 * Returns medicines that have the same name+dosage+form combination
 */
export const detectDuplicateMedicines = (
  newMedicine: Partial<Medicine>,
  existingMedicines: Medicine[]
): Medicine[] => {
  if (!newMedicine.name) {
    return [];
  }

  // For custom medicines, check by custom name
  if (newMedicine.is_custom) {
    const customName = newMedicine.custom_name?.toLowerCase().trim();
    if (!customName) return [];

    return existingMedicines.filter(med =>
      med.is_custom &&
      med.custom_name?.toLowerCase().trim() === customName
    );
  }

  // For database medicines, check by name+dosage+form
  const targetName = newMedicine.name.toLowerCase().trim();
  const targetDosage = newMedicine.dosage?.toLowerCase().trim() || "";
  const targetForm = newMedicine.form?.toLowerCase().trim() || "";

  return existingMedicines.filter(med => {
    if (med.is_custom) return false;

    const medName = med.name?.toLowerCase().trim() || "";
    const medDosage = med.dosage?.toLowerCase().trim() || "";
    const medForm = med.form?.toLowerCase().trim() || "";

    return medName === targetName &&
           medDosage === targetDosage &&
           medForm === targetForm;
  });
};

/**
 * Gets statistics about grouped medicines
 */
export interface GroupedMedicineStats {
  totalGroups: number;
  totalIndividualMedicines: number;
  groupsWithMultipleEntries: number;
  averageMedicinesPerGroup: number;
  expiredGroups: number;
  expiringGroups: number;
  activeGroups: number;
}

export const getGroupedMedicineStats = (
  groupedMedicines: GroupedMedicine[]
): GroupedMedicineStats => {
  const today = new Date();
  const thirtyDaysFromNow = new Date();
  thirtyDaysFromNow.setDate(today.getDate() + 30);

  const stats: GroupedMedicineStats = {
    totalGroups: groupedMedicines.length,
    totalIndividualMedicines: 0,
    groupsWithMultipleEntries: 0,
    averageMedicinesPerGroup: 0,
    expiredGroups: 0,
    expiringGroups: 0,
    activeGroups: 0
  };

  groupedMedicines.forEach(group => {
    stats.totalIndividualMedicines += group.medicines.length;

    if (group.medicines.length > 1) {
      stats.groupsWithMultipleEntries++;
    }

    const expiryDate = new Date(group.earliestExpiry);
    if (expiryDate < today) {
      stats.expiredGroups++;
    } else if (expiryDate <= thirtyDaysFromNow) {
      stats.expiringGroups++;
    } else {
      stats.activeGroups++;
    }
  });

  stats.averageMedicinesPerGroup = stats.totalGroups > 0
    ? stats.totalIndividualMedicines / stats.totalGroups
    : 0;

  return stats;
};

/**
 * Searches grouped medicines by name, dosage, or form
 */
export const searchGroupedMedicines = (
  groupedMedicines: GroupedMedicine[],
  searchTerm: string
): GroupedMedicine[] => {
  if (!searchTerm.trim()) {
    return groupedMedicines;
  }

  const term = searchTerm.toLowerCase().trim();

  return groupedMedicines.filter(group => {
    // Search in name
    if (group.name?.toLowerCase().includes(term)) {
      return true;
    }

    // Search in dosage
    if (group.dosage?.toLowerCase().includes(term)) {
      return true;
    }

    // Search in form
    if (group.form?.toLowerCase().includes(term)) {
      return true;
    }

    // Search in presentation
    if (group.presentation?.toLowerCase().includes(term)) {
      return true;
    }

    // Search in individual medicine notes or other fields
    return group.medicines.some(medicine =>
      medicine.notes?.toLowerCase().includes(term) ||
      medicine.locationName?.toLowerCase().includes(term) ||
      medicine.familyMember?.name?.toLowerCase().includes(term)
    );
  });
};
