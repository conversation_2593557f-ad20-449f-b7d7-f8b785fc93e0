import React, { memo, useState, useEffect, useRef } from "react";
import { Medicine } from "@/types";
import MedicineCard from "./MedicineCard";

interface VirtualizedMedicineListProps {
  medicines: Medicine[];
  onMedicineClick: (medicine: Medicine) => void;
  height?: number;
}

// Simple virtualization implementation without external dependencies
const VirtualizedMedicineList = ({
  medicines,
  onMedicineClick,
  height = 600
}: VirtualizedMedicineListProps) => {
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 10 });
  const containerRef = useRef<HTMLDivElement>(null);
  const itemHeight = 180; // Approximate height of each medicine card in single column
  const itemsPerRow = 1; // Single column layout
  const totalRows = medicines.length; // Each medicine is its own row

  useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current) return;

      const scrollTop = containerRef.current.scrollTop;
      const visibleStart = Math.floor(scrollTop / itemHeight);
      const visibleEnd = Math.min(
        visibleStart + Math.ceil(height / itemHeight) + 2,
        totalRows
      );

      setVisibleRange({ start: visibleStart, end: visibleEnd });
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      handleScroll(); // Initial calculation

      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, [height, totalRows, itemHeight]);

  const visibleMedicines = medicines.slice(
    visibleRange.start,
    visibleRange.end
  );

  const topPadding = visibleRange.start * itemHeight;
  const bottomPadding = Math.max(0, (totalRows - visibleRange.end) * itemHeight);

  return (
    <div
      ref={containerRef}
      className="overflow-auto scrollbar-thin scrollbar-thumb-teal/20 scrollbar-track-gray-100"
      style={{ height }}
    >
      <div style={{ paddingTop: topPadding }}>
        <div className="space-y-4">
          {visibleMedicines.map((medicine) => (
            <MedicineCard
              key={medicine.id}
              medicine={medicine}
              onClick={onMedicineClick}
            />
          ))}
        </div>
      </div>
      <div style={{ height: bottomPadding }} />
    </div>
  );
};

export default memo(VirtualizedMedicineList);
