# MedyTrack v1.4.2 Migration Guide

This guide covers the migration process for implementing custom expiry thresholds and the new tagging system in MedyTrack v1.4.2.

## Overview

Version 1.4.2 introduces two major features:
1. **Custom Expiry Warning Thresholds**: Users can set personalized expiry warning periods in months
2. **Flexible Tagging System**: Replaces the fixed category system with customizable tags

## Migration Steps

### Step 1: Database Schema Updates

Execute the following SQL scripts in order:

1. **Custom Expiry Thresholds**:
   ```bash
   psql -d your_database -f database/custom_expiry_thresholds.sql
   ```

2. **Tagging System**:
   ```bash
   psql -d your_database -f database/tagging_system.sql
   ```

### Step 2: Data Migration

The migration scripts automatically handle:

- **Expiry Thresholds**: All existing users get a default 1-month warning threshold
- **Category to Tag Migration**: Existing categories are converted to system tags:
  - `pain` → `Douleur` (Red: #EF4444)
  - `cold` → `Rhume` (Blue: #3B82F6)
  - `allergy` → `Allergie` (Amber: #F59E0B)
  - `digestion` → `Digestion` (Green: #10B981)
  - `first-aid` → `Premiers soins` (Purple: #8B5CF6)
  - `prescription` → `Ordonnance` (Navy: #2D4A8E)
  - `other` → `Autre` (Gray: #6B7280)

### Step 3: Application Updates

The application maintains backward compatibility during the transition:

- **Category field**: Preserved in `user_medicines` table for compatibility
- **New tags field**: Added to `Medicine` interface as optional
- **Dual filtering**: Both category and tag filters work simultaneously

### Step 4: Verification

After migration, verify:

1. **Expiry Thresholds**:
   ```sql
   SELECT id, expiry_warning_days FROM users LIMIT 5;
   ```

2. **System Tags Created**:
   ```sql
   SELECT name, color, is_system_tag FROM tags WHERE is_system_tag = true;
   ```

3. **Medicine Tags Migrated**:
   ```sql
   SELECT COUNT(*) FROM medicine_tags;
   ```

4. **Views Working**:
   ```sql
   SELECT * FROM medicines_with_tags LIMIT 5;
   SELECT * FROM dashboard_medicine_alerts_view LIMIT 5;
   ```

## New Features

### Custom Expiry Thresholds

- **User Setting**: Available in Profile → Medicine Settings
- **Range**: 1-12 months
- **Default**: 1 month
- **Impact**: Affects all expiry alerts, badges, and dashboard statistics

### Tagging System

- **System Tags**: Pre-defined tags equivalent to old categories
- **Custom Tags**: Users can create unlimited custom tags with colors
- **Multi-tagging**: Medicines can have multiple tags
- **Tag Management**: Create, edit, delete custom tags (system tags are protected)

## API Changes

### New Endpoints (via Supabase RPC)

- `get_household_tags(household_id)`: Get all tags for a household
- `add_tag_to_medicine(medicine_id, tag_id)`: Add tag to medicine

### Updated Views

- `medicines_with_tags`: Medicines with their associated tags
- `dashboard_medicine_alerts_view`: Now includes user expiry thresholds

### New Tables

- `tags`: Tag definitions with colors and household association
- `medicine_tags`: Many-to-many relationship between medicines and tags

## Backward Compatibility

### During Transition Period

- **Category field**: Still functional and displayed
- **Existing filters**: Category filters continue to work
- **API compatibility**: All existing endpoints remain functional

### Deprecation Timeline

- **v1.4.2**: Both systems active (current)
- **v1.5.0**: Category system marked as deprecated
- **v1.6.0**: Category system removed (planned)

## Rollback Plan

If rollback is needed:

1. **Expiry Thresholds**: Remove column and restore hardcoded 30-day logic
2. **Tagging System**: Drop new tables and restore category-only filtering

```sql
-- Rollback expiry thresholds
ALTER TABLE users DROP COLUMN IF EXISTS expiry_warning_days;

-- Rollback tagging system
DROP VIEW IF EXISTS medicines_with_tags;
DROP TABLE IF EXISTS medicine_tags;
DROP TABLE IF EXISTS tags;
```

## Performance Considerations

- **Indexes**: Added for tag filtering and expiry threshold queries
- **View optimization**: New views use efficient JOINs
- **Memory impact**: Minimal increase due to tag data

## Testing Checklist

- [ ] User can set custom expiry threshold in Profile
- [ ] Expiry alerts respect custom thresholds
- [ ] System tags created for all households
- [ ] Category data migrated to tags
- [ ] Users can create custom tags
- [ ] Tag filtering works in medicine lists
- [ ] Backward compatibility maintained
- [ ] Performance remains acceptable

## Support

For issues during migration:
1. Check database logs for errors
2. Verify all scripts executed successfully
3. Test with a small subset of users first
4. Have rollback plan ready

## Post-Migration Tasks

1. **User Communication**: Inform users about new features
2. **Documentation**: Update user guides
3. **Monitoring**: Watch for performance issues
4. **Feedback**: Collect user feedback on new features
