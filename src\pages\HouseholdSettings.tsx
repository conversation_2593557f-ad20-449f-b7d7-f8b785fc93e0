import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Layout from '@/components/Layout';
import ModernHeader from '@/components/ModernHeader';
import HouseholdInvitations from '@/components/household/HouseholdInvitations';

const HouseholdSettings = () => {
  const navigate = useNavigate();

  return (
    <Layout>
      <div className="medicinet-container pb-24 animate-fade-in">
        <ModernHeader
          title="Paramètres du foyer"
          subtitle="Gérez les membres de votre foyer et les invitations"
          showBackButton={true}
          variant="navy"
        />

        <HouseholdInvitations />
      </div>
    </Layout>
  );
};

export default HouseholdSettings;
