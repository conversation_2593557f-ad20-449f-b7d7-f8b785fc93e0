import React from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Loader2 } from "lucide-react";

interface OnboardingRouteProps {
  children: React.ReactNode;
}

const OnboardingRoute = ({ children }: OnboardingRouteProps) => {
  const { user, loading, isOnboardingCompleted } = useAuth();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="animate-spin h-8 w-8 text-primary" />
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/auth" />;
  }

  // If user has completed onboarding, redirect to home
  if (isOnboardingCompleted) {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

export default OnboardingRoute;
