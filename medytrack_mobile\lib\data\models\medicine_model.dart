import '../../domain/entities/medicine.dart';
import '../../domain/entities/tag.dart';
import '../../domain/entities/family_member.dart';

class MedicineModel extends Medicine {
  const MedicineModel({
    required super.id,
    required super.name,
    super.dosage,
    super.form,
    super.presentation,
    required super.quantity,
    required super.expiryDate,
    super.tags,
    super.locationId,
    super.locationName,
    super.imageUrl,
    super.notes,
    super.barcode,
    super.medicineId,
    super.customName,
    super.isCustom,
    super.lowStockThreshold,
    super.familyMember,
    required super.createdAt,
    required super.updatedAt,
    super.laboratoire,
    super.dci,
    super.classe,
    super.sousClasse,
    super.indications,
  });

  factory MedicineModel.fromJson(Map<String, dynamic> json) {
    return MedicineModel(
      id: json['id'] as String,
      name: json['name'] as String,
      dosage: json['dosage'] as String?,
      form: json['form'] as String?,
      presentation: json['presentation'] as String?,
      quantity: (json['quantity'] as num).toInt(),
      expiryDate: DateTime.parse(json['expiry_date'] as String),
      tags: [], // TODO: Parse tags from JSON
      locationId: json['location_id'] as String?,
      locationName: json['location_name'] as String?,
      imageUrl: json['image_url'] as String?,
      notes: json['notes'] as String?,
      barcode: json['barcode'] as String?,
      medicineId: json['medicine_id'] as String?,
      customName: json['custom_name'] as String?,
      isCustom: (json['is_custom'] as bool?) ?? false,
      lowStockThreshold: (json['low_stock_threshold'] as int?) ?? 5,
      familyMember: null, // TODO: Parse family member from JSON
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      laboratoire: json['laboratoire'] as String?,
      dci: json['dci'] as String?,
      classe: json['classe'] as String?,
      sousClasse: json['sous_classe'] as String?,
      indications: json['indications'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'dosage': dosage,
      'form': form,
      'presentation': presentation,
      'quantity': quantity,
      'expiry_date': expiryDate.toIso8601String(),
      'location_id': locationId,
      'location_name': locationName,
      'image_url': imageUrl,
      'notes': notes,
      'barcode': barcode,
      'medicine_id': medicineId,
      'custom_name': customName,
      'is_custom': isCustom,
      'low_stock_threshold': lowStockThreshold,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'laboratoire': laboratoire,
      'dci': dci,
      'classe': classe,
      'sous_classe': sousClasse,
      'indications': indications,
    };
  }

  MedicineModel copyWith({
    String? id,
    String? name,
    String? dosage,
    String? form,
    String? presentation,
    int? quantity,
    DateTime? expiryDate,
    List<Tag>? tags,
    String? locationId,
    String? locationName,
    String? imageUrl,
    String? notes,
    String? barcode,
    String? medicineId,
    String? customName,
    bool? isCustom,
    int? lowStockThreshold,
    FamilyMember? familyMember,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? laboratoire,
    String? dci,
    String? classe,
    String? sousClasse,
    String? indications,
  }) {
    return MedicineModel(
      id: id ?? this.id,
      name: name ?? this.name,
      dosage: dosage ?? this.dosage,
      form: form ?? this.form,
      presentation: presentation ?? this.presentation,
      quantity: quantity ?? this.quantity,
      expiryDate: expiryDate ?? this.expiryDate,
      tags: tags ?? this.tags,
      locationId: locationId ?? this.locationId,
      locationName: locationName ?? this.locationName,
      imageUrl: imageUrl ?? this.imageUrl,
      notes: notes ?? this.notes,
      barcode: barcode ?? this.barcode,
      medicineId: medicineId ?? this.medicineId,
      customName: customName ?? this.customName,
      isCustom: isCustom ?? this.isCustom,
      lowStockThreshold: lowStockThreshold ?? this.lowStockThreshold,
      familyMember: familyMember ?? this.familyMember,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      laboratoire: laboratoire ?? this.laboratoire,
      dci: dci ?? this.dci,
      classe: classe ?? this.classe,
      sousClasse: sousClasse ?? this.sousClasse,
      indications: indications ?? this.indications,
    );
  }
}
