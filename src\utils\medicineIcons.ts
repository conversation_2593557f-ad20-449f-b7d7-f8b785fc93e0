import {
  Pill,
  Droplets,
  Syringe,
  Wind, // Using Wind icon for sprays/aerosols
  Tablet,
  LucideIcon
} from "lucide-react";

/**
 * Maps medicine forms to appropriate icons
 */
export const getMedicineIcon = (form: string): LucideIcon => {
  const formLower = form.toLowerCase();
  
  // Tablets and pills
  if (formLower.includes('comprimé') || 
      formLower.includes('tablet') || 
      formLower.includes('gélule') || 
      formLower.includes('capsule')) {
    return Tablet;
  }
  
  // Liquids and drops
  if (formLower.includes('goutte') || 
      formLower.includes('drop') || 
      formLower.includes('sirop') || 
      formLower.includes('syrup') || 
      formLower.includes('solution') || 
      formLower.includes('suspension')) {
    return Droplets;
  }
  
  // Injections
  if (formLower.includes('injection') || 
      formLower.includes('ampoule') || 
      formLower.includes('seringue')) {
    return Syringe;
  }
  
  // Sprays and aerosols
  if (formLower.includes('spray') ||
      formLower.includes('aérosol') ||
      formLower.includes('pulvérisation') ||
      formLower.includes('inhalation')) {
    return Wind;
  }
  
  // Default to pill icon
  return Pill;
};

/**
 * Gets the color class for medicine icons based on form
 */
export const getMedicineIconColor = (form: string): string => {
  const formLower = form.toLowerCase();
  
  // Tablets and pills - blue
  if (formLower.includes('comprimé') || 
      formLower.includes('tablet') || 
      formLower.includes('gélule') || 
      formLower.includes('capsule')) {
    return 'text-blue-600';
  }
  
  // Liquids and drops - teal
  if (formLower.includes('goutte') || 
      formLower.includes('drop') || 
      formLower.includes('sirop') || 
      formLower.includes('syrup') || 
      formLower.includes('solution') || 
      formLower.includes('suspension')) {
    return 'text-teal-600';
  }
  
  // Injections - red
  if (formLower.includes('injection') || 
      formLower.includes('ampoule') || 
      formLower.includes('seringue')) {
    return 'text-red-600';
  }
  
  // Sprays and aerosols - green
  if (formLower.includes('spray') || 
      formLower.includes('aérosol') || 
      formLower.includes('pulvérisation') || 
      formLower.includes('inhalation')) {
    return 'text-green-600';
  }
  
  // Default - navy
  return 'text-navy';
};

/**
 * Gets a descriptive label for the medicine form
 */
export const getMedicineFormLabel = (form: string): string => {
  const formLower = form.toLowerCase();
  
  if (formLower.includes('comprimé') || formLower.includes('tablet')) {
    return 'Comprimé';
  }
  
  if (formLower.includes('gélule') || formLower.includes('capsule')) {
    return 'Gélule';
  }
  
  if (formLower.includes('goutte') || formLower.includes('drop')) {
    return 'Gouttes';
  }
  
  if (formLower.includes('sirop') || formLower.includes('syrup')) {
    return 'Sirop';
  }
  
  if (formLower.includes('injection')) {
    return 'Injectable';
  }
  
  if (formLower.includes('spray') || formLower.includes('pulvérisation')) {
    return 'Spray';
  }
  
  if (formLower.includes('crème') || formLower.includes('cream')) {
    return 'Crème';
  }
  
  if (formLower.includes('pommade') || formLower.includes('ointment')) {
    return 'Pommade';
  }
  
  // Return the original form if no specific match
  return form || 'Médicament';
};
