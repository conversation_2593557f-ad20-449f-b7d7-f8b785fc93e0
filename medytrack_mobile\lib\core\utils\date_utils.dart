import 'package:intl/intl.dart';

/// Utility class for date formatting and manipulation
class AppDateUtils {
  AppDateUtils._();

  // Date formatters
  static final DateFormat _dateFormat = DateFormat('dd/MM/yyyy');
  static final DateFormat _monthYearFormat = DateFormat('MM/yy');
  static final DateFormat _timeFormat = DateFormat('HH:mm');
  static final DateFormat _dateTimeFormat = DateFormat('dd/MM/yyyy HH:mm');
  static final DateFormat _shortDateFormat = DateFormat('dd MMM');
  static final DateFormat _fullDateFormat = DateFormat('EEEE, dd MMMM yyyy');

  /// Format date as dd/MM/yyyy
  static String formatDate(DateTime date) {
    return _dateFormat.format(date);
  }

  /// Format date as MM/yy (for expiry dates)
  static String formatMonthYear(DateTime date) {
    return _monthYearFormat.format(date);
  }

  /// Format time as HH:mm
  static String formatTime(DateTime date) {
    return _timeFormat.format(date);
  }

  /// Format date and time as dd/MM/yyyy HH:mm
  static String formatDateTime(DateTime date) {
    return _dateTimeFormat.format(date);
  }

  /// Format date as dd MMM (short format)
  static String formatShortDate(DateTime date) {
    return _shortDateFormat.format(date);
  }

  /// Format date as full format (e.g., Monday, 15 January 2024)
  static String formatFullDate(DateTime date) {
    return _fullDateFormat.format(date);
  }

  /// Get relative time string (e.g., "2 days ago", "in 3 months")
  static String getRelativeTime(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now);

    if (difference.isNegative) {
      // Past dates
      final absDifference = difference.abs();
      if (absDifference.inDays == 0) {
        if (absDifference.inHours == 0) {
          return 'Il y a ${absDifference.inMinutes} min';
        }
        return 'Il y a ${absDifference.inHours}h';
      } else if (absDifference.inDays < 30) {
        return 'Il y a ${absDifference.inDays} jour${absDifference.inDays > 1 ? 's' : ''}';
      } else if (absDifference.inDays < 365) {
        final months = (absDifference.inDays / 30).floor();
        return 'Il y a $months mois';
      } else {
        final years = (absDifference.inDays / 365).floor();
        return 'Il y a $years an${years > 1 ? 's' : ''}';
      }
    } else {
      // Future dates
      if (difference.inDays == 0) {
        if (difference.inHours == 0) {
          return 'Dans ${difference.inMinutes} min';
        }
        return 'Dans ${difference.inHours}h';
      } else if (difference.inDays < 30) {
        return 'Dans ${difference.inDays} jour${difference.inDays > 1 ? 's' : ''}';
      } else if (difference.inDays < 365) {
        final months = (difference.inDays / 30).floor();
        return 'Dans $months mois';
      } else {
        final years = (difference.inDays / 365).floor();
        return 'Dans $years an${years > 1 ? 's' : ''}';
      }
    }
  }

  /// Check if date is today
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month && date.day == now.day;
  }

  /// Check if date is yesterday
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year && date.month == yesterday.month && date.day == yesterday.day;
  }

  /// Check if date is tomorrow
  static bool isTomorrow(DateTime date) {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return date.year == tomorrow.year && date.month == tomorrow.month && date.day == tomorrow.day;
  }

  /// Check if medicine is expired
  static bool isExpired(DateTime expiryDate) {
    return expiryDate.isBefore(DateTime.now());
  }

  /// Check if medicine is expiring soon (within specified months)
  static bool isExpiringSoon(DateTime expiryDate, int warningMonths) {
    final warningDate = DateTime.now().add(Duration(days: warningMonths * 30));
    return expiryDate.isBefore(warningDate) && !isExpired(expiryDate);
  }

  /// Get expiry status for medicine
  static ExpiryStatus getExpiryStatus(DateTime expiryDate, int warningMonths) {
    if (isExpired(expiryDate)) {
      return ExpiryStatus.expired;
    } else if (isExpiringSoon(expiryDate, warningMonths)) {
      return ExpiryStatus.expiringSoon;
    } else {
      return ExpiryStatus.active;
    }
  }

  /// Get days until expiry (negative if expired)
  static int getDaysUntilExpiry(DateTime expiryDate) {
    return expiryDate.difference(DateTime.now()).inDays;
  }

  /// Get months until expiry (negative if expired)
  static int getMonthsUntilExpiry(DateTime expiryDate) {
    final now = DateTime.now();
    final months = (expiryDate.year - now.year) * 12 + (expiryDate.month - now.month);
    return months;
  }

  /// Parse date from string (dd/MM/yyyy format)
  static DateTime? parseDate(String dateString) {
    try {
      return _dateFormat.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// Parse month/year from string (MM/yy format)
  static DateTime? parseMonthYear(String monthYearString) {
    try {
      return _monthYearFormat.parse(monthYearString);
    } catch (e) {
      return null;
    }
  }

  /// Get start of day
  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  /// Get end of day
  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }

  /// Get start of month
  static DateTime startOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }

  /// Get end of month
  static DateTime endOfMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0, 23, 59, 59, 999);
  }

  /// Get age from birth date
  static int getAge(DateTime birthDate) {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month || (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }
}

/// Enum for expiry status
enum ExpiryStatus {
  active,
  expiringSoon,
  expired,
}
