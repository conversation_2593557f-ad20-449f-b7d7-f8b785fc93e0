import '../../core/storage/local_storage_service.dart';
import '../models/medicine_model.dart';

abstract class MedicineLocalDataSource {
  Future<List<MedicineModel>> getCachedMedicines();
  Future<void> cacheMedicines(List<MedicineModel> medicines);
  Future<void> clearCache();
}

class MedicineLocalDataSourceImpl implements MedicineLocalDataSource {
  final LocalStorageService localStorage;
  static const String _medicinesKey = 'cached_medicines';

  MedicineLocalDataSourceImpl({
    required this.localStorage,
  });

  @override
  Future<List<MedicineModel>> getCachedMedicines() async {
    try {
      final medicinesJson = await localStorage.getStringList(_medicinesKey);
      if (medicinesJson == null) return [];

      return medicinesJson
          .map((json) => MedicineModel.fromJson({'data': json}))
          .toList();
    } catch (e) {
      return [];
    }
  }

  @override
  Future<void> cacheMedicines(List<MedicineModel> medicines) async {
    try {
      final medicinesJson = medicines
          .map((medicine) => medicine.toJson().toString())
          .toList();
      
      await localStorage.setStringList(_medicinesKey, medicinesJson);
    } catch (e) {
      // Ignore cache errors
    }
  }

  @override
  Future<void> clearCache() async {
    try {
      await localStorage.remove(_medicinesKey);
    } catch (e) {
      // Ignore cache errors
    }
  }
}
