import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { MedicineVariantSelector } from '@/components/medicine/MedicineVariantSelector'
import { MedicineVariant } from '@/hooks/useMedicineSearch'
import { formatMedicineVariantName, formatCompleteMedicineName } from '@/utils/medicineUtils'
import { Medicine } from '@/types'

// Mock data for testing
const mockVariants: MedicineVariant[] = [
  {
    id: '1',
    nom: 'Levostamine',
    dosage: '5mg',
    forme: 'Comprimé',
    presentation: 'Boîte de 30 comprimés',
    laboratoire: 'Lab A',
    amm: 'AMM001',
    dci: 'DCI001',
    classe: 'Classe A',
    sous_classe: 'Sous-classe A'
  },
  {
    id: '2',
    nom: 'Levostamine',
    dosage: '5mg',
    forme: 'Comprimé',
    presentation: 'Flacon de 100ml',
    laboratoire: 'Lab A',
    amm: 'AMM002',
    dci: 'DCI001',
    classe: 'Classe A',
    sous_classe: 'Sous-classe A'
  },
  {
    id: '3',
    nom: 'Levostamine',
    dosage: '10mg',
    forme: 'Sirop',
    presentation: 'Flacon de 200ml',
    laboratoire: 'Lab A',
    amm: 'AMM003',
    dci: 'DCI001',
    classe: 'Classe A',
    sous_classe: 'Sous-classe A'
  }
]

const mockMedicine: Medicine = {
  id: 'med-1',
  name: 'Levostamine',
  dosage: '5mg',
  form: 'Comprimé',
  presentation: 'Boîte de 30 comprimés',
  quantity: 1,
  expiryDate: '2025-12-31',
  category: 'other',
  location: 'loc-1',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

describe('Presentation Field Functionality', () => {
  describe('MedicineVariantSelector Component', () => {
    const mockOnVariantSelect = vi.fn()
    const mockOnCancel = vi.fn()
    const mockOnCreateManual = vi.fn()

    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('should display presentation dropdown alongside dosage and form', () => {
      render(
        <MedicineVariantSelector
          medicineName="Levostamine"
          variants={mockVariants}
          isLoading={false}
          onVariantSelect={mockOnVariantSelect}
          onCancel={mockOnCancel}
          onCreateManual={mockOnCreateManual}
        />
      )

      expect(screen.getByText('Dosage')).toBeInTheDocument()
      expect(screen.getByText('Forme')).toBeInTheDocument()
      expect(screen.getByText('Présentation')).toBeInTheDocument()
    })

    it('should show unique presentation options', async () => {
      const user = userEvent.setup()
      
      render(
        <MedicineVariantSelector
          medicineName="Levostamine"
          variants={mockVariants}
          isLoading={false}
          onVariantSelect={mockOnVariantSelect}
          onCancel={mockOnCancel}
          onCreateManual={mockOnCreateManual}
        />
      )

      // Click on presentation dropdown
      const presentationTrigger = screen.getByRole('combobox', { name: /présentation/i })
      await user.click(presentationTrigger)

      // Should show unique presentations
      await waitFor(() => {
        expect(screen.getByText('Boîte de 30 comprimés')).toBeInTheDocument()
        expect(screen.getByText('Flacon de 100ml')).toBeInTheDocument()
        expect(screen.getByText('Flacon de 200ml')).toBeInTheDocument()
      })
    })

    it('should filter variants based on presentation selection', async () => {
      const user = userEvent.setup()
      
      render(
        <MedicineVariantSelector
          medicineName="Levostamine"
          variants={mockVariants}
          isLoading={false}
          onVariantSelect={mockOnVariantSelect}
          onCancel={mockOnCancel}
          onCreateManual={mockOnCreateManual}
        />
      )

      // Select presentation
      const presentationTrigger = screen.getByRole('combobox', { name: /présentation/i })
      await user.click(presentationTrigger)
      
      await waitFor(() => {
        const option = screen.getByText('Boîte de 30 comprimés')
        user.click(option)
      })

      // Should show confirmation with complete name including presentation
      await waitFor(() => {
        expect(screen.getByText(/Levostamine - 5mg - Comprimé - Boîte de 30 comprimés/)).toBeInTheDocument()
      })
    })

    it('should call onVariantSelect with presentation data', async () => {
      const user = userEvent.setup()
      
      render(
        <MedicineVariantSelector
          medicineName="Levostamine"
          variants={mockVariants}
          isLoading={false}
          onVariantSelect={mockOnVariantSelect}
          onCancel={mockOnCancel}
          onCreateManual={mockOnCreateManual}
        />
      )

      // Select dosage, form, and presentation to get a unique variant
      const dosageTrigger = screen.getByRole('combobox', { name: /dosage/i })
      await user.click(dosageTrigger)
      await user.click(screen.getByText('5mg'))

      const formeTrigger = screen.getByRole('combobox', { name: /forme/i })
      await user.click(formeTrigger)
      await user.click(screen.getByText('Comprimé'))

      const presentationTrigger = screen.getByRole('combobox', { name: /présentation/i })
      await user.click(presentationTrigger)
      await user.click(screen.getByText('Boîte de 30 comprimés'))

      // Click confirm button
      const confirmButton = screen.getByText('Confirmer la sélection')
      await user.click(confirmButton)

      expect(mockOnVariantSelect).toHaveBeenCalledWith(
        expect.objectContaining({
          nom: 'Levostamine',
          dosage: '5mg',
          forme: 'Comprimé',
          presentation: 'Boîte de 30 comprimés'
        })
      )
    })
  })

  describe('Medicine Utility Functions', () => {
    it('should format medicine variant name with presentation', () => {
      const variant = mockVariants[0]
      const formattedName = formatMedicineVariantName(variant)
      
      expect(formattedName).toBe('Levostamine - 5mg - Comprimé - Boîte de 30 comprimés')
    })

    it('should format complete medicine name with presentation', () => {
      const formattedName = formatCompleteMedicineName(mockMedicine)
      
      expect(formattedName).toBe('Levostamine - 5mg - Comprimé - Boîte de 30 comprimés')
    })

    it('should handle missing presentation gracefully', () => {
      const medicineWithoutPresentation = { ...mockMedicine, presentation: undefined }
      const formattedName = formatCompleteMedicineName(medicineWithoutPresentation)
      
      expect(formattedName).toBe('Levostamine - 5mg - Comprimé')
    })

    it('should handle custom medicines without presentation', () => {
      const customMedicine = {
        ...mockMedicine,
        is_custom: true,
        custom_name: 'Mon médicament personnel',
        presentation: undefined
      }
      const formattedName = formatCompleteMedicineName(customMedicine)
      
      expect(formattedName).toBe('Mon médicament personnel')
    })
  })

  describe('Presentation Field Data Structure', () => {
    it('should include presentation in MedicineVariant interface', () => {
      const variant = mockVariants[0]
      
      expect(variant).toHaveProperty('presentation')
      expect(variant.presentation).toBe('Boîte de 30 comprimés')
    })

    it('should include presentation in Medicine interface', () => {
      expect(mockMedicine).toHaveProperty('presentation')
      expect(mockMedicine.presentation).toBe('Boîte de 30 comprimés')
    })
  })

  describe('Presentation Field Filtering Logic', () => {
    it('should distinguish between medicines with same name/dosage/form but different presentations', () => {
      const variant1 = mockVariants[0] // Boîte de 30 comprimés
      const variant2 = mockVariants[1] // Flacon de 100ml
      
      // Same name, dosage, form
      expect(variant1.nom).toBe(variant2.nom)
      expect(variant1.dosage).toBe(variant2.dosage)
      expect(variant1.forme).toBe(variant2.forme)
      
      // Different presentations
      expect(variant1.presentation).not.toBe(variant2.presentation)
      expect(variant1.presentation).toBe('Boîte de 30 comprimés')
      expect(variant2.presentation).toBe('Flacon de 100ml')
    })

    it('should create unique identifiers with presentation', () => {
      const name1 = formatMedicineVariantName(mockVariants[0])
      const name2 = formatMedicineVariantName(mockVariants[1])
      
      expect(name1).not.toBe(name2)
      expect(name1).toContain('Boîte de 30 comprimés')
      expect(name2).toContain('Flacon de 100ml')
    })
  })
})
