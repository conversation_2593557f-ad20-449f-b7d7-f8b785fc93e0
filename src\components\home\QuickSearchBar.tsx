
import React, { useState, useMemo } from "react";
import { Medicine } from "@/types";
import SearchBar from "@/components/SearchBar";
import { useUserMedicines } from "@/hooks/useUserMedicines";
import { Card } from "@/components/ui/card";
import { formatCompleteMedicineName } from "@/utils/medicineUtils";

interface QuickSearchBarProps {
  onSelect: (medicine: Medicine) => void;
}

const QuickSearchBar = ({ onSelect }: QuickSearchBarProps) => {
  const { medicines, isLoading } = useUserMedicines();
  const [showResults, setShowResults] = useState(false);
  const [query, setQuery] = useState("");

  // Filter user medicines based on search query
  const filteredMedicines = useMemo(() => {
    if (!query || query.length < 2) return [];

    const searchTerm = query.toLowerCase();
    return medicines.filter(medicine => {
      const medicineName = formatCompleteMedicineName(medicine).toLowerCase();
      const customName = medicine.custom_name?.toLowerCase() || "";
      const notes = medicine.notes?.toLowerCase() || "";

      return medicineName.includes(searchTerm) ||
             customName.includes(searchTerm) ||
             notes.includes(searchTerm);
    });
  }, [medicines, query]);

  const handleSearch = (value: string) => {
    setQuery(value);
    setShowResults(value.length >= 2);
  };

  const handleSelect = (medicine: Medicine) => {
    setShowResults(false);
    setQuery("");
    onSelect(medicine);
  };

  return (
    <div className="relative">
      <SearchBar
        value={query}
        onChange={handleSearch}
        onSearch={() => {}}
        placeholder="Rechercher dans mes médicaments..."
      />
      {showResults && query.length >= 2 && (
        <Card className="absolute z-50 w-full mt-1 max-h-64 overflow-y-auto">
          <div className="p-2">
            {isLoading ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin h-4 w-4 border-2 border-teal border-t-transparent rounded-full"></div>
                <span className="ml-2 text-sm text-gray-600">Recherche...</span>
              </div>
            ) : filteredMedicines.length > 0 ? (
              <div className="space-y-1">
                {filteredMedicines.map((medicine) => (
                  <div
                    key={medicine.id}
                    onClick={() => handleSelect(medicine)}
                    className="p-3 hover:bg-gray-50 cursor-pointer rounded-lg border border-transparent hover:border-teal/20 transition-colors"
                  >
                    <div className="font-bold text-navy">
                      {formatCompleteMedicineName(medicine)}
                    </div>
                    <div className="text-sm text-gray-600">
                      Quantité: {medicine.quantity} • {medicine.locationName || 'Emplacement non défini'}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500">
                <p className="text-sm">Aucun médicament trouvé dans votre collection</p>
              </div>
            )}
          </div>
        </Card>
      )}
    </div>
  );
};

export default QuickSearchBar;
