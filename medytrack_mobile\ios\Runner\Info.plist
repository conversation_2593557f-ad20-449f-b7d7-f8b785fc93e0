<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<!-- App Information -->
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>MedyTrack</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>medytrack_mobile</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	
	<!-- Minimum iOS Version -->
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>MinimumOSVersion</key>
	<string>14.0</string>
	
	<!-- Supported Interface Orientations -->
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	
	<!-- Launch Screen -->
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	
	<!-- Status Bar -->
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
	
	<!-- Privacy Permissions -->
	<key>NSCameraUsageDescription</key>
	<string>MedyTrack a besoin d'accéder à votre appareil photo pour scanner les codes-barres des médicaments et prendre des photos pour identification.</string>
	
	<key>NSPhotoLibraryUsageDescription</key>
	<string>MedyTrack a besoin d'accéder à votre photothèque pour sélectionner des images de médicaments existantes.</string>
	
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>MedyTrack a besoin d'accéder à votre photothèque pour sauvegarder les photos de médicaments.</string>
	
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>MedyTrack peut utiliser votre localisation pour vous rappeler de prendre vos médicaments selon votre emplacement.</string>
	
	<key>NSMicrophoneUsageDescription</key>
	<string>MedyTrack a besoin d'accéder au microphone pour les fonctionnalités de reconnaissance vocale.</string>
	
	<key>NSContactsUsageDescription</key>
	<string>MedyTrack a besoin d'accéder à vos contacts pour partager des informations sur les médicaments avec votre famille.</string>
	
	<key>NSCalendarsUsageDescription</key>
	<string>MedyTrack a besoin d'accéder à votre calendrier pour programmer des rappels de prise de médicaments.</string>
	
	<key>NSRemindersUsageDescription</key>
	<string>MedyTrack a besoin d'accéder à vos rappels pour créer des alertes de médicaments.</string>
	
	<key>NSFaceIDUsageDescription</key>
	<string>MedyTrack utilise Face ID pour sécuriser l'accès à vos informations médicales sensibles.</string>
	
	<!-- App Transport Security -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>wzzykbnebhyvdoagpwvk.supabase.co</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
		</dict>
	</dict>
	
	<!-- URL Schemes -->
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>medytrack.deeplink</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>medytrack</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>medytrack.auth</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.medytrack.medytrack-mobile</string>
			</array>
		</dict>
	</array>
	
	<!-- Document Types -->
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>Medicine Image</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.image</string>
			</array>
		</dict>
	</array>
	
	<!-- Exported Type Declarations -->
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.data</string>
			</array>
			<key>UTTypeDescription</key>
			<string>MedyTrack Medicine Data</string>
			<key>UTTypeIdentifier</key>
			<string>com.medytrack.medicine-data</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>medytrack</string>
				</array>
			</dict>
		</dict>
	</array>
	
	<!-- Background Modes -->
	<key>UIBackgroundModes</key>
	<array>
		<string>background-fetch</string>
		<string>background-processing</string>
		<string>remote-notification</string>
	</array>
	
	<!-- Required Device Capabilities -->
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	
	<!-- App Category -->
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.medical</string>
	
	<!-- iTunes File Sharing -->
	<key>UIFileSharingEnabled</key>
	<false/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<false/>
	
	<!-- Scene Configuration -->
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
				</dict>
			</array>
		</dict>
	</dict>
	
	<!-- Accessibility -->
	<key>UIAccessibilityEnabled</key>
	<true/>
	
	<!-- Keyboard Support -->
	<key>UIKeyboardType</key>
	<string>UIKeyboardTypeDefault</string>
	
	<!-- Notification Settings -->
	<key>UIUserNotificationSettings</key>
	<dict>
		<key>UIUserNotificationTypeAlert</key>
		<true/>
		<key>UIUserNotificationTypeBadge</key>
		<true/>
		<key>UIUserNotificationTypeSound</key>
		<true/>
	</dict>
	
	<!-- Firebase Configuration -->
	<key>FirebaseAppDelegateProxyEnabled</key>
	<true/>
	
	<!-- App Store Configuration -->
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	
	<!-- Localization -->
	<key>CFBundleLocalizations</key>
	<array>
		<string>fr</string>
		<string>en</string>
		<string>ar</string>
	</array>
	
	<!-- Development Team -->
	<key>DTPlatformName</key>
	<string>iphoneos</string>
	<key>DTSDKName</key>
	<string>iphoneos17.0</string>
</dict>
</plist>
