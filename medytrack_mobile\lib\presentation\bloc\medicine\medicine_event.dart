part of 'medicine_bloc.dart';

abstract class MedicineEvent extends Equatable {
  const MedicineEvent();

  @override
  List<Object?> get props => [];
}

class MedicineLoadRequested extends MedicineEvent {}

class MedicineAddRequested extends MedicineEvent {
  final Medicine medicine;

  const MedicineAddRequested(this.medicine);

  @override
  List<Object?> get props => [medicine];
}

class MedicineUpdateRequested extends MedicineEvent {
  final Medicine medicine;

  const MedicineUpdateRequested(this.medicine);

  @override
  List<Object?> get props => [medicine];
}

class MedicineDeleteRequested extends MedicineEvent {
  final String id;

  const MedicineDeleteRequested(this.id);

  @override
  List<Object?> get props => [id];
}
