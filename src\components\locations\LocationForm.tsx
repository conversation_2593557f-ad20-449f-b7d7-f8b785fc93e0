
import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

interface LocationFormProps {
  name: string;
  description: string;
  setName: (name: string) => void;
  setDescription: (description: string) => void;
  isSubmitting?: boolean;
}

const LocationForm = ({ 
  name, 
  description, 
  setName, 
  setDescription,
  isSubmitting = false 
}: LocationFormProps) => {
  return (
    <div className="space-y-4 py-4">
      <div className="space-y-2">
        <Label htmlFor="name">Nom</Label>
        <Input
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Ex: Salle de bain, Cuisine..."
          disabled={isSubmitting}
          required
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="description">Description (optionnel)</Label>
        <Textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Description de l'emplacement"
          rows={3}
          disabled={isSubmitting}
        />
      </div>
    </div>
  );
};

export default LocationForm;
