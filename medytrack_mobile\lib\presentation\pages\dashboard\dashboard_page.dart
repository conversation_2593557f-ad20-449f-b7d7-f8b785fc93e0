import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:lucide_icons/lucide_icons.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/medicine.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/dashboard/dashboard_bloc.dart';
import '../../widgets/common/app_logo.dart';
import '../../widgets/common/modern_header.dart';
import '../../widgets/dashboard/statistics_card.dart';
import '../../widgets/medicine/medicine_card.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  @override
  void initState() {
    super.initState();
    context.read<DashboardBloc>().add(DashboardLoadRequested());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, authState) {
          String userName = 'Utilisateur';
          if (authState is AuthAuthenticated) {
            userName = authState.user.displayName;
          }

          return ModernHeader(
            greeting: 'Bonjour, $userName',
            subtitle: 'Gérez vos médicaments facilement',
            showNotificationBadge: false, // TODO: Get from notifications bloc
            notificationCount: 0,
            onNotificationTap: () {
              // TODO: Navigate to notifications
            },
            onProfileTap: () {
              // TODO: Navigate to profile
            },
          );
        },
      ),
      body: BlocBuilder<DashboardBloc, DashboardState>(
        builder: (context, state) {
          if (state is DashboardLoading) {
            return const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.teal),
              ),
            );
          }

          if (state is DashboardError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppColors.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Erreur de chargement',
                    style: AppTextStyles.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.grey600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      context.read<DashboardBloc>().add(
                        DashboardLoadRequested(),
                      );
                    },
                    child: const Text('Réessayer'),
                  ),
                ],
              ),
            );
          }

          if (state is DashboardLoaded) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Quick Search Bar
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.grey100,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.grey300),
                    ),
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: 'Rechercher vos médicaments...',
                        hintStyle: AppTextStyles.formHint,
                        prefixIcon: const Icon(
                          LucideIcons.search,
                          color: AppColors.grey600,
                        ),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      onChanged: (value) {
                        // TODO: Implement search functionality
                      },
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Statistics Cards
                  Text(
                    'Aperçu de vos médicaments',
                    style: AppTextStyles.headlineSmall.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppColors.navy,
                    ),
                  ),

                  const SizedBox(height: 16),

                  GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 1.1,
                    children: [
                      StatisticsCardFactory.total(
                        count: state.stats.total,
                        onTap: () {
                          // TODO: Navigate to medicines with no filter
                        },
                      ),
                      StatisticsCardFactory.expired(
                        count: state.stats.expired,
                        totalCount: state.stats.total,
                        onTap: () {
                          // TODO: Navigate to medicines with expired filter
                        },
                      ),
                      StatisticsCardFactory.expiringSoon(
                        count: state.stats.expiringSoon,
                        totalCount: state.stats.total,
                        onTap: () {
                          // TODO: Navigate to medicines with expiring filter
                        },
                      ),
                      StatisticsCardFactory.lowStock(
                        count: state.stats.lowStock,
                        totalCount: state.stats.total,
                        onTap: () {
                          // TODO: Navigate to medicines with low stock filter
                        },
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Recent Medicines
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Médicaments récents',
                        style: AppTextStyles.headlineSmall.copyWith(
                          fontWeight: FontWeight.w700,
                          color: AppColors.navy,
                        ),
                      ),
                      if (state.medicines.isNotEmpty)
                        TextButton(
                          onPressed: () {
                            // TODO: Navigate to all medicines
                          },
                          child: Text(
                            'Voir tout',
                            style: AppTextStyles.labelLarge.copyWith(
                              color: AppColors.teal,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  if (state.medicines.isEmpty)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(32),
                      decoration: BoxDecoration(
                        color: AppColors.grey50,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: AppColors.grey200),
                      ),
                      child: Column(
                        children: [
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: AppColors.teal.withOpacity(0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              LucideIcons.pill,
                              size: 40,
                              color: AppColors.teal,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Aucun médicament',
                            style: AppTextStyles.titleMedium.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Commencez par ajouter vos premiers médicaments pour suivre leur expiration et stock',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.grey600,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 20),
                          ElevatedButton.icon(
                            onPressed: () {
                              // TODO: Navigate to add medicine
                            },
                            icon: Icon(LucideIcons.plus),
                            label: const Text('Ajouter un médicament'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.teal,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  else
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: state.medicines.take(5).length,
                      separatorBuilder: (context, index) =>
                          const SizedBox(height: 12),
                      itemBuilder: (context, index) {
                        final medicine = state.medicines[index];
                        return MedicineCard(
                          medicine: medicine,
                          showHouseholdMember: true,
                          onTap: () {
                            // TODO: Navigate to medicine details
                          },
                          onEdit: () {
                            // TODO: Navigate to edit medicine
                          },
                        );
                      },
                    ),
                ],
              ),
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }
}
