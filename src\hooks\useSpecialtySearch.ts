
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface TunisiaMedicine {
  id: string;
  nom: string | null;
  dosage: string | null;
  forme: string | null;
  presentation: string | null;
  dci: string | null;
  classe: string | null;
  sous_classe: string | null;
  laboratoire: string | null;
  amm: string | null;
  date_amm: string | null;
  conditionnement_primaire: string | null;
  specification_conditionnement_primaire: string | null;
  tableau: string | null;
  duree_de_conservation: string | null;
  indications: string | null;
  g_p_b: string | null;
  veic: string | null;
}

export const useTunisiaMedicineSearch = () => {
  const [medicines, setMedicines] = useState<TunisiaMedicine[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const searchSpecialties = async (query: string) => {
    if (query.trim().length < 2) {
      setMedicines([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const { data, error: supabaseError } = await supabase
        .from('tunisia_medicines')
        .select('*')
        .or(`nom.ilike.%${query}%,amm.ilike.%${query}%,laboratoire.ilike.%${query}%,dci.ilike.%${query}%`)
        .limit(50);

      if (supabaseError) {
        throw supabaseError;
      }

      setMedicines(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Une erreur est survenue lors de la recherche');
      setMedicines([]);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    medicines,
    searchMedicines: searchSpecialties,
    isLoading,
    error,
    searchQuery,
    setSearchQuery,
  };
};
