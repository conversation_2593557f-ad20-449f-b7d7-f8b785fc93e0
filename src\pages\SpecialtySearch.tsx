
import React, { useState, useEffect } from "react";
import { useTunisiaMedicineSearch, TunisiaMedicine } from "@/hooks/useSpecialtySearch";
import SearchBar from "@/components/SearchBar";
import ModernHeader from "@/components/ModernHeader";
import BottomNavigation from "@/components/BottomNavigation";
import { Loader2, SearchX } from "lucide-react";
import SpecialtyCard from "@/components/SpecialtyCard";
import SpecialtyDetails from "@/components/SpecialtyDetails";
import { toast } from "sonner";
import EmptyState from "@/components/EmptyState";
import { useLocation } from "react-router-dom";

const SpecialtySearch = () => {
  const { medicines, searchMedicines, isLoading, error } = useTunisiaMedicineSearch();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedMedicine, setSelectedMedicine] = useState<TunisiaMedicine | null>(null);
  const location = useLocation();

  // Extract query from URL if present
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const queryFromUrl = queryParams.get("q");
    
    if (queryFromUrl) {
      setSearchQuery(queryFromUrl);
      searchMedicines(queryFromUrl);
    }
  }, [location.search]);

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    if (value.trim().length >= 2) {
      searchMedicines(value);
    }
  };

  const handleSearch = () => {
    if (searchQuery.trim().length >= 2) {
      searchMedicines(searchQuery);
    } else if (searchQuery.trim().length > 0) {
      toast.warning("Veuillez saisir au moins 2 caractères");
    }
  };

  // Show error toast when error occurs
  useEffect(() => {
    if (error) {
      toast.error("Erreur de recherche", {
        description: error,
        duration: 3000,
      });
      console.error("Specialty search error:", error);
    }
  }, [error]);

  return (
    <div className="medicinet-container animate-fade-in">
      <ModernHeader
        title="Recherche de Médicaments"
        subtitle="Base de données des médicaments tunisiens"
        variant="navy"
      />
      
      <div className="mb-6">
        <SearchBar 
          value={searchQuery}
          onChange={handleSearchChange}
          onSearch={handleSearch}
          placeholder="Rechercher par nom, AMM ou laboratoire..."
        />
        <p className="text-xs text-muted-foreground mt-2">
          Tapez au moins 2 caractères pour lancer une recherche
        </p>
      </div>
      
      {isLoading && (
        <div className="flex justify-center items-center py-16">
          <Loader2 className="animate-spin text-primary" size={48} />
        </div>
      )}

      {error && !isLoading && (
        <div className="text-center text-destructive py-16">
          <p>{error}</p>
        </div>
      )}

      {!isLoading && !error && medicines.length === 0 && searchQuery.trim().length >= 2 && (
        <EmptyState
          title="Aucun résultat trouvé"
          description={`Aucun médicament ne correspond à "${searchQuery}"`}
          icon={<SearchX size={48} />}
        />
      )}

      {!isLoading && medicines.length > 0 && (
        <div>
          <p className="mb-4 text-sm text-muted-foreground">
            {medicines.length} résultat{medicines.length !== 1 ? 's' : ''} pour "{searchQuery}"
          </p>
          <div className="space-y-4">
            {medicines.map((medicine) => (
              <SpecialtyCard
                key={medicine.id}
                specialty={medicine}
                onClick={() => setSelectedMedicine(medicine)}
              />
            ))}
          </div>
        </div>
      )}

      {selectedMedicine && (
        <SpecialtyDetails
          specialty={selectedMedicine}
          isOpen={!!selectedMedicine}
          onClose={() => setSelectedMedicine(null)}
        />
      )}
      
      <BottomNavigation />
    </div>
  );
};

export default SpecialtySearch;
